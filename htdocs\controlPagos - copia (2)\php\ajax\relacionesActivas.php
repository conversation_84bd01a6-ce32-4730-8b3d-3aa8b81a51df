<?php
include_once("../conexion.php");
include_once("../configDB.php");
include_once("../classEconomico.php");
include_once("../classOperador.php");

if (isset($_POST['myVal']) and $_POST['myVal'] == 8709011992) {

    $taxiConect = new foo_mysqli("localhost", USER, PASS, DBNAME);
    $taxiConect->query("SET NAMES utf8");

    $q = "SELECT idOperador, idEconomico, estado, fechaRegsitro,
                IF(fechaBaja IS NULL, 'ACTIVO', fechaBaja) AS Baja
    FROM operadoreconomico";
    // WHERE estado = 'A'";
    $eq = $taxiConect->query($q);

    if ($eq->num_rows != 0) {
        $i = 0;
        while ($row = $eq->fetch_array(MYSQLI_ASSOC)) {
            $idEconomico = $row['idEconomico'];
            $auto = new economico("", "", "", "", "", $idEconomico, "", 'A', "", "");
            $operador = new operador('A');
            $operador->identificador = $row['idOperador'];

            $dataAuto = $auto->dataXIdentificador($taxiConect);
            if (count($dataAuto) == 1 and $dataAuto[0] == 0) {
                $auto->estado = 'I';
                $dataAuto = $auto->dataXIdentificador($taxiConect);
            }

            $dataOperador = $operador->dataXIdentificador($taxiConect);
            if (count($dataOperador) == 1 and $dataOperador[0] == 0) {
                $operador->estado = 'I';
                $dataOperador = $operador->dataXIdentificador($taxiConect);
            }

            $aux[$i] = array(
                'auto' => $dataAuto, 'operador' => $dataOperador,
                'estadoRelacion' => $row['estado'],
                'alta' => $row['fechaRegsitro'],
                'baja' => $row['Baja']
            );
            $i++;
        }
    } else {
        $aux = array(0);
    }

    $taxiConect->close();
} else {
    $aux = array(0);
}

echo json_encode($aux);
