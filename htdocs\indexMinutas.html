<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sistema de Gestión de Minutas</title>
    <script src="https://unpkg.com/react@18.2.0/umd/react.production.min.js"></script>
    <script src="https://unpkg.com/react-dom@18.2.0/umd/react-dom.production.min.js"></script>
    <script src="https://unpkg.com/jspdf@2.5.1/dist/jspdf.umd.min.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-QWTKZyjpPEjISv5WaRU9OFeRpok6YctnYmDr5pNlyT2bRjXh0JMhjY6hW+ALEwIH" crossorigin="anonymous">
    <link href="https://cdn.jsdelivr.net/npm/flatpickr@4.6.13/dist/flatpickr.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/flatpickr@4.6.13/dist/flatpickr.min.js"></script>
    <style>
        .form-label { font-weight: bold; }
        .card { margin-bottom: 1.5rem; }
        .btn-remove { font-size: 0.875rem; }
        .flatpickr-calendar { z-index: 1050; }
        .firma-input { width: 100%; }
    </style>
</head>
<body>
    <div id="root"></div>
    <script type="text/javascript">
        const { useState, useEffect } = React;
        const { jsPDF } = window.jspdf;

        const asistentesPredefinidos = [
            { nombre: 'Maria Elena López', area: 'Gerente de Desarrollo del RUV', extensionCorreo: '<EMAIL>' },
            { nombre: 'Abraham Rojas Unda', area: 'Gerente de Administración y Finanzas del RUV', extensionCorreo: '<EMAIL>' },
            { nombre: 'Norberto Rojas Nava', area: 'Supervisor de Aplicaciones Móviles y Portal del RUV', extensionCorreo: '<EMAIL>' },
            { nombre: 'Kenya Isabel Anaya Nolasco', area: 'Enlace Especializado de Aplicaciones Móviles y Portal del RUV', extensionCorreo: '<EMAIL>' }
        ];

        const predefinedUsers = [
            'Juana Elizabeth Castro González',
            'María Elena López',
            'Cesar Amaury Aguilera Gómez',
            'José Hernán Valencia Santo',
            'Paul Enrique Tinoco Manrique'
        ];

        const predefinedLugares = [
            'Sala de junta A del RUV',
            'Sala de junta B del RUV',
            'Sala de juntas de Dirección General del RUV'
        ];

        const duracionOpciones = [
            '0.5 horas',
            '1 hora',
            '1.5 horas',
            '2 horas',
            '2.5 horas',
            '3 horas',
            '3.5 horas',
            '4 horas'
        ];

        const MinutaApp = () => {
            const [formData, setFormData] = useState({
                fechaHora: '',
                lugar: '',
                duracion: '',
                convoca: '',
                antecedentes: '',
                objetivo: '',
                relator: '',
                ordenDia: '',
                proximaReunion: '',
                asistentes: [{ nombre: '', area: '', extensionCorreo: '', firma: '', isCustom: true }],
                asuntosTratados: [''],
                acuerdos: [{ descripcion: '', responsable: '', fechaAprox: '' }]
            });

            useEffect(() => {
                const fechaHoraPicker = flatpickr('#fechaHora', {
                    enableTime: true,
                    dateFormat: 'd/m/Y H:i',
                    defaultDate: formData.fechaHora,
                    onChange: (selectedDates, dateStr) => {
                        setFormData(prev => ({ ...prev, fechaHora: dateStr }));
                    }
                });
                const proximaReunionPicker = flatpickr('#proximaReunion', {
                    enableTime: true,
                    dateFormat: 'd/m/Y H:i',
                    defaultDate: formData.proximaReunion,
                    onChange: (selectedDates, dateStr) => {
                        setFormData(prev => ({ ...prev, proximaReunion: dateStr }));
                    }
                });
                const acuerdoPickers = formData.acuerdos.map((_, index) => {
                    return flatpickr(`#fechaAprox-${index}`, {
                        enableTime: true,
                        dateFormat: 'd/m/Y H:i',
                        defaultDate: formData.acuerdos[index].fechaAprox,
                        onChange: (selectedDates, dateStr) => {
                            const updatedAcuerdos = [...formData.acuerdos];
                            updatedAcuerdos[index].fechaAprox = dateStr;
                            setFormData(prev => ({ ...prev, acuerdos: updatedAcuerdos }));
                        }
                    });
                });
                return () => {
                    fechaHoraPicker.destroy();
                    proximaReunionPicker.destroy();
                    acuerdoPickers.forEach(picker => picker.destroy());
                };
            }, [formData.acuerdos.length]);

            const handleInputChange = (e, index = null, field = null, arrayField = null) => {
                if (arrayField) {
                    const updatedArray = [...formData[arrayField]];
                    if (arrayField === 'asistentes' && field === 'nombre') {
                        const selectedAsistente = asistentesPredefinidos.find(a => a.nombre === e.target.value);
                        updatedArray[index] = {
                            ...updatedArray[index],
                            nombre: e.target.value,
                            area: selectedAsistente ? selectedAsistente.area : updatedArray[index].area,
                            extensionCorreo: selectedAsistente ? selectedAsistente.extensionCorreo : updatedArray[index].extensionCorreo,
                            isCustom: !selectedAsistente
                        };
                    } else if (arrayField === 'asuntosTratados') {
                        updatedArray[index] = e.target.value;
                    } else {
                        updatedArray[index][field] = e.target.value;
                    }
                    setFormData({ ...formData, [arrayField]: updatedArray });
                } else {
                    setFormData({ ...formData, [e.target.name]: e.target.value });
                }
            };

            const addAsistente = () => {
                setFormData({
                    ...formData,
                    asistentes: [...formData.asistentes, { nombre: '', area: '', extensionCorreo: '', firma: '', isCustom: true }]
                });
            };

            const removeAsistente = (index) => {
                setFormData({
                    ...formData,
                    asistentes: formData.asistentes.filter((_, i) => i !== index)
                });
            };

            const addAcuerdo = () => {
                setFormData({
                    ...formData,
                    acuerdos: [...formData.acuerdos, { descripcion: '', responsable: '', fechaAprox: '' }]
                });
            };

            const removeAcuerdo = (index) => {
                setFormData({
                    ...formData,
                    acuerdos: formData.acuerdos.filter((_, i) => i !== index)
                });
            };

            const addAsunto = () => {
                setFormData({
                    ...formData,
                    asuntosTratados: [...formData.asuntosTratados, '']
                });
            };

            const removeAsunto = (index) => {
                setFormData({
                    ...formData,
                    asuntosTratados: formData.asuntosTratados.filter((_, i) => i !== index)
                });
            };

            const generatePDF = () => {
                const doc = new jsPDF();
                doc.setFontSize(16);
                doc.setFont("helvetica", "bold");
                doc.text('Reunión de Trabajo', 20, 20);
                doc.setFontSize(12);

                let y = 30;
                const fields = [
                    { label: 'Fecha y Hora:', value: formData.fechaHora },
                    { label: 'Lugar:', value: formData.lugar },
                    { label: 'Duración:', value: formData.duracion },
                    { label: 'Convoca:', value: formData.convoca },
                    { label: 'Relator:', value: formData.relator }
                ];

                fields.forEach(field => {
                    doc.setFont("helvetica", "bold");
                    doc.text(field.label, 20, y);
                    doc.setFont("helvetica", "normal");
                    doc.text(field.value || '', 60, y);
                    y += 10;
                });

                const multiLineFields = [
                    { label: 'Antecedentes:', value: formData.antecedentes },
                    { label: 'Objetivo:', value: formData.objetivo },
                    { label: 'Orden del Día:', value: formData.ordenDia },
                    { label: 'Fecha Próxima Reunión:', value: formData.proximaReunion }
                ];

                multiLineFields.forEach(field => {
                    doc.setFont("helvetica", "bold");
                    doc.text(field.label, 20, y);
                    doc.setFont("helvetica", "normal");
                    const splitText = doc.splitTextToSize(field.value || '', 170);
                    doc.text(splitText, 20, y + 10);
                    y += 10 + splitText.length * 7;
                });

                doc.setFont("helvetica", "bold");
                doc.text('Asistentes de Reunión:', 20, y);
                y += 10;
                doc.setFont("helvetica", "normal");
                formData.asistentes.forEach((asistente, index) => {
                    const text = `${index + 1}. ${asistente.nombre || ''} - ${asistente.area || ''} - ${asistente.extensionCorreo || ''} - Firma: ${asistente.firma || ''}`;
                    doc.text(text, 20, y);
                    y += 10;
                });

                y += 10;
                doc.setFont("helvetica", "bold");
                doc.text('Inicio de Reunión', 20, y);
                y += 10;

                doc.setFont("helvetica", "bold");
                doc.text('Asuntos Tratados:', 20, y);
                doc.setFont("helvetica", "normal");
                const asuntosText = formData.asuntosTratados
                    .map((asunto, index) => asunto ? `${index + 1}. ${asunto}` : '')
                    .filter(a => a)
                    .join('\n');
                const asuntosSplit = doc.splitTextToSize(asuntosText || '', 170);
                doc.text(asuntosSplit, 20, y + 10);
                y += 10 + asuntosSplit.length * 7;

                doc.setFont("helvetica", "bold");
                doc.text('Acuerdos:', 20, y);
                y += 10;
                formData.acuerdos.forEach((acuerdo, index) => {
                    doc.setFont("helvetica", "normal");
                    const descSplit = doc.splitTextToSize(`${index + 1}. ${acuerdo.descripcion || ''}`, 170);
                    doc.text(descSplit, 20, y);
                    y += descSplit.length * 7;
                    doc.setFont("helvetica", "bold");
                    doc.text('Responsable:', 20, y);
                    doc.setFont("helvetica", "normal");
                    doc.text(acuerdo.responsable || '', 60, y);
                    y += 10;
                    doc.setFont("helvetica", "bold");
                    doc.text('Fecha Aprox.:', 20, y);
                    doc.setFont("helvetica", "normal");
                    doc.text(acuerdo.fechaAprox || '', 60, y);
                    y += 10;
                });

                doc.save('minuta.pdf');
            };

            const generateWord = () => {
                const htmlContent = `\uFEFF<html>
                    <head><meta charset="UTF-8"></head>
                    <body style="font-family:Arial,sans-serif;font-size:12pt;">
                        <h1 style="font-size:16pt;text-align:center;">Reunión de Trabajo</h1>
                        <p><b>Fecha y Hora:</b> ${formData.fechaHora || ''}</p>
                        <p><b>Lugar:</b> ${formData.lugar || ''}</p>
                        <p><b>Duración:</b> ${formData.duracion || ''}</p>
                        <p><b>Convoca:</b> ${formData.convoca || ''}</p>
                        <p><b>Antecedentes:</b> ${formData.antecedentes.replace(/\n/g, '<br>') || ''}</p>
                        <p><b>Objetivo:</b> ${formData.objetivo.replace(/\n/g, '<br>') || ''}</p>
                        <p><b>Relator:</b> ${formData.relator || ''}</p>
                        <p style="text-align:center;"><b>Orden del Día:</b><br>${formData.ordenDia.replace(/\n/g, '<br>') || ''}</p>
                        <p><b>Fecha Próxima Reunión:</b> ${formData.proximaReunion || ''}</p>
                        <h2 style="font-size:14pt;text-align:center;">Asistentes de Reunión</h2>
                        <table border="1" style="border-collapse:collapse;width:100%;font-size:10pt;">
                            <tr>
                                <th style="width:25%;">Nombre</th>
                                <th style="width:25%;">Área</th>
                                <th style="width:25%;">Extensión y Correo</th>
                                <th style="width:25%;">Firma</th>
                            </tr>
                            ${formData.asistentes.map(a => `
                                <tr>
                                    <td>${a.nombre || ''}</td>
                                    <td>${a.area || ''}</td>
                                    <td>${a.extensionCorreo || ''}</td>
                                    <td>${a.firma || ''}</td>
                                </tr>
                            `).join('')}
                        </table>
                        <h2 style="font-size:14pt;text-align:left;">Inicio de Reunión</h2>
                        <p style="text-align:center;"><b>Asuntos Tratados:</b><br>${formData.asuntosTratados
                            .map((asunto, index) => asunto ? `${index + 1}. ${asunto.replace(/\n/g, '<br>')}` : '')
                            .filter(a => a)
                            .join('<br>') || ''}</p>
                        <h2 style="font-size:14pt;text-align:center;">Acuerdos</h2>
                        ${formData.acuerdos.map((a, i) => `
                            <p>${i + 1}. ${a.descripcion.replace(/\n/g, '<br>') || ''}<br>
                            <b>Responsable:</b> ${a.responsable || ''}<br>
                            <b>Fecha Aprox.:</b> ${a.fechaAprox || ''}</p>
                        `).join('')}
                    </body>
                </html>`;
                const blob = new Blob([htmlContent], { type: 'application/msword' });
                const link = document.createElement('a');
                link.href = URL.createObjectURL(blob);
                link.download = 'minuta.doc';
                link.click();
            };

            return React.createElement(
                'div',
                { className: 'container my-4' },
                React.createElement(
                    'h1',
                    { className: 'text-center mb-4 display-4' },
                    'Generador de Minutas'
                ),
                React.createElement(
                    'div',
                    { className: 'card shadow' },
                    React.createElement(
                        'div',
                        { className: 'card-body' },
                        [
                            { label: 'Fecha y Hora', name: 'fechaHora', type: 'input', placeholder: 'Seleccione fecha y hora...', id: 'fechaHora' },
                            { label: 'Lugar', name: 'lugar', type: 'input', placeholder: 'Seleccione o escriba un lugar...', datalistId: 'lugares' },
                            { label: 'Duración', name: 'duracion', type: 'select', placeholder: 'Seleccione duración...' },
                            { label: 'Convoca', name: 'convoca', type: 'input', placeholder: 'Seleccione o escriba un usuario...', datalistId: 'users' },
                            { label: 'Antecedentes', name: 'antecedentes', type: 'textarea', placeholder: '', rows: 4 },
                            { label: 'Objetivo', name: 'objetivo', type: 'textarea', placeholder: '', rows: 4 },
                            { label: 'Relator', name: 'relator', type: 'input', placeholder: 'Seleccione o escriba un usuario...', datalistId: 'users' },
                            { label: 'Orden del Día', name: 'ordenDia', type: 'textarea', placeholder: '', rows: 6 },
                            { label: 'Fecha Próxima Reunión', name: 'proximaReunion', type: 'input', placeholder: 'Seleccione fecha y hora...', id: 'proximaReunion' }
                        ].map(field => React.createElement(
                            'div',
                            { key: field.name, className: 'mb-3' },
                            React.createElement(
                                'label',
                                { className: 'form-label' },
                                field.label
                            ),
                            field.type === 'select' ? React.createElement(
                                'select',
                                {
                                    name: field.name,
                                    value: formData[field.name],
                                    onChange: handleInputChange,
                                    className: 'form-select'
                                },
                                React.createElement('option', { value: '' }, field.placeholder),
                                duracionOpciones.map((option, i) => React.createElement(
                                    'option',
                                    { key: i, value: option },
                                    option
                                ))
                            ) : React.createElement(
                                field.type === 'input' ? 'input' : 'textarea',
                                {
                                    name: field.name,
                                    id: field.id,
                                    value: formData[field.name],
                                    onChange: handleInputChange,
                                    className: 'form-control',
                                    placeholder: field.placeholder,
                                    rows: field.rows,
                                    list: field.datalistId
                                }
                            ),
                            field.datalistId && React.createElement(
                                'datalist',
                                { id: field.datalistId },
                                (field.datalistId === 'lugares' ? predefinedLugares : predefinedUsers).map((option, i) => React.createElement(
                                    'option',
                                    { key: i, value: option }
                                ))
                            )
                        )),
                        React.createElement(
                            'div',
                            { className: 'mb-4' },
                            React.createElement(
                                'h2',
                                { className: 'h4 mb-3 text-center' },
                                'Asistentes de Reunión'
                            ),
                            formData.asistentes.map((asistente, index) => React.createElement(
                                'div',
                                { key: index, className: 'card mb-3' },
                                React.createElement(
                                    'div',
                                    { className: 'card-body' },
                                    React.createElement(
                                        'div',
                                        { className: 'row g-3' },
                                        [
                                            {
                                                label: 'Nombre',
                                                type: 'input',
                                                value: asistente.nombre,
                                                onChange: e => handleInputChange(e, index, 'nombre', 'asistentes'),
                                                placeholder: 'Seleccione o escriba un nombre...',
                                                datalistId: `asistentes-${index}`
                                            },
                                            {
                                                label: 'Área',
                                                type: 'input',
                                                value: asistente.area,
                                                onChange: e => handleInputChange(e, index, 'area', 'asistentes'),
                                                readOnly: !asistente.isCustom
                                            },
                                            {
                                                label: 'Extensión y Correo',
                                                type: 'input',
                                                value: asistente.extensionCorreo,
                                                onChange: e => handleInputChange(e, index, 'extensionCorreo', 'asistentes'),
                                                readOnly: !asistente.isCustom
                                            },
                                            {
                                                label: 'Firma',
                                                type: 'input',
                                                value: asistente.firma,
                                                onChange: e => handleInputChange(e, index, 'firma', 'asistentes'),
                                                placeholder: '',
                                                className: 'firma-input'
                                            }
                                        ].map((field, i) => React.createElement(
                                            'div',
                                            { key: i, className: field.label === 'Firma' ? 'col-md-12' : 'col-md-6' },
                                            React.createElement(
                                                'label',
                                                { className: 'form-label' },
                                                field.label
                                            ),
                                            React.createElement(
                                                'input',
                                                {
                                                    type: 'text',
                                                    value: field.value,
                                                    onChange: field.onChange,
                                                    readOnly: field.readOnly,
                                                    className: `form-control ${field.readOnly ? 'bg-light' : ''} ${field.className || ''}`,
                                                    placeholder: field.placeholder,
                                                    list: field.datalistId
                                                }
                                            ),
                                            field.datalistId && React.createElement(
                                                'datalist',
                                                { id: field.datalistId },
                                                asistentesPredefinidos.map((a, j) => React.createElement(
                                                    'option',
                                                    { key: j, value: a.nombre }
                                                ))
                                            )
                                        ))
                                    ),
                                    React.createElement(
                                        'button',
                                        {
                                            onClick: () => removeAsistente(index),
                                            className: 'btn btn-link text-danger mt-2'
                                        },
                                        'Eliminar Asistente'
                                    )
                                )
                            )),
                            React.createElement(
                                'button',
                                {
                                    onClick: addAsistente,
                                    className: 'btn btn-primary mt-2'
                                },
                                'Agregar Asistente'
                            )
                        ),
                        React.createElement(
                            'h2',
                            { className: 'h4 mb-3 text-start' },
                            'Inicio de Reunión'
                        ),
                        React.createElement(
                            'div',
                            { className: 'mb-4' },
                            React.createElement(
                                'h2',
                                { className: 'h4 mb-3 text-center' },
                                'Asuntos Tratados'
                            ),
                            formData.asuntosTratados.map((asunto, index) => React.createElement(
                                'div',
                                { key: index, className: 'card mb-3' },
                                React.createElement(
                                    'div',
                                    { className: 'card-body' },
                                    React.createElement(
                                        'div',
                                        { className: 'mb-3' },
                                        React.createElement(
                                            'label',
                                            { className: 'form-label' },
                                            `Asunto ${index + 1}`
                                        ),
                                        React.createElement(
                                            'textarea',
                                            {
                                                value: asunto,
                                                onChange: e => handleInputChange(e, index, null, 'asuntosTratados'),
                                                className: 'form-control',
                                                placeholder: '',
                                                rows: 4
                                            }
                                        )
                                    ),
                                    formData.asuntosTratados.length > 1 && React.createElement(
                                        'button',
                                        {
                                            onClick: () => removeAsunto(index),
                                            className: 'btn btn-link text-danger mt-2'
                                        },
                                        'Eliminar Asunto'
                                    )
                                )
                            )),
                            React.createElement(
                                'button',
                                {
                                    onClick: addAsunto,
                                    className: 'btn btn-primary mt-2'
                                },
                                'Agregar Asunto'
                            )
                        ),
                        React.createElement(
                            'div',
                            { className: 'mb-4' },
                            React.createElement(
                                'h2',
                                { className: 'h4 mb-3 text-center' },
                                'Acuerdos'
                            ),
                            formData.acuerdos.map((acuerdo, index) => React.createElement(
                                'div',
                                { key: index, className: 'card mb-3' },
                                React.createElement(
                                    'div',
                                    { className: 'card-body' },
                                    [
                                        {
                                            label: 'Descripción',
                                            name: 'descripcion',
                                            type: 'textarea',
                                            value: acuerdo.descripcion,
                                            placeholder: '',
                                            rows: 4
                                        },
                                        {
                                            label: 'Responsable',
                                            name: 'responsable',
                                            type: 'input',
                                            value: acuerdo.responsable,
                                            placeholder: 'Seleccione o escriba un nombre...',
                                            datalistId: `responsable-${index}`
                                        },
                                        {
                                            label: 'Fecha Aproximada',
                                            name: 'fechaAprox',
                                            type: 'input',
                                            value: acuerdo.fechaAprox,
                                            placeholder: 'Seleccione fecha y hora...',
                                            id: `fechaAprox-${index}`
                                        }
                                    ].map((field, i) => React.createElement(
                                        'div',
                                        { key: i, className: 'mb-3' },
                                        React.createElement(
                                            'label',
                                            { className: 'form-label' },
                                            field.label
                                        ),
                                        React.createElement(
                                            field.type === 'input' ? 'input' : 'textarea',
                                            {
                                                name: field.name,
                                                id: field.id,
                                                value: field.value,
                                                onChange: e => handleInputChange(e, index, field.name, 'acuerdos'),
                                                className: 'form-control',
                                                placeholder: field.placeholder,
                                                rows: field.rows,
                                                list: field.datalistId
                                            }
                                        ),
                                        field.datalistId && React.createElement(
                                            'datalist',
                                            { id: field.datalistId },
                                            predefinedUsers.map((user, j) => React.createElement(
                                                'option',
                                                { key: j, value: user }
                                            ))
                                        )
                                    )),
                                    React.createElement(
                                        'button',
                                        {
                                            onClick: () => removeAcuerdo(index),
                                            className: 'btn btn-link text-danger mt-2'
                                        },
                                        'Eliminar Acuerdo'
                                    )
                                )
                            )),
                            React.createElement(
                                'button',
                                {
                                    onClick: addAcuerdo,
                                    className: 'btn btn-primary mt-2'
                                },
                                'Agregar Acuerdo'
                            )
                        ),
                        React.createElement(
                            'div',
                            { className: 'd-flex gap-2' },
                            React.createElement(
                                'button',
                                {
                                    onClick: generatePDF,
                                    className: 'btn btn-success'
                                },
                                'Exportar a PDF'
                            ),
                            React.createElement(
                                'button',
                                {
                                    onClick: generateWord,
                                    className: 'btn btn-primary'
                                },
                                'Exportar a Word'
                            )
                        )
                    )
                )
            );
        };

        const root = ReactDOM.createRoot(document.getElementById('root'));
        root.render(React.createElement(MinutaApp));
    </script>
</body>
</html>