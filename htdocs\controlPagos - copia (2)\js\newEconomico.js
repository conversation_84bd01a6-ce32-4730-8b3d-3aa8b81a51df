$(document).ready(function () {
  if ($.get("resp")) {
    resp = $.get("resp");
    switch (resp) {
      case "0":
        msg = "Error al guardar, intente nuevamente.";
        break;
      case "1":
        msg = "Nuevo Economico guardado satisfacctoriamente";
        break;
    }
    alertify.alert("Servitaxis Sitio 152", msg);
  }

  $(
    "#marca,#modelo,#placa,#aseguradora,#niv,#propietarioAuto,#motor"
  ).cambiaMayus();

  $("#owner").miCatalogo("ownerlist", "Catalogo propietario");

  $("#vigenciaBegin")
    .datepicker({
      dateFormat: "yy-mm-dd",
      defaultDate: "-6m",
      changeMonth: true,
      changeYear: true,
      numberOfMonths: 1,
    })
    .change(function () {
      $("#vigenciaEnd").datepicker("option", "minDate", $(this).val());
    });
  $("#vigenciaEnd")
    .datepicker({
      dateFormat: "yy-mm-dd",
      defaultDate: "+3m",
      changeMonth: true,
      changeYear: true,
      numberOfMonths: 1,
    })
    .change(function () {
      $("#vigenciaBegin").datepicker("option", "maxDate", $(this).val());
    });

  $("#formEconomico").validate({
    rules: {
      placa: {
        remote: "php/ajax/validaVariable.php",
      },
      economico: {
        remote: "php/ajax/validaVariable.php",
      },
      vigenciaEnd: {
        fechaMayor: true,
      },
    },
    messages: {
      placa: {
        remote: "Placa asignada a economico activo.",
      },
      economico: {
        remote: "Económico asignado a otro auto.",
      },
    },
  });
});
