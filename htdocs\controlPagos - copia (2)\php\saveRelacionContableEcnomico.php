<?php
include_once("conexion.php");
include_once("configDB.php");
include_once("classProcesaVariable.php");
include_once("classEconomico.php");
include_once("classOperador.php");

if (isset($_POST['contable0']) and isset($_POST['economico0'])) {

    $economico = new procesaVariable($_POST['economico0']);
    $contable = new procesaVariable($_POST['contable0']);

    $taxiConect = new foo_mysqli("localhost", USER, PASS, DBNAME);
    $taxiConect->query("SET NAMES utf8");

    $driver = new mysqli_driver();
    $driver->report_mode = MYSQLI_REPORT_STRICT | MYSQLI_REPORT_ERROR;

    $var = 0;

    $idEconomico = $economico->valor;
    $idOperador = $contable->valor;
    $insert = "INSERT INTO operadoreconomico(idOperador, idEconomico, estado) 
        VALUES ('$idOperador','$idEconomico','A')
        ON DUPLICATE KEY UPDATE estado = 'A'";
    try {
        $var = 1;
        $exeInsert = $taxiConect->query($insert);
    } catch (mysqli_sql_exception $e) {
        $var = $e->getCode();
    }
    // }
    echo $taxiConect->error;
    $taxiConect->close();
} else {
    $var = '0';
}

header("Location: ../operadorEconomico.php?res=$var");
