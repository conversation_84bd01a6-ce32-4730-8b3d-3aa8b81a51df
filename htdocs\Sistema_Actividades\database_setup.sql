-- =====================================================
-- SISTEMA DE ACTIVIDADES - ESTRUCTURA DE BASE DE DATOS
-- Base de datos: minutas_db
-- =====================================================

USE minutas_db;

-- Tabla principal de actividades
CREATE TABLE IF NOT EXISTS actividades (
    id INT AUTO_INCREMENT PRIMARY KEY,
    actividad_id VARCHAR(50) UNIQUE NOT NULL,
    tarea TEXT NOT NULL,
    tema VARCHAR(100) NOT NULL,
    prioridad ENUM('Alta', 'Media', 'Baja') NOT NULL,
    fecha_hora_compromiso DATETIME NOT NULL,
    responsable VARCHAR(255) NOT NULL,
    progreso ENUM('Vigente', 'En Progreso', 'Vencido', 'Completado') DEFAULT 'Vigente',
    comentarios TEXT,
    fecha_creacion TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    fecha_modificacion TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_actividad_id (actividad_id),
    INDEX idx_responsable (responsable),
    INDEX idx_progreso (progreso),
    INDEX idx_fecha_compromiso (fecha_hora_compromiso),
    INDEX idx_tema (tema),
    INDEX idx_prioridad (prioridad)
);

-- Tabla de temas predefinidos (catálogo)
CREATE TABLE IF NOT EXISTS actividades_temas (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nombre VARCHAR(100) UNIQUE NOT NULL,
    descripcion TEXT,
    activo BOOLEAN DEFAULT TRUE,
    fecha_creacion TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Insertar temas predefinidos
INSERT INTO actividades_temas (nombre, descripcion) VALUES
('Desarrollo de Software', 'Actividades relacionadas con programación y desarrollo'),
('Reuniones', 'Juntas, reuniones y presentaciones'),
('Documentación', 'Creación y actualización de documentos'),
('Capacitación', 'Entrenamientos y cursos'),
('Soporte Técnico', 'Atención a usuarios y resolución de problemas'),
('Planificación', 'Actividades de planeación y estrategia'),
('Revisión y Testing', 'Pruebas y revisiones de calidad'),
('Mantenimiento', 'Actividades de mantenimiento preventivo y correctivo'),
('Investigación', 'Investigación y análisis'),
('Administración', 'Tareas administrativas y gestión'),
('Comunicación', 'Actividades de comunicación interna y externa'),
('Otros', 'Actividades que no encajan en otras categorías');

-- Tabla de historial de cambios (auditoría)
CREATE TABLE IF NOT EXISTS actividades_historial (
    id INT AUTO_INCREMENT PRIMARY KEY,
    actividad_id VARCHAR(50) NOT NULL,
    campo_modificado VARCHAR(50) NOT NULL,
    valor_anterior TEXT,
    valor_nuevo TEXT,
    usuario_modificacion VARCHAR(255),
    fecha_modificacion TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_actividad_id (actividad_id),
    INDEX idx_fecha (fecha_modificacion),
    FOREIGN KEY (actividad_id) REFERENCES actividades(actividad_id) ON DELETE CASCADE
);

-- Vista para consultas optimizadas
CREATE OR REPLACE VIEW vista_actividades AS
SELECT 
    a.id,
    a.actividad_id,
    a.tarea,
    a.tema,
    a.prioridad,
    a.fecha_hora_compromiso,
    a.responsable,
    a.progreso,
    a.comentarios,
    a.fecha_creacion,
    a.fecha_modificacion,
    CASE 
        WHEN a.progreso = 'Completado' THEN 'Completado'
        WHEN a.progreso = 'En Progreso' THEN 'En Progreso'
        WHEN a.fecha_hora_compromiso < NOW() AND a.progreso = 'Vigente' THEN 'Vencido'
        ELSE a.progreso
    END AS estado_calculado,
    DATEDIFF(a.fecha_hora_compromiso, NOW()) as dias_restantes
FROM actividades a
ORDER BY 
    CASE a.prioridad 
        WHEN 'Alta' THEN 1 
        WHEN 'Media' THEN 2 
        WHEN 'Baja' THEN 3 
    END,
    a.fecha_hora_compromiso ASC;

-- Procedimiento para actualizar estados automáticamente
DELIMITER //
CREATE OR REPLACE PROCEDURE ActualizarEstadosActividades()
BEGIN
    -- Actualizar actividades vencidas
    UPDATE actividades 
    SET progreso = 'Vencido' 
    WHERE fecha_hora_compromiso < NOW() 
    AND progreso = 'Vigente';
    
    -- Registrar en historial
    INSERT INTO actividades_historial (actividad_id, campo_modificado, valor_anterior, valor_nuevo, usuario_modificacion)
    SELECT actividad_id, 'progreso', 'Vigente', 'Vencido', 'SISTEMA_AUTO'
    FROM actividades 
    WHERE fecha_hora_compromiso < NOW() 
    AND progreso = 'Vencido'
    AND actividad_id NOT IN (
        SELECT DISTINCT actividad_id 
        FROM actividades_historial 
        WHERE campo_modificado = 'progreso' 
        AND valor_nuevo = 'Vencido'
        AND DATE(fecha_modificacion) = CURDATE()
    );
END //
DELIMITER ;

-- Evento para ejecutar la actualización automática cada hora
CREATE EVENT IF NOT EXISTS evento_actualizar_estados
ON SCHEDULE EVERY 1 HOUR
DO CALL ActualizarEstadosActividades();

-- Mostrar estructura creada
SHOW TABLES LIKE '%actividades%';
SELECT 'Base de datos del Sistema de Actividades creada exitosamente' as mensaje;
