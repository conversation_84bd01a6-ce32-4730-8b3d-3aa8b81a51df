<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test API Acuerdos</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container my-4">
        <h2>Test API - Repositorio de Acuerdos</h2>
        
        <button onclick="testAPI()" class="btn btn-primary mb-3">Probar API de Acuerdos</button>
        
        <div id="result"></div>
    </div>

    <script>
        async function testAPI() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<div class="spinner-border" role="status"></div> Cargando...';
            
            try {
                const response = await fetch('api/minutas.php?action=acuerdos&limit=10');
                const data = await response.json();
                
                console.log('Respuesta completa:', data);
                
                if (data.success) {
                    let html = `
                        <div class="alert alert-success">
                            <h5>✅ API funciona correctamente</h5>
                            <p>Total de acuerdos: ${data.data.length}</p>
                        </div>
                        
                        <h4>Datos recibidos:</h4>
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>ID Minuta</th>
                                        <th>Descripción</th>
                                        <th>Responsable</th>
                                        <th>Fecha Compromiso</th>
                                        <th>Fecha Minuta</th>
                                        <th>Lugar</th>
                                        <th>Convoca</th>
                                    </tr>
                                </thead>
                                <tbody>
                    `;
                    
                    data.data.forEach((acuerdo, index) => {
                        html += `
                            <tr>
                                <td>${index + 1}</td>
                                <td>${acuerdo.minuta_id || 'NULL'}</td>
                                <td>${acuerdo.descripcion || 'NULL'}</td>
                                <td>${acuerdo.responsable || 'NULL'}</td>
                                <td>${acuerdo.fecha_aproximada || 'NULL'}</td>
                                <td>${acuerdo.fecha_minuta || 'NULL'}</td>
                                <td>${acuerdo.lugar || 'NULL'}</td>
                                <td>${acuerdo.convoca || 'NULL'}</td>
                            </tr>
                        `;
                    });
                    
                    html += `
                                </tbody>
                            </table>
                        </div>
                        
                        <h4>JSON Raw:</h4>
                        <pre class="bg-light p-3" style="max-height: 400px; overflow-y: auto;">
${JSON.stringify(data, null, 2)}
                        </pre>
                    `;
                    
                    resultDiv.innerHTML = html;
                } else {
                    resultDiv.innerHTML = `
                        <div class="alert alert-danger">
                            <h5>❌ Error en API</h5>
                            <p>${data.error || 'Error desconocido'}</p>
                        </div>
                    `;
                }
            } catch (error) {
                console.error('Error:', error);
                resultDiv.innerHTML = `
                    <div class="alert alert-danger">
                        <h5>❌ Error de conexión</h5>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }
    </script>
</body>
</html>
