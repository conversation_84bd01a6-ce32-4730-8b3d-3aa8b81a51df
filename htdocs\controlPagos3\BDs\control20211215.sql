-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 4.7.9
-- https://www.phpmyadmin.net/
--
-- Servidor: localhost
-- Tiempo de generación: 15-12-2021 a las 21:24:53
-- Versión del servidor: 8.0.27
-- Versión de PHP: 5.6.40-55+ubuntu21.04.1+deb.sury.org+1

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
SET AUTOCOMMIT = 0;
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Base de datos: `control`
--

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `abono`
--

CREATE TABLE `abono` (
  `idAbono` int NOT NULL,
  `idUsuario` int NOT NULL,
  `tipoUsuario` enum('A','C','J') NOT NULL,
  `idOperador` int NOT NULL,
  `idImporte` int NOT NULL,
  `idRecibo` char(10) NOT NULL,
  `semana` int NOT NULL,
  `anio` char(4) NOT NULL,
  `tipoAbono` enum('A','C','V') DEFAULT NULL COMMENT 'abono,condonacion,convenio'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;

--
-- Volcado de datos para la tabla `abono`
--

INSERT INTO `abono` (`idAbono`, `idUsuario`, `tipoUsuario`, `idOperador`, `idImporte`, `idRecibo`, `semana`, `anio`, `tipoAbono`) VALUES
(1, 1, 'C', 1, 5, '2021070001', 30, '2021', 'A'),
(2, 1, 'C', 1, 5, '2021080001', 31, '2021', 'A'),
(3, 1, 'C', 1, 5, '2021100001', 32, '2021', 'A'),
(4, 1, 'C', 1, 6, '2021100001', 40, '2021', 'A'),
(5, 1, 'C', 1, 7, '2021100001', 40, '2021', 'C'),
(6, 1, 'C', 1, 3, '2021100001', 40, '2021', 'A'),
(10, 1, 'C', 1, 5, '2021110001', 33, '2021', 'A'),
(11, 1, 'C', 1, 5, '2021110002', 34, '2021', 'A'),
(12, 1, 'C', 1, 5, '2021110003', 35, '2021', 'A'),
(13, 1, 'C', 1, 6, '2021110003', 41, '2021', 'A'),
(14, 1, 'C', 1, 5, '2021110003', 36, '2021', 'A'),
(15, 1, 'C', 4, 5, '2021110004', 45, '2021', 'A'),
(16, 1, 'C', 4, 6, '2021110004', 45, '2021', 'A'),
(17, 1, 'C', 4, 5, '2021110005', 46, '2021', 'A'),
(18, 1, 'C', 1, 5, '2021110006', 37, '2021', 'V'),
(19, 1, 'C', 1, 5, '2021110006', 38, '2021', 'V'),
(20, 1, 'C', 1, 5, '2021110006', 39, '2021', 'V'),
(21, 1, 'C', 1, 5, '2021110006', 40, '2021', 'V'),
(22, 1, 'C', 1, 5, '2021110006', 41, '2021', 'V'),
(23, 1, 'C', 1, 5, '2021110006', 42, '2021', 'V'),
(24, 1, 'C', 1, 5, '2021110006', 43, '2021', 'V'),
(25, 1, 'C', 1, 5, '2021110006', 44, '2021', 'V'),
(26, 1, 'C', 1, 6, '2021110006', 42, '2021', 'V'),
(27, 1, 'C', 1, 6, '2021110006', 43, '2021', 'V'),
(28, 1, 'C', 1, 6, '2021110006', 44, '2021', 'V'),
(29, 1, 'C', 1, 6, '2021110006', 45, '2021', 'V'),
(30, 1, 'C', 1, 7, '2021110006', 41, '2021', 'V'),
(31, 1, 'C', 1, 7, '2021110006', 42, '2021', 'V'),
(32, 1, 'C', 1, 7, '2021110006', 43, '2021', 'V'),
(33, 1, 'C', 1, 7, '2021110006', 44, '2021', 'V'),
(34, 1, 'C', 5, 5, '2021110007', 20, '2021', 'A'),
(35, 1, 'C', 5, 5, '2021110008', 21, '2021', 'V'),
(36, 1, 'C', 5, 5, '2021110008', 22, '2021', 'V'),
(37, 1, 'C', 5, 5, '2021110008', 23, '2021', 'V'),
(38, 1, 'C', 5, 5, '2021110008', 24, '2021', 'V'),
(39, 1, 'C', 5, 5, '2021110008', 25, '2021', 'V'),
(40, 1, 'C', 5, 5, '2021110008', 26, '2021', 'V'),
(41, 1, 'C', 5, 5, '2021110008', 27, '2021', 'V'),
(42, 1, 'C', 5, 5, '2021110008', 28, '2021', 'V'),
(43, 1, 'C', 5, 5, '2021110008', 29, '2021', 'V'),
(44, 1, 'C', 5, 5, '2021110008', 30, '2021', 'V'),
(45, 1, 'C', 5, 5, '2021110009', 31, '2021', 'V'),
(46, 1, 'C', 5, 5, '2021110009', 32, '2021', 'V'),
(47, 1, 'C', 5, 5, '2021110009', 33, '2021', 'V'),
(48, 1, 'C', 5, 5, '2021110009', 34, '2021', 'V'),
(49, 1, 'C', 5, 5, '2021110009', 35, '2021', 'V'),
(50, 3, 'J', 1, 5, '2021110010', 45, '2021', 'A'),
(51, 3, 'J', 1, 6, '2021110010', 46, '2021', 'A'),
(52, 5, 'A', 1, 5, '2021120001', 46, '2021', 'A'),
(53, 1, 'C', 1, 5, '2021120002', 47, '2021', 'A'),
(54, 1, 'C', 1, 5, '2021120003', 48, '2021', 'A');

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `cargo`
--

CREATE TABLE `cargo` (
  `tipoCargo` enum('C','P') NOT NULL,
  `folio` char(10) NOT NULL COMMENT 'noCheque o folioConvenio',
  `idOperador` int NOT NULL,
  `idUsuario` int NOT NULL,
  `tipoUsuario` enum('A','C','J') NOT NULL,
  `idImporteConvenio` int DEFAULT NULL,
  `monto` decimal(8,2) NOT NULL,
  `fechaRegistro` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;

--
-- Volcado de datos para la tabla `cargo`
--

INSERT INTO `cargo` (`tipoCargo`, `folio`, `idOperador`, `idUsuario`, `tipoUsuario`, `idImporteConvenio`, `monto`) VALUES
('C', '2021110006', 1, 1, 'C', 1, '4760.00'),
('C', '2021110008', 5, 1, 'C', 1, '5000.00'),
('C', '2021110009', 5, 1, 'C', 1, '2500.00'),
('P', '0007174', 1, 1, 'C', NULL, '6300.00'),
('P', '5434535', 1, 1, 'C', NULL, '2000.00');

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `concepto`
--

CREATE TABLE `concepto` (
  `idConcepto` int NOT NULL,
  `descripcion` varchar(100) NOT NULL,
  `fechaRegistro` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=COMPACT;

--
-- Volcado de datos para la tabla `concepto`
--

INSERT INTO `concepto` (`idConcepto`, `descripcion`) VALUES
(1, 'Prueba'),
(2, 'Caseta'),
(3, 'Seguro'),
(4, 'PRUEBA 3'),
(6, 'FFGREH');

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `cuentaBancaria`
--

CREATE TABLE `cuentaBancaria` (
  `idCuentaBancaria` char(11) NOT NULL,
  `alias` varchar(15) NOT NULL,
  `estado` enum('A','I') NOT NULL,
  `fechaRegistro` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;

--
-- Volcado de datos para la tabla `cuentaBancaria`
--

INSERT INTO `cuentaBancaria` (`idCuentaBancaria`, `alias`, `estado`) VALUES
('12123235436', 'DDDDD', 'A'),
('12345678901', 'prueba', 'A'),
('30201254869', 'PRUEBAS 2', 'A'),
('56345345345', 'FDGRTH', 'A'),
('65165116516', 'RVEVERVER', 'A'),
('96385274102', 'DSGRHT', 'A');

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `economico`
--

CREATE TABLE `economico` (
  `idEconomico` char(17) NOT NULL,
  `idOwner` int NOT NULL,
  `economico` int NOT NULL,
  `marca` varchar(30) NOT NULL,
  `modelo` varchar(30) NOT NULL,
  `placa` char(6) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `anio` char(4) DEFAULT NULL,
  `estado` enum('A','I') NOT NULL DEFAULT 'A',
  `fechaRegistro` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;

--
-- Volcado de datos para la tabla `economico`
--

INSERT INTO `economico` (`idEconomico`, `idOwner`, `economico`, `marca`, `modelo`, `placa`, `anio`, `estado`) VALUES
('435BFGRTH45864666', 1, 59, 'NISSAN', 'TIDA', 'A1234C', '2018', 'A'),
('435BFGRTH45864678', 1, 125, 'VW', 'VENTO', 'A4567C', '2016', 'A'),
('435BFGRTH45864684', 1, 58, 'KIA', 'RIO', 'A4567B', '2018', 'A');

-- --------------------------------------------------------

--
-- Estructura Stand-in para la vista `fechaultimadesactivacion`
-- (Véase abajo para la vista actual)
--
CREATE TABLE `fechaultimadesactivacion` (
`idConcepto` int
,`fechaDesactivado` timestamp
);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `importe`
--

CREATE TABLE `importe` (
  `idImporte` int NOT NULL,
  `idConcepto` int NOT NULL,
  `monto` decimal(8,2) NOT NULL,
  `fechaRegistro` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `estatus` enum('A','I') NOT NULL,
  `fechaUpdate` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;

--
-- Volcado de datos para la tabla `importe`
--

INSERT INTO `importe` (`idImporte`, `idConcepto`, `monto`, `estatus`, `fechaUpdate`) VALUES
(1, 1, '100.00', 'I', '2021-10-27 22:55:24'),
(2, 1, '200.00', 'I', '2021-10-27 22:55:24'),
(3, 1, '300.00', 'I', '2021-10-27 22:55:24'),
(4, 1, '400.00', 'I', '2021-10-27 22:55:24'),
(5, 2, '500.00', 'A', NULL),
(6, 3, '100.00', 'A', NULL),
(7, 4, '90.00', 'I', '2021-10-27 23:12:09'),
(8, 1, '350.00', 'A', NULL),
(9, 1, '400.00', 'A', NULL),
(10, 1, '600.00', 'A', NULL),
(11, 4, '250.00', 'A', NULL),
(12, 4, '375.00', 'A', NULL),
(13, 6, '150.00', 'A', NULL);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `importeconvenio`
--

CREATE TABLE `importeconvenio` (
  `idImporteConvenio` int NOT NULL,
  `monto` decimal(6,2) NOT NULL,
  `fechaRegistro` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `fechaBaja` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;

--
-- Volcado de datos para la tabla `importeconvenio`
--

INSERT INTO `importeconvenio` (`idImporteConvenio`, `monto`, `fechaBaja`) VALUES
(1, '250.00', NULL);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `licencia`
--

CREATE TABLE `licencia` (
  `idLicencia` char(9) NOT NULL,
  `idOperador` int NOT NULL,
  `vigenciaInicio` date DEFAULT NULL,
  `vigenciaFin` date NOT NULL,
  `fechaRegistro` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;

--
-- Volcado de datos para la tabla `licencia`
--

INSERT INTO `licencia` (`idLicencia`, `idOperador`, `vigenciaInicio`, `vigenciaFin`) VALUES
('*********', 5, '2020-10-05', '2023-10-06'),
('*********', 4, '2020-10-08', '2021-11-13'),
('*********', 3, '2021-02-02', '2024-11-01'),
('*********', 6, '2021-10-05', '2024-11-15'),
('*********', 1, '2018-08-01', '2021-08-01');

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `login`
--

CREATE TABLE `login` (
  `idUsuario` int NOT NULL,
  `tipoUsuario` enum('A','C','J') NOT NULL COMMENT 'admin, caputrista, caja',
  `userName` varchar(16) NOT NULL,
  `passwd` char(60) NOT NULL,
  `estado` enum('A','I') NOT NULL,
  `fechaRegistro` timestamp NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;

--
-- Volcado de datos para la tabla `login`
--

INSERT INTO `login` (`idUsuario`, `tipoUsuario`, `userName`, `passwd`, `estado`) VALUES
(1, 'C', 'lupis', '$2y$10$gjBvwH9G8GnEL8iYH9INM.8x0WhQzDwdZT9ryB9ib3YaSI/7DJHOO', 'A'),
(1, 'J', 'lupe2', '$2y$10$CtB03Zxf2210wTRK.aILoORbhXIV8GSl3g1GoAkHYo4fzeumEg6qq', 'A'),
(3, 'J', 'predcel', 'predcel', 'I'),
(4, 'A', 'fencho', '$2y$10$eNZgs6sQJ96BCldfl2IgVe/FLpHXxHqBLxd4v39QY1d5lAmm5ELnO', 'A'),
(5, 'A', 'admi01', '$2y$10$W/3Re82fdh3cXtwuGlzKIuch681SBkDYpIFzLVFxNeR6cwTXz/wqS', 'A');

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `operador`
--

CREATE TABLE `operador` (
  `idOperador` int NOT NULL,
  `nombre` varchar(30) NOT NULL,
  `apellidoPaterno` varchar(30) NOT NULL,
  `apellidoMaterno` varchar(30) NOT NULL,
  `contable` char(3) NOT NULL,
  `referenciado` char(7) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `celular` char(10) NOT NULL,
  `telefono` char(10) DEFAULT NULL,
  `domicilio` varchar(100) DEFAULT NULL,
  `correo` varchar(80) NOT NULL,
  `estado` enum('A','I') NOT NULL DEFAULT 'A',
  `fechaRegistro` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;

--
-- Volcado de datos para la tabla `operador`
--

INSERT INTO `operador` (`idOperador`, `nombre`, `apellidoPaterno`, `apellidoMaterno`, `contable`, `referenciado`, `celular`, `telefono`, `domicilio`, `correo`, `estado`) VALUES
(1, 'EDCEL', 'FUERTE', 'MARTÍNEZ', '335', '234343', '5513548968', NULL, NULL, '<EMAIL>', 'A'),
(3, 'MIGUEL ANGEL ', 'GARCIA ', 'XX', '405', '7894561', '5512345678', NULL, 'CALL X #234', '<EMAIL>', 'A'),
(4, 'MIGUEL ANGEL', 'GARCIA', 'SORIA', '440', '1524407', '5512345678', NULL, NULL, '<EMAIL>', 'A'),
(5, 'RAUL', 'RODRIGUEZ', 'MARTINEZ', '123', '7894569', '5568963175', NULL, 'RGRGRGR', '<EMAIL>', 'A'),
(6, 'EFREN', 'RODRIGUEZ', 'MARTINEZ', '235', '7894566', '5512467896', NULL, NULL, '<EMAIL>', 'A');

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `operadoreconomico`
--

CREATE TABLE `operadoreconomico` (
  `idOperador` int NOT NULL,
  `idEconomico` char(17) NOT NULL,
  `fechaRegsitro` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;

-- --------------------------------------------------------

--
-- Estructura Stand-in para la vista `operadorview`
-- (Véase abajo para la vista actual)
--
CREATE TABLE `operadorview` (
`idOperador` int
,`nombreCompleto` varchar(92)
,`contable` char(3)
,`referenciado` char(7)
);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `owner`
--

CREATE TABLE `owner` (
  `idOwner` int NOT NULL,
  `nombre` varchar(30) NOT NULL,
  `apellidoPaterno` varchar(30) NOT NULL,
  `apellidoMaterno` varchar(30) NOT NULL,
  `telefono` char(10) NOT NULL,
  `correo` varchar(80) NOT NULL,
  `fechaRegistro` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;

--
-- Volcado de datos para la tabla `owner`
--

INSERT INTO `owner` (`idOwner`, `nombre`, `apellidoPaterno`, `apellidoMaterno`, `telefono`, `correo`) VALUES
(1, 'EDCEL', 'FUERTE', 'MARTINEZ', '5513548968', '<EMAIL>');

-- --------------------------------------------------------

--
-- Estructura Stand-in para la vista `ownerlist`
-- (Véase abajo para la vista actual)
--
CREATE TABLE `ownerlist` (
`idOwner` int
,`nombreCompleto` varchar(92)
);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `recibo`
--

CREATE TABLE `recibo` (
  `idRecibo` char(10) NOT NULL,
  `estado` enum('A','P') NOT NULL DEFAULT 'A' COMMENT 'adeudo, pagado',
  `efectivo` decimal(8,2) NOT NULL DEFAULT '0.00',
  `cheque` decimal(8,2) NOT NULL DEFAULT '0.00',
  `convenio` decimal(8,2) NOT NULL DEFAULT '0.00',
  `numeroCheque` varchar(10) DEFAULT NULL,
  `fechaRegistro` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `fechaPago` timestamp NULL DEFAULT NULL,
  `fechaDeposito` date DEFAULT NULL,
  `movimiento` char(9) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `idUsuario` int DEFAULT NULL COMMENT 'usuario recibió Pago',
  `tipoUsuario` enum('A','C','J') DEFAULT NULL,
  `idCuentaBancaria` char(11) DEFAULT NULL,
  `observaciones` varchar(200) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;

--
-- Volcado de datos para la tabla `recibo`
--

INSERT INTO `recibo` (`idRecibo`, `estado`, `efectivo`, `cheque`, `convenio`, `numeroCheque`, `fechaPago`, `fechaDeposito`, `movimiento`, `idUsuario`, `tipoUsuario`, `idCuentaBancaria`, `observaciones`) VALUES
('2021070001', 'P', '500.00', '0.00', '0.00', NULL, '2021-09-29 02:44:04', '2021-11-05', '123456789', NULL, NULL, NULL, NULL),
('2021080001', 'P', '0.00', '500.00', '0.00', '345345643', '2021-09-29 02:45:05', NULL, NULL, NULL, NULL, NULL, NULL),
('2021100001', 'P', '700.00', '500.00', '0.00', '595959955', '2021-10-20 01:28:39', NULL, NULL, NULL, NULL, NULL, NULL),
('2021110001', 'P', '500.00', '0.00', '0.00', NULL, '2021-11-04 06:00:00', '2021-11-04', '234543', NULL, NULL, NULL, NULL),
('2021110002', 'P', '500.00', '0.00', '0.00', NULL, '2021-11-03 06:00:00', '2021-11-03', '324535334', NULL, NULL, NULL, NULL),
('2021110003', 'P', '600.00', '600.00', '0.00', '67867', '2021-11-12 21:59:45', '2021-11-26', '464564565', NULL, NULL, NULL, NULL),
('2021110004', 'P', '0.00', '700.00', '0.00', '123', '2021-11-12 22:19:43', NULL, NULL, NULL, NULL, NULL, NULL),
('2021110005', 'A', '0.00', '0.00', '0.00', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
('2021110006', 'P', '0.00', '0.00', '4760.00', NULL, '2021-11-12 22:29:41', NULL, NULL, NULL, NULL, NULL, NULL),
('2021110007', 'P', '500.00', '0.00', '0.00', NULL, '2021-11-03 06:00:00', '2021-11-03', '1238797', NULL, NULL, NULL, NULL),
('2021110008', 'P', '0.00', '0.00', '5000.00', NULL, '2021-11-19 03:31:46', NULL, NULL, NULL, NULL, NULL, NULL),
('2021110009', 'P', '0.00', '0.00', '2500.00', NULL, '2021-11-19 03:35:52', NULL, NULL, NULL, NULL, NULL, NULL),
('2021110010', 'A', '0.00', '0.00', '0.00', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
('2021120001', 'A', '0.00', '0.00', '0.00', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
('2021120002', 'P', '500.00', '0.00', '0.00', NULL, '2021-12-15 06:00:00', '2021-12-15', '2345678', 1, 'C', '12123235436', 'xxxxxx'),
('2021120003', 'P', '500.00', '0.00', '0.00', NULL, '2021-12-15 20:52:03', NULL, NULL, 1, 'J', NULL, 'prueba 2');

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `seguro`
--

CREATE TABLE `seguro` (
  `idSeguro` int NOT NULL,
  `idEconomico` char(17) NOT NULL,
  `aseguradora` varchar(30) NOT NULL,
  `poliza` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `vigenciaInicio` date NOT NULL,
  `vigenciaFin` date NOT NULL,
  `fechaRegistro` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;

--
-- Volcado de datos para la tabla `seguro`
--

INSERT INTO `seguro` (`idSeguro`, `idEconomico`, `aseguradora`, `poliza`, `vigenciaInicio`, `vigenciaFin`) VALUES
(2, '435BFGRTH45864684', 'BANORTE', '00012456201', '2021-06-18', '2022-06-18'),
(4, '435BFGRTH45864678', 'BANORTE', '000124562047', '2021-06-01', '2022-06-01'),
(5, '435BFGRTH45864666', 'BANORTE', '00012456203', '2021-06-02', '2022-03-04');

-- --------------------------------------------------------

--
-- Estructura Stand-in para la vista `totalrecibo`
-- (Véase abajo para la vista actual)
--
CREATE TABLE `totalrecibo` (
`idRecibo` char(10)
,`Total` decimal(30,2)
,`Pago` decimal(9,2)
,`Cambio` decimal(31,2)
);

-- --------------------------------------------------------

--
-- Estructura Stand-in para la vista `userlist`
-- (Véase abajo para la vista actual)
--
CREATE TABLE `userlist` (
`idUsuario` int
,`concat(nombre,' ',apellidoPaterno,' ',apellidoMaterno)` varchar(92)
);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `usuario`
--

CREATE TABLE `usuario` (
  `idUsuario` int NOT NULL,
  `nombre` varchar(30) NOT NULL,
  `apellidoPaterno` varchar(30) NOT NULL,
  `apellidoMaterno` varchar(30) NOT NULL,
  `correo` varchar(80) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `telefono` char(10) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `fechaRegistro` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=COMPACT;

--
-- Volcado de datos para la tabla `usuario`
--

INSERT INTO `usuario` (`idUsuario`, `nombre`, `apellidoPaterno`, `apellidoMaterno`, `correo`, `telefono`) VALUES
(1, 'Lupe', 'XXXX', 'YYYY', '<EMAIL>', '5512346789'),
(3, 'EDCEL', 'FUERTE', 'MARTINEZ', '<EMAIL>', '5513548968'),
(4, 'EFREN', 'RODRIGUEZ', 'MARTINEZ', '<EMAIL>', '5555958100'),
(5, 'MIGUEL ANGEL', 'GARCIA', 'SORIA', '<EMAIL>', '5555555555');

-- --------------------------------------------------------

--
-- Estructura Stand-in para la vista `vistaCuenta`
-- (Véase abajo para la vista actual)
--
CREATE TABLE `vistaCuenta` (
`idCuentaBancaria` char(11)
,`Cuenta` varchar(27)
);

-- --------------------------------------------------------

--
-- Estructura para la vista `fechaultimadesactivacion`
--
DROP TABLE IF EXISTS `fechaultimadesactivacion`;

CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `fechaultimadesactivacion`  AS  select `i`.`idConcepto` AS `idConcepto`,max(`i`.`fechaUpdate`) AS `fechaDesactivado` from `importe` `i` where (`i`.`estatus` = 'I') group by `i`.`idConcepto` ;

-- --------------------------------------------------------

--
-- Estructura para la vista `operadorview`
--
DROP TABLE IF EXISTS `operadorview`;

CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `operadorview`  AS  select `operador`.`idOperador` AS `idOperador`,concat(`operador`.`nombre`,' ',`operador`.`apellidoPaterno`,' ',`operador`.`apellidoMaterno`) AS `nombreCompleto`,`operador`.`contable` AS `contable`,`operador`.`referenciado` AS `referenciado` from `operador` ;

-- --------------------------------------------------------

--
-- Estructura para la vista `ownerlist`
--
DROP TABLE IF EXISTS `ownerlist`;

CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `ownerlist`  AS  select `owner`.`idOwner` AS `idOwner`,concat(`owner`.`apellidoPaterno`,' ',`owner`.`apellidoMaterno`,' ',`owner`.`nombre`) AS `nombreCompleto` from `owner` order by `owner`.`apellidoPaterno` ;

-- --------------------------------------------------------

--
-- Estructura para la vista `totalrecibo`
--
DROP TABLE IF EXISTS `totalrecibo`;

CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `totalrecibo`  AS  select `r`.`idRecibo` AS `idRecibo`,sum(`i`.`monto`) AS `Total`,(`r`.`efectivo` + `r`.`cheque`) AS `Pago`,((`r`.`efectivo` + `r`.`cheque`) - sum(`i`.`monto`)) AS `Cambio` from ((`abono` `a` join `recibo` `r` on((`a`.`idRecibo` = `r`.`idRecibo`))) join `importe` `i` on((`a`.`idImporte` = `i`.`idImporte`))) where (`a`.`tipoAbono` = 'A') group by `r`.`idRecibo` ;

-- --------------------------------------------------------

--
-- Estructura para la vista `userlist`
--
DROP TABLE IF EXISTS `userlist`;

CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `userlist`  AS  select `usuario`.`idUsuario` AS `idUsuario`,concat(`usuario`.`nombre`,' ',`usuario`.`apellidoPaterno`,' ',`usuario`.`apellidoMaterno`) AS `concat(nombre,' ',apellidoPaterno,' ',apellidoMaterno)` from `usuario` ;

-- --------------------------------------------------------

--
-- Estructura para la vista `vistaCuenta`
--
DROP TABLE IF EXISTS `vistaCuenta`;

CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `vistaCuenta`  AS  select `c`.`idCuentaBancaria` AS `idCuentaBancaria`,concat(`c`.`alias`,'-',`c`.`idCuentaBancaria`) AS `Cuenta` from `cuentaBancaria` `c` ;

--
-- Índices para tablas volcadas
--

--
-- Indices de la tabla `abono`
--
ALTER TABLE `abono`
  ADD PRIMARY KEY (`idAbono`),
  ADD KEY `fkAbonoRecibo` (`idRecibo`),
  ADD KEY `fkAbonoImporte` (`idImporte`),
  ADD KEY `fkAbonoOperador` (`idOperador`),
  ADD KEY `fkAbonoLogin` (`idUsuario`,`tipoUsuario`);

--
-- Indices de la tabla `cargo`
--
ALTER TABLE `cargo`
  ADD PRIMARY KEY (`tipoCargo`,`folio`),
  ADD KEY `fkCargoImporteConvenio` (`idImporteConvenio`),
  ADD KEY `fkCargoLogin` (`idUsuario`,`tipoUsuario`),
  ADD KEY `fkCargoOperador` (`idOperador`);

--
-- Indices de la tabla `concepto`
--
ALTER TABLE `concepto`
  ADD PRIMARY KEY (`idConcepto`);

--
-- Indices de la tabla `cuentaBancaria`
--
ALTER TABLE `cuentaBancaria`
  ADD PRIMARY KEY (`idCuentaBancaria`);

--
-- Indices de la tabla `economico`
--
ALTER TABLE `economico`
  ADD PRIMARY KEY (`idEconomico`),
  ADD KEY `fkEconomicoOwner` (`idOwner`);

--
-- Indices de la tabla `importe`
--
ALTER TABLE `importe`
  ADD PRIMARY KEY (`idImporte`),
  ADD KEY `fkImporteConcepto` (`idConcepto`);

--
-- Indices de la tabla `importeconvenio`
--
ALTER TABLE `importeconvenio`
  ADD PRIMARY KEY (`idImporteConvenio`);

--
-- Indices de la tabla `licencia`
--
ALTER TABLE `licencia`
  ADD PRIMARY KEY (`idLicencia`),
  ADD KEY `fkLicenciaOperador` (`idOperador`);

--
-- Indices de la tabla `login`
--
ALTER TABLE `login`
  ADD PRIMARY KEY (`idUsuario`,`tipoUsuario`);

--
-- Indices de la tabla `operador`
--
ALTER TABLE `operador`
  ADD PRIMARY KEY (`idOperador`);

--
-- Indices de la tabla `operadoreconomico`
--
ALTER TABLE `operadoreconomico`
  ADD PRIMARY KEY (`idOperador`,`idEconomico`),
  ADD KEY `fkOperadorEconomicoEconomico` (`idEconomico`);

--
-- Indices de la tabla `owner`
--
ALTER TABLE `owner`
  ADD PRIMARY KEY (`idOwner`);

--
-- Indices de la tabla `recibo`
--
ALTER TABLE `recibo`
  ADD PRIMARY KEY (`idRecibo`),
  ADD KEY `fkReciboCuentaBancaria` (`idCuentaBancaria`),
  ADD KEY `fkReciboLogin` (`idUsuario`,`tipoUsuario`);

--
-- Indices de la tabla `seguro`
--
ALTER TABLE `seguro`
  ADD PRIMARY KEY (`idSeguro`),
  ADD KEY `fkSeguroEconomico` (`idEconomico`);

--
-- Indices de la tabla `usuario`
--
ALTER TABLE `usuario`
  ADD PRIMARY KEY (`idUsuario`);

--
-- AUTO_INCREMENT de las tablas volcadas
--

--
-- AUTO_INCREMENT de la tabla `abono`
--
ALTER TABLE `abono`
  MODIFY `idAbono` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=55;

--
-- AUTO_INCREMENT de la tabla `concepto`
--
ALTER TABLE `concepto`
  MODIFY `idConcepto` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- AUTO_INCREMENT de la tabla `importe`
--
ALTER TABLE `importe`
  MODIFY `idImporte` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=14;

--
-- AUTO_INCREMENT de la tabla `importeconvenio`
--
ALTER TABLE `importeconvenio`
  MODIFY `idImporteConvenio` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT de la tabla `operador`
--
ALTER TABLE `operador`
  MODIFY `idOperador` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- AUTO_INCREMENT de la tabla `owner`
--
ALTER TABLE `owner`
  MODIFY `idOwner` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT de la tabla `seguro`
--
ALTER TABLE `seguro`
  MODIFY `idSeguro` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT de la tabla `usuario`
--
ALTER TABLE `usuario`
  MODIFY `idUsuario` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- Restricciones para tablas volcadas
--

--
-- Filtros para la tabla `abono`
--
ALTER TABLE `abono`
  ADD CONSTRAINT `fkAbonoImporte` FOREIGN KEY (`idImporte`) REFERENCES `importe` (`idImporte`) ON UPDATE CASCADE,
  ADD CONSTRAINT `fkAbonoLogin` FOREIGN KEY (`idUsuario`,`tipoUsuario`) REFERENCES `login` (`idUsuario`, `tipoUsuario`) ON DELETE RESTRICT ON UPDATE CASCADE,
  ADD CONSTRAINT `fkAbonoOperador` FOREIGN KEY (`idOperador`) REFERENCES `operador` (`idOperador`) ON UPDATE CASCADE,
  ADD CONSTRAINT `fkAbonoRecibo` FOREIGN KEY (`idRecibo`) REFERENCES `recibo` (`idRecibo`) ON UPDATE CASCADE;

--
-- Filtros para la tabla `cargo`
--
ALTER TABLE `cargo`
  ADD CONSTRAINT `fkCargoImporteConvenio` FOREIGN KEY (`idImporteConvenio`) REFERENCES `importeconvenio` (`idImporteConvenio`) ON UPDATE CASCADE,
  ADD CONSTRAINT `fkCargoLogin` FOREIGN KEY (`idUsuario`,`tipoUsuario`) REFERENCES `login` (`idUsuario`, `tipoUsuario`) ON DELETE RESTRICT ON UPDATE CASCADE,
  ADD CONSTRAINT `fkCargoOperador` FOREIGN KEY (`idOperador`) REFERENCES `operador` (`idOperador`) ON DELETE RESTRICT ON UPDATE CASCADE;

--
-- Filtros para la tabla `economico`
--
ALTER TABLE `economico`
  ADD CONSTRAINT `fkEconomicoOwner` FOREIGN KEY (`idOwner`) REFERENCES `owner` (`idOwner`) ON UPDATE CASCADE;

--
-- Filtros para la tabla `importe`
--
ALTER TABLE `importe`
  ADD CONSTRAINT `fkImporteConcepto` FOREIGN KEY (`idConcepto`) REFERENCES `concepto` (`idConcepto`) ON UPDATE CASCADE;

--
-- Filtros para la tabla `licencia`
--
ALTER TABLE `licencia`
  ADD CONSTRAINT `fkLicenciaOperador` FOREIGN KEY (`idOperador`) REFERENCES `operador` (`idOperador`) ON UPDATE CASCADE;

--
-- Filtros para la tabla `login`
--
ALTER TABLE `login`
  ADD CONSTRAINT `fkLoginUsuario` FOREIGN KEY (`idUsuario`) REFERENCES `usuario` (`idUsuario`) ON DELETE RESTRICT ON UPDATE CASCADE;

--
-- Filtros para la tabla `operadoreconomico`
--
ALTER TABLE `operadoreconomico`
  ADD CONSTRAINT `fkOperadorEconomicoEconomico` FOREIGN KEY (`idEconomico`) REFERENCES `economico` (`idEconomico`) ON DELETE RESTRICT ON UPDATE CASCADE,
  ADD CONSTRAINT `fkOperadorEconomicoOperador` FOREIGN KEY (`idOperador`) REFERENCES `operador` (`idOperador`) ON DELETE RESTRICT ON UPDATE CASCADE;

--
-- Filtros para la tabla `recibo`
--
ALTER TABLE `recibo`
  ADD CONSTRAINT `fkReciboCuentaBancaria` FOREIGN KEY (`idCuentaBancaria`) REFERENCES `cuentaBancaria` (`idCuentaBancaria`) ON DELETE RESTRICT ON UPDATE CASCADE,
  ADD CONSTRAINT `fkReciboLogin` FOREIGN KEY (`idUsuario`,`tipoUsuario`) REFERENCES `login` (`idUsuario`, `tipoUsuario`) ON DELETE RESTRICT ON UPDATE CASCADE;

--
-- Filtros para la tabla `seguro`
--
ALTER TABLE `seguro`
  ADD CONSTRAINT `fkSeguroEconomico` FOREIGN KEY (`idEconomico`) REFERENCES `economico` (`idEconomico`) ON DELETE RESTRICT ON UPDATE CASCADE;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
