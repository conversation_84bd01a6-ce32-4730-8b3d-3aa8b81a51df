<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

// Manejar preflight requests
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit(0);
}

require_once '../config/database.php';
require_once '../models/Minuta.php';

// Obtener método HTTP y datos
$method = $_SERVER['REQUEST_METHOD'];
$input = json_decode(file_get_contents('php://input'), true);

// Inicializar base de datos y modelo
$database = new Database();
$db = $database->getConnection();
$minuta = new Minuta($db);

// Función para enviar respuesta JSON
function sendResponse($data, $status = 200) {
    http_response_code($status);
    echo json_encode($data, JSON_UNESCAPED_UNICODE);
    exit;
}

// Función para enviar error
function sendError($message, $status = 400) {
    sendResponse(['error' => $message], $status);
}

try {
    switch ($method) {
        case 'GET':
            handleGet();
            break;
        case 'POST':
            handlePost();
            break;
        default:
            sendError('Método no permitido', 405);
    }
} catch (Exception $e) {
    sendError('Error del servidor: ' . $e->getMessage(), 500);
}

/**
 * Manejar peticiones GET
 */
function handleGet() {
    global $minuta;
    
    // Obtener parámetros de la URL
    $minuta_id = isset($_GET['id']) ? $_GET['id'] : null;
    $action = isset($_GET['action']) ? $_GET['action'] : null;
    
    if ($minuta_id) {
        // Obtener minuta específica
        $result = $minuta->getById($minuta_id);
        if ($result) {
            sendResponse(['success' => true, 'data' => $result]);
        } else {
            sendError('Minuta no encontrada', 404);
        }
    } elseif ($action === 'list') {
        // Obtener lista de minutas
        $limit = isset($_GET['limit']) ? intval($_GET['limit']) : 50;
        $offset = isset($_GET['offset']) ? intval($_GET['offset']) : 0;
        
        $result = $minuta->getAll($limit, $offset);
        sendResponse(['success' => true, 'data' => $result]);
    } elseif ($action === 'generate_id') {
        // Generar nuevo ID
        $newId = Database::generateMinutaId();
        sendResponse(['success' => true, 'minuta_id' => $newId]);
    } else {
        sendError('Parámetros inválidos');
    }
}

/**
 * Manejar peticiones POST
 */
function handlePost() {
    global $minuta, $input;
    
    if (!$input) {
        sendError('Datos no válidos');
    }
    
    // Validar datos requeridos
    $required_fields = ['fechaHora', 'lugar', 'duracion', 'convoca', 'relator'];
    foreach ($required_fields as $field) {
        if (empty($input[$field])) {
            sendError("El campo '$field' es requerido");
        }
    }
    
    // Validar que tenga al menos un asistente
    if (empty($input['asistentes']) || !is_array($input['asistentes'])) {
        sendError('Debe incluir al menos un asistente');
    }
    
    // Validar que tenga al menos un asunto tratado
    if (empty($input['asuntosTratados']) || !is_array($input['asuntosTratados'])) {
        sendError('Debe incluir al menos un asunto tratado');
    }
    
    // Validar que tenga al menos un acuerdo
    if (empty($input['acuerdos']) || !is_array($input['acuerdos'])) {
        sendError('Debe incluir al menos un acuerdo');
    }
    
    try {
        $minuta_id = $minuta->create($input);
        sendResponse([
            'success' => true, 
            'message' => 'Minuta guardada exitosamente',
            'minuta_id' => $minuta_id
        ], 201);
    } catch (Exception $e) {
        sendError('Error al guardar la minuta: ' . $e->getMessage());
    }
}
?>
