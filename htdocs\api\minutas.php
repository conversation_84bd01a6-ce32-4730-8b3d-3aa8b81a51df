<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

// Manejar preflight requests
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit(0);
}

require_once '../config/database.php';
require_once '../models/Minuta.php';

// Obtener método HTTP y datos
$method = $_SERVER['REQUEST_METHOD'];
$input = json_decode(file_get_contents('php://input'), true);

// Inicializar base de datos y modelo
$database = new Database();
$db = $database->getConnection();
$minuta = new Minuta($db);

// Función para enviar respuesta JSON
function sendResponse($data, $status = 200) {
    http_response_code($status);
    echo json_encode($data, JSON_UNESCAPED_UNICODE);
    exit;
}

// Función para enviar error
function sendError($message, $status = 400) {
    sendResponse(['error' => $message], $status);
}

try {
    switch ($method) {
        case 'GET':
            handleGet();
            break;
        case 'POST':
            handlePost();
            break;
        case 'PUT':
            handlePut();
            break;
        default:
            sendError('Método no permitido', 405);
    }
} catch (Exception $e) {
    sendError('Error del servidor: ' . $e->getMessage(), 500);
}

/**
 * Manejar peticiones GET
 */
function handleGet() {
    global $minuta;

    // Obtener parámetros de la URL
    $minuta_id = isset($_GET['id']) ? $_GET['id'] : null;
    $action = isset($_GET['action']) ? $_GET['action'] : null;

    if ($minuta_id) {
        // Obtener minuta específica
        $result = $minuta->getById($minuta_id);
        if ($result) {
            sendResponse(['success' => true, 'data' => $result]);
        } else {
            sendError('Minuta no encontrada', 404);
        }
    } elseif ($action === 'list') {
        // Obtener lista de minutas
        $limit = isset($_GET['limit']) ? intval($_GET['limit']) : 50;
        $offset = isset($_GET['offset']) ? intval($_GET['offset']) : 0;

        $result = $minuta->getAll($limit, $offset);
        sendResponse(['success' => true, 'data' => $result]);
    } elseif ($action === 'generate_id') {
        // Generar nuevo ID
        $newId = Database::generateMinutaId();
        sendResponse(['success' => true, 'minuta_id' => $newId]);
    } elseif ($action === 'acuerdos') {
        // Obtener todos los acuerdos
        $limit = isset($_GET['limit']) ? intval($_GET['limit']) : 100;
        $offset = isset($_GET['offset']) ? intval($_GET['offset']) : 0;

        $result = getAllAcuerdos($limit, $offset);
        sendResponse(['success' => true, 'data' => $result]);
    } elseif ($action === 'check_duplicate') {
        // Verificar si existe una minuta duplicada
        $fecha_hora = $_GET['fecha_hora'] ?? '';
        $lugar = $_GET['lugar'] ?? '';
        $convoca = $_GET['convoca'] ?? '';
        $exclude_id = $_GET['exclude_id'] ?? '';

        $isDuplicate = checkDuplicateMinuta($fecha_hora, $lugar, $convoca, $exclude_id);
        sendResponse(['success' => true, 'is_duplicate' => $isDuplicate]);
    } else {
        sendError('Parámetros inválidos');
    }
}

/**
 * Manejar peticiones POST
 */
function handlePost() {
    global $minuta, $input;

    if (!$input) {
        sendError('Datos no válidos');
    }

    // Validar datos requeridos
    $required_fields = ['fechaHora', 'lugar', 'duracion', 'convoca', 'relator'];
    foreach ($required_fields as $field) {
        if (empty($input[$field])) {
            sendError("El campo '$field' es requerido. Valor recibido: " . json_encode($input[$field] ?? 'no definido'));
        }
    }

    // Validar formato de fecha específicamente
    if (!empty($input['fechaHora'])) {
        $testDate = DateTime::createFromFormat('d/m/Y H:i', $input['fechaHora']);
        if ($testDate === false) {
            sendError("Formato de fecha inválido. Recibido: " . $input['fechaHora'] . ". Esperado: dd/mm/yyyy hh:mm");
        }
    }

    // Validar que tenga al menos un asistente
    if (empty($input['asistentes']) || !is_array($input['asistentes'])) {
        sendError('Debe incluir al menos un asistente');
    }

    // Validar que tenga al menos un asunto tratado
    if (empty($input['asuntosTratados']) || !is_array($input['asuntosTratados'])) {
        sendError('Debe incluir al menos un asunto tratado');
    }

    // Validar que tenga al menos un acuerdo
    if (empty($input['acuerdos']) || !is_array($input['acuerdos'])) {
        sendError('Debe incluir al menos un acuerdo');
    }

    // Verificar duplicados
    $isDuplicate = checkDuplicateMinuta($input['fechaHora'], $input['lugar'], $input['convoca']);
    if ($isDuplicate) {
        sendError('Ya existe una minuta con la misma fecha, lugar y convocante. Por favor verifique los datos o modifique algún campo para evitar duplicados.');
    }

    try {
        $minuta_id = $minuta->create($input);
        sendResponse([
            'success' => true,
            'message' => 'Minuta guardada exitosamente',
            'minuta_id' => $minuta_id
        ], 201);
    } catch (Exception $e) {
        sendError('Error al guardar la minuta: ' . $e->getMessage());
    }
}

/**
 * Manejar peticiones PUT (actualizar minuta)
 */
function handlePut() {
    global $minuta, $input;

    if (!$input) {
        sendError('Datos no válidos');
    }

    // Validar que se proporcione el ID de la minuta
    if (empty($input['minuta_id'])) {
        sendError('ID de minuta requerido para actualización');
    }

    // Validar datos requeridos
    $required_fields = ['fechaHora', 'lugar', 'duracion', 'convoca', 'relator'];
    foreach ($required_fields as $field) {
        if (empty($input[$field])) {
            sendError("El campo '$field' es requerido. Valor recibido: " . json_encode($input[$field] ?? 'no definido'));
        }
    }

    // Validar formato de fecha específicamente
    if (!empty($input['fechaHora'])) {
        $testDate = DateTime::createFromFormat('d/m/Y H:i', $input['fechaHora']);
        if ($testDate === false) {
            sendError("Formato de fecha inválido. Recibido: " . $input['fechaHora'] . ". Esperado: dd/mm/yyyy hh:mm");
        }
    }

    // Validar que tenga al menos un asistente
    if (empty($input['asistentes']) || !is_array($input['asistentes'])) {
        sendError('Debe incluir al menos un asistente');
    }

    // Validar que tenga al menos un asunto tratado
    if (empty($input['asuntosTratados']) || !is_array($input['asuntosTratados'])) {
        sendError('Debe incluir al menos un asunto tratado');
    }

    // Validar que tenga al menos un acuerdo
    if (empty($input['acuerdos']) || !is_array($input['acuerdos'])) {
        sendError('Debe incluir al menos un acuerdo');
    }

    // Verificar duplicados (excluyendo la minuta actual)
    $isDuplicate = checkDuplicateMinuta($input['fechaHora'], $input['lugar'], $input['convoca'], $input['minuta_id']);
    if ($isDuplicate) {
        sendError('Ya existe otra minuta con la misma fecha, lugar y convocante. Por favor verifique los datos o modifique algún campo para evitar duplicados.');
    }

    try {
        $result = $minuta->update($input['minuta_id'], $input);
        if ($result) {
            sendResponse([
                'success' => true,
                'message' => 'Minuta actualizada exitosamente',
                'minuta_id' => $input['minuta_id']
            ]);
        } else {
            sendError('No se pudo actualizar la minuta. Verifique que el ID existe.');
        }
    } catch (Exception $e) {
        sendError('Error al actualizar la minuta: ' . $e->getMessage());
    }
}

/**
 * Obtener todos los acuerdos de todas las minutas
 */
function getAllAcuerdos($limit = 100, $offset = 0) {
    global $db;

    $query = "SELECT
                ma.descripcion,
                ma.responsable,
                ma.fecha_aproximada,
                m.minuta_id,
                m.fecha_hora as fecha_minuta,
                m.lugar,
                m.convoca
              FROM minuta_acuerdos ma
              INNER JOIN minutas m ON ma.minuta_id = m.minuta_id
              ORDER BY m.fecha_hora DESC, ma.orden_acuerdo ASC
              LIMIT :limit OFFSET :offset";

    $stmt = $db->prepare($query);
    $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
    $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
    $stmt->execute();

    $acuerdos = $stmt->fetchAll();

    // Convertir fechas al formato del frontend
    foreach ($acuerdos as &$acuerdo) {
        if ($acuerdo['fecha_aproximada']) {
            $date = DateTime::createFromFormat('Y-m-d H:i:s', $acuerdo['fecha_aproximada']);
            $acuerdo['fecha_aproximada'] = $date ? $date->format('d/m/Y H:i') : '';
        }
        if ($acuerdo['fecha_minuta']) {
            $date = DateTime::createFromFormat('Y-m-d H:i:s', $acuerdo['fecha_minuta']);
            $acuerdo['fecha_minuta'] = $date ? $date->format('d/m/Y H:i') : '';
        }
    }

    return $acuerdos;
}

/**
 * Verificar si existe una minuta duplicada
 */
function checkDuplicateMinuta($fecha_hora, $lugar, $convoca, $exclude_id = '') {
    global $db;

    // Convertir fecha al formato MySQL
    $fecha_mysql = '';
    if ($fecha_hora) {
        $date = DateTime::createFromFormat('d/m/Y H:i', $fecha_hora);
        if ($date) {
            $fecha_mysql = $date->format('Y-m-d H:i:s');
        }
    }

    $query = "SELECT COUNT(*) as count FROM minutas
              WHERE fecha_hora = :fecha_hora
              AND lugar = :lugar
              AND convoca = :convoca";

    $params = [
        ':fecha_hora' => $fecha_mysql,
        ':lugar' => $lugar,
        ':convoca' => $convoca
    ];

    // Excluir ID específico (para ediciones)
    if ($exclude_id) {
        $query .= " AND minuta_id != :exclude_id";
        $params[':exclude_id'] = $exclude_id;
    }

    $stmt = $db->prepare($query);
    $stmt->execute($params);

    $result = $stmt->fetch();
    return $result['count'] > 0;
}
?>
