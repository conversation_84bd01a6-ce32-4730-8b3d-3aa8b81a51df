<?php
include_once("conexion.php");
include_once("configDB.php");
include_once("classProcesaVariable.php");
include_once("classOwner.php");

if (isset($_POST['nombre']) and isset($_POST['correoOwner'])) {

    $nombre = new procesaVariable($_POST['nombre']);
    $aPaterno = new procesaVariable($_POST['aPaterno']);
    $aMaterno = new procesaVariable($_POST['aMaterno']);
    $telefono = new procesaVariable($_POST['telefono']);
    $correo = new procesaVariable($_POST['correoOwner']);
    $tipoPropietario = new procesaVariable($_POST['tipoPropietario']);
    $idOwner = new procesaVariable($_POST['idOwner']);

    $mailUser = strtolower($correo->valor);

    $taxiConect = new foo_mysqli("localhost", USER, PASS, DBNAME);
    $taxiConect->query("SET NAMES utf8");

    $ownerUpdate = new owner(
        $nombre->valor,
        $aPaterno->valor,
        $aMaterno->valor,
        $mailUser,
        $telefono->valor,
        $tipoPropietario->valor
    );

    $ownerUpdate->identificador = $idOwner->valor;
    if ($ownerUpdate->update($taxiConect)) {
        $var = 11;
    } else {
        $var = 0;
    }

    $taxiConect->close();
} else {
    $var = '0';
}

header("Location: ../listaOwner.php?res=$var");
