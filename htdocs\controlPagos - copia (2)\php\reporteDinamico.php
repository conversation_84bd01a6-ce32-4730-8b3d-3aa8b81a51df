<?php
include_once("conexion.php");
include_once("configDB.php");
include_once("classProcesaVariable.php");
include_once("classAbono.php");
include_once("classImporte.php");
include_once("classReporte.php");
include_once("session.php");

if (isset($_POST['anio']) and isset($_POST['semana'])) {

    $anio = new procesaVariable($_POST['anio']);
    $semana = new procesaVariable($_POST['semana']);
    $concepto = new procesaVariable($_POST['concepto']);

    $taxiConect = new foo_mysqli("localhost", USER, PASS, DBNAME);
    $taxiConect->query("SET NAMES utf8");

    $q = "SELECT	o2.nombreCompleto, o2.contable";
    foreach ($concepto->valor as $clave => $idConcepto) {
        $importeAux = new importe($idConcepto);
        $importes = $importeAux->importeXConcepto($taxiConect);
        $coneptoNombre = $importeAux->nombreConcepto($taxiConect);
        $q .= ", SUM(IF(a.idImporte IN($importes), i.monto, 0)) AS '$coneptoNombre'";
    }
    $q .= "FROM operador o 
    INNER JOIN abono a ON o.idOperador = a.idOperador
    INNER JOIN operadorview o2 ON o.idOperador = o2.idOperador
    INNER JOIN importe i ON a.idImporte = i.idImporte
    WHERE a.anio = '$anio->valor'
    AND a.semana = '$semana->valor'
    GROUP BY o.idOperador";
    // echo $q;
    $rq = $taxiConect->query($q);

    $myCsv = new Reporte("", $rq);
    $myCsv->putHeaders(true);
    $myCsv->putData(true, 2);

    $rq->free();

    $taxiConect->close();
} else {
    $link = '0';
}
