<?php
include_once("conexion.php");
include_once("configDB.php");
include_once("classUsuario.php");
$userName = $_POST['userName'];
$passwd = $_POST['passwd'];

$taxiConect = new foo_mysqli("localhost", USER, PASS, DBNAME);
$taxiConect->query("SET NAMES utf8");

$userName = $taxiConect->real_escape_string($userName);
$passwd = $taxiConect->real_escape_string($passwd);

$userLog = new usuario('x', 'y', 'z', $userName, $passwd, 1, 2, 'A', 0);
$dataUser = $userLog->dataUser($taxiConect);
$host = $_SERVER['HTTP_HOST'];
$scheme = $_SERVER['REQUEST_SCHEME'];
$base_url = "$scheme://$host";

if ($dataUser) {
	if ($dataUser['pass'] < 60) {
		if ($userLog->passDecifred($taxiConect)) {
			session_start();
			$_SESSION['idUser'] = $dataUser['idUsuario'];
			$_SESSION['userType'] = $dataUser['tipoUsuario'];
			$link = "$base_url/controlPagos/nuevoPasswd.php";
		} else {
			$link = "$base_url/controlPagos/?error=1";
		}
	} else {
		$rowPass = $userLog->passCifred($taxiConect);
		if (password_verify($passwd, $rowPass['passwd'])) {
			session_start();
			$_SESSION['idUser'] = $dataUser['idUsuario'];
			$_SESSION['userType'] = $dataUser['tipoUsuario'];
			$link = "$base_url/controlPagos/bienvenido.php";
		} else {
			$link = "$base_url/controlPagos/?error=11";
		}
	}
} else {
	$link = "$base_url/controlPagos/?error=2";
}

$taxiConect->close();

header("Location: $link");
