<?php
class economico
{

    public $owner;
    public $marca;
    public $modelo;
    public $placa;
    public $anio;
    public $economico;
    public $identificador;
    public $propietarioAuto;
    public $estado;
    public $motor;

    function __construct($ow, $ma, $mo, $pa, $year, $id, $eco, $std, $ownerCar, $mtr)
    {
        $this->owner = $ow;
        $this->marca = $ma;
        $this->modelo = $mo;
        $this->placa = $pa;
        $this->anio = $year;
        $this->identificador = $id;
        $this->economico = $eco;
        $this->estado = $std;
        $this->propietarioAuto = $ownerCar;
        $this->motor = $mtr;
    }

    public function save($conexion)
    {
        $q = "INSERT INTO economico(idEconomico, idOwner, motor, economico, marca, modelo, placa, anio, propietarioAuto, estado)
        VALUES ('$this->identificador',
        '$this->owner',
        '$this->motor',
        '$this->economico',
        '$this->marca',
        '$this->modelo',
        '$this->placa',
        '$this->anio',
        '$this->propietarioAuto',
        '$this->estado')";
        $eq = $conexion->query($q);
        if (!$eq) {
            $this->identificador = false;
        }
        return $this->identificador;
    }

    public function dataEconomico($conexion)
    {
        $q = "SELECT e.idEconomico, e.economico, e.marca, e.motor, e.modelo, e.placa, e.anio, 
		o.nombre, o.apellidoPaterno, o.apellidoMaterno, e.propietarioAuto
        FROM economico e 
        INNER JOIN owner o ON e.idOwner = o.idOwner
        WHERE e.economico = '$this->economico'
        AND e.estado = '$this->estado'";
        $eq = $conexion->query($q);
        if (!$eq) {
            $res = array(0);
        } else {
            if ($eq->num_rows != 0) {
                $res = $eq->fetch_array(MYSQLI_ASSOC);
            } else {
                $res = array(0);
            }
        }
        return $res;
    }

    public function dataXIdentificador($conexion)
    {
        $q = "SELECT e.idEconomico, e.economico, e.marca, e.modelo, e.placa, e.anio, 
		o.nombre, o.apellidoPaterno, o.apellidoMaterno 
        FROM economico e 
        INNER JOIN owner o ON e.idOwner = o.idOwner
        WHERE e.idEconomico = '$this->identificador'
        AND e.estado = '$this->estado'";
        $eq = $conexion->query($q);
        if (!$eq) {
            $res = array(0);
        } else {
            if ($eq->num_rows != 0) {
                $res = $eq->fetch_array(MYSQLI_ASSOC);
            } else {
                $res = array(0);
            }
        }
        return $res;
    }

    public function baja($conexion)
    {
        $q = "UPDATE economico SET estado = '$this->estado' 
            WHERE idEconomico = '$this->identificador';";
        $eq = $conexion->query($q);

        return $eq;
    }

    public function validaActivo($conexion)
    {
        $q = "SELECT idOperador FROM operador WHERE estado = 'A' and contable = '$this->contable'";
        $eq = $conexion->query($q);
        if ($eq->num_roww != 0) {
            $row = $eq->fetch_array(MYSQLI_ASSOC);
            $id = $row['idOperador'];
        } else {
            $id = 0;
        }
        return $id;
    }

    public function updateEconomico($conexion)
    {
        $q = "UPDATE economico 
        SET marca='$this->marca',
        modelo='$this->modelo',
        placa='$this->placa',
        anio='$this->anio',
        propietarioAuto='$this->propietarioAuto'
        WHERE idEconomico = '$this->identificador'";
        $eq = $conexion->query($q);
        if (!$eq) {
            $this->identificador = false;
        }

        return $this->identificador;
    }

    public function economicosList($conexion)
    {
        $q = "SELECT	e.idEconomico, e.Propietario, e.motor, 
		        e.economico, e.marca, e.modelo, e.placa, e.anio, 
		        e.propietarioAuto, e.`Status`,
		IF(s.vigenciaFin IS NULL, 'Caducado',s.vigenciaFin) AS vigenciaSeguro
        FROM economicopropietario e 
        LEFT JOIN segurovigente s ON e.idEconomico = s.idEconomico;";
        $eq = $conexion->query($q);

        return $eq;
    }
}
