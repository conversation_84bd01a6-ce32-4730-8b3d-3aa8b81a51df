<?php

use P<PERSON><PERSON>ailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\SMTP;
use PHPMailer\PHPMailer\Exception;

require __DIR__ . '/../vendor/autoload.php';

class correo
{

    public $asunto;
    public $to;
    public $body;

    function __construct($destino, $asunto)
    {
        $this->to = $destino;
        $this->asunto = $asunto;
        $this->body = "";
    }

    public function cuerpoRecibo($renglones, $total, $operaddor, $folio, $capturista, $caja)
    {
        $msg = '<html><head><meta charset="utf-8"></head>
        <body>
        <table border="0" align="center" width="650">
        <tr>
        <td>
            <h2>Recibo Sitio 152</h2>
        </td>
        </tr>
	<tr>
		<td>
			<p>
            <b>Folio:</b> ' . $folio . '<br>
            <b>Operador:</b> ' . $operaddor['nombreCompleto'] . '<br>
            <b>Contable:</b> ' . $operaddor['contable'] . '<br>
            <b>Capturado por:</b> ' . $capturista . ' <br>
            <b>Cobrado por:</b> ' . $caja . '
            </p>
		</td>
	</tr>
</table><hr>';

        $msg .= '<table border="1">
        <col style="width: 38%;">
        <col style="width: 15%;">
        <col style="width: 14%;">
        <col style="width: 14%;">
        <col style="width: 19%;">
        <thead>
            <tr>
                <th>Concepto</th>
                <th>Semana</th>
                <th>A&ntilde;o</th>
                <th>Tipo</th>
                <th>Importe</th>
            </tr>
        </thead>
        <tbody>';
        $msg .= $renglones;
        $msg .= '<tr style="background: #FFF376">
                <td colspan="3">&nbsp;</td>
                <td>Total</td>
                <td>$ ' . number_format($total, 2) . '</td>
            </tr>
        </tbody>
    </table>
    </body>
    </html>';

        $this->body = $msg;
    }

    public function enviar()
    {
        $mail = new PHPMailer(true);

        try {
            // $mail->SMTPDebug = 0;
            // $mail->isSMTP();
            // $mail->Host       = 'smtp.exchangeadministrado.com';
            // $mail->SMTPAuth   = true;
            // $mail->Username   = '<EMAIL>';
            // $mail->Password   = 'CosmicStarArrow10!!';
            // $mail->SMTPSecure = 'STARTTLS';
            // $mail->Port       = 587;

            $mail->SMTPDebug = 0;
            $mail->isSMTP();
            $mail->Host       = 'smtp.gmail.com';
            $mail->SMTPAuth   = true;
            $mail->Username   = '<EMAIL>';
            $mail->Password   = 'fvbkzvlgpsuktfba';
            $mail->SMTPSecure = 'TLS';
            $mail->Port       = 587;

            //Recipients
            $mail->setFrom('<EMAIL>', 'Casetas Sitio 152');
            $mail->addReplyTo('<EMAIL>', 'Sitio 152');
            $mail->addAddress($this->to);
            $mail->addBCC('<EMAIL>');

            //Content
            $mail->isHTML(true);
            $mail->CharSet = 'UTF-8';
            $mail->Subject = $this->asunto;
            $mail->Body    = $this->body;

            $mail->send();
        } catch (Exception $e) {
            echo "Algo fallo Mailer Error: {$mail->ErrorInfo}";
        }
    }
}
