<?php
class usuario
{

    public $nombre;
    public $paterno;
    public $materno;
    public $telefono;
    public $correo;
    public $estado;
    public $identificador;
    public $user;
    public $pass;
    public $tipoUser;

    function __construct($nom, $ap, $am, $userName, $passwd, $mail, $type, $std, $tel)
    {
        $this->nombre = $nom;
        $this->paterno = $ap;
        $this->materno = $am;
        $this->telefono = $tel;
        $this->correo = $mail;
        $this->estado = $std;
        $this->user = $userName;
        $this->pass = $passwd;
        $this->tipoUser = $type;
    }

    public function saveUsuario($conexion)
    {
        $q = "INSERT INTO usuario(nombre, apellidoPaterno, apellidoMaterno, correo, telefono) 
        VALUES ('$this->nombre',
        '$this->paterno',
        '$this->materno',
        '$this->correo',
        '$this->telefono')";
        $eq = $conexion->query($q);
        if ($eq) {
            $this->identificador = $conexion->insert_id;
        } else {
            $this->identificador = false;
        }

        return $this->identificador;
    }

    public function saveLogin($conexion)
    {
        $q = "INSERT INTO login(idUsuario, tipoUsuario, userName, passwd, estado) 
        VALUES ('$this->identificador',
        '$this->tipoUser',
        '$this->user',
        '$this->pass',
        '$this->estado')";
        $eq = $conexion->query($q);

        return $eq;
    }

    public function estadoLogin($conexion)
    {
        $q = "SELECT l.estado
        FROM `login` l
        WHERE l.idUsuario = '$this->identificador'
        AND l.tipoUsuario = '$this->tipoUser'";
        $eq = $conexion->query($q);
        $row = $eq->fetch_array(MYSQLI_ASSOC);
        $this->estado = $row['estado'];

        return $this->estado;
    }


    public function validaUserType($conexion)
    {
        $q = "SELECT idUsuario FROM `login` 
        WHERE idUsuario = '$this->identificador'
        AND tipoUsuario = '$this->tipoUser'
        AND estado = 'A'";
        $eq = $conexion->query($q);
        if ($eq->num_rows != 0) {
            $row = true;
        } else {
            $row = false;
        }
        return $row;
    }

    public function dataUser($conexion)
    {
        $q = "SELECT idUsuario, tipoUsuario, LENGTH(passwd) AS pass FROM `login` 
        WHERE userName = '$this->user'
        AND estado = 'A'";
        $eq = $conexion->query($q);
        if ($eq->num_rows != 0) {
            $row = $eq->fetch_array(MYSQLI_ASSOC);
        } else {
            $row = false;
        }
        return $row;
    }

    public function datsUserSinLength($conexion)
    {
        $q = "SELECT idUsuario, tipoUsuario, passwd
        FROM `login` 
        WHERE idUsuario='$this->user'
        AND tipoUsuario = '$this->tipoUser';";
        $rq = $conexion->query($q);
        $row = $rq->fetch_array(MYSQLI_ASSOC);

        return $row;
    }

    public function passDecifred($conexion)
    {
        $q = "SELECT idUsuario FROM `login` 
        WHERE userName = '$this->user' 
        AND passwd = '$this->pass'
        AND estado = 'A'";
        $eq = $conexion->query($q);
        if ($eq->num_rows != 0) {
            $row = true;
        } else {
            $row = false;
        }
        return $row;
    }

    public function passCifred($conexion)
    {
        $q = "SELECT passwd FROM `login` 
        WHERE userName = '$this->user'
        AND estado = 'A'";
        $eq = $conexion->query($q);
        if ($eq->num_rows != 0) {
            $row = $eq->fetch_array(MYSQLI_ASSOC);
        } else {
            $row = false;
        }
        return $row;
    }

    public function cifraNewPass($bool)
    {
        if ($bool) {
            $this->pass = password_hash($this->pass, PASSWORD_DEFAULT);
        } else {
            $this->pass = $this->pass;
        }
    }

    public function updatePass($conexion)
    {
        $up = "UPDATE `login` SET passwd = '$this->pass' WHERE idUsuario = '$this->identificador' AND tipoUsuario = '$this->tipoUser'";
        $ru = $conexion->query($up);
        return $ru;
    }

    public function bajaLogin($conexion)
    {
        $q = "UPDATE `login` SET estado = '$this->estado' WHERE idUsuario = '$this->identificador' 
        AND tipoUsuario = '$this->tipoUser';";

        return $conexion->query($q);
    }

    public function loginList($conexion)
    {
        $q = "SELECT idUsuario, tipoUsuario, Persona, usuarioTipo, describeEstado, estado 
                FROM loginlist;";
        $eq = $conexion->query($q);

        return $eq;
    }
}
