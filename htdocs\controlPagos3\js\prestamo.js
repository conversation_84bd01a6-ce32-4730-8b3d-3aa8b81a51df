$(document).ready(function () {
  if ($.get("resp")) {
    Rsp = $.get("resp");
    switch (Rsp) {
      case "0":
        rmsg = "Error al guardar los datos";
        break;
      case "1":
        rmsg = "Datos guardados correctamente";
        break;
    }
    alertify.alert("Servitaxis Sitio 152", rmsg);
  }

  $("#fechaCheque").datepicker({
    dateFormat: "yy-mm-dd",
    changeMonth: true,
    changeYear: true,
    numberOfMonths: 1,
    maxDate: "Today",
  });

  $(".card-body,#prestamoData").hide();

  $("#prestamoData").validate();

  $("#busqueda").validate({
    submitHandler: function (form) {
      datoOP = $("#buscadorOp").val();
      $.ajax({
        type: "post",
        url: "php/ajax/buscaOperador.php",
        data: { datoBusca: datoOP },
      })
        .done(function (r) {
          R = $.parseJSON(r);
          if (R[0] != 0) {
            datos = R[0];
            $(".card-body, #prestamoData").show();
            $("#operador").empty().html(datos.nombreCompleto);
            $("#contable").empty().html(datos.contable);
            $("#operadorID").val(datos.idOperador);
          } else {
            $("#operador").empty();
            $("#contable").empty();
            $("#operadorID").val("");
            $("#noCheque").val("");
            $("#monto").val("");

            alertify.alert("Warning!", "Operador no encontrado");
          }
        })
        .fail(function () {
          alertify.alert("Warning!", "Error al cargar el formulario");
        });
    },
  });
});
