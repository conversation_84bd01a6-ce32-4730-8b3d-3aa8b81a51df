 PMA_token |s:32:"7b6b35493d39604d436b3f3f4727433d"; HMAC_secret |s:16:"YE\q`tKEFc7+L)!L";browser_access_time|a:23:{s:7:"default";i:1749149129;s:36:"0c85e53b-e2dc-0fda-0ac2-719afdada22d";i:1747944785;s:36:"64845d28-5109-199d-f8e1-8ef1dfae8d20";i:1748877516;s:36:"7acb8b75-788e-0e75-b98d-39d032c7b016";i:1747964473;s:36:"c69c4fde-a8ab-4070-f010-1dc2fda7baf6";i:1748916576;s:36:"eebe0550-dea3-f95f-bc31-582958b2dfd5";i:1748012590;s:36:"24b0da34-7a0c-a3bf-e169-9dc188a325e5";i:1748035423;s:36:"a8c52cef-84a6-d6a0-1392-f1973427a32b";i:1748040608;s:36:"b8fad87e-1c7c-e292-7f50-b8af41713d0b";i:1748463954;s:36:"0ee1c0a5-0014-1198-7eee-02a6eb055afc";i:1748466855;s:36:"71b57ae2-bfa7-3245-3b29-f39c4cdd231e";i:1748549702;s:36:"a683c1c0-b6d1-003b-cbaf-509370009e54";i:1748875245;s:36:"b9bfad55-fa4e-af99-1f03-b2859cc8c9a2";i:1749241255;s:36:"8287cb55-fc4b-5737-b9cb-d6e2a24380d9";i:1748878565;s:36:"0b092713-d518-1e68-19d0-a8e65db01f91";i:1749146666;s:36:"8cc2211e-d53e-58cc-60fd-cb374bd212ce";i:1748894774;s:36:"d2e69949-8e6f-c693-18a0-7ad537c0da7f";i:1748914414;s:36:"5bde38a1-000e-528d-6c13-75861527b044";i:1749240757;s:36:"883d3888-6191-54a5-b49c-c80742f049cf";i:1749091667;s:36:"43526b6c-7ca6-5f5c-80b1-24486c109fa6";i:1749129487;s:36:"768cfde1-0cc9-3722-0371-5d314fe56ca8";i:1749137967;s:36:"c8237bfb-5da6-e502-0310-abf3d15b9431";i:1749241977;s:36:"09c4f483-b895-6dc9-1073-40560b3aa633";i:1749240574;}relation|a:1:{i:1;a:41:{s:7:"version";s:5:"5.2.1";s:4:"user";s:4:"root";s:2:"db";s:10:"phpmyadmin";s:8:"bookmark";s:13:"pma__bookmark";s:15:"central_columns";s:20:"pma__central_columns";s:11:"column_info";s:16:"pma__column_info";s:17:"designer_settings";s:22:"pma__designer_settings";s:16:"export_templates";s:21:"pma__export_templates";s:8:"favorite";s:13:"pma__favorite";s:7:"history";s:12:"pma__history";s:16:"navigationhiding";s:21:"pma__navigationhiding";s:9:"pdf_pages";s:14:"pma__pdf_pages";s:6:"recent";s:11:"pma__recent";s:8:"relation";s:13:"pma__relation";s:13:"savedsearches";s:18:"pma__savedsearches";s:12:"table_coords";s:17:"pma__table_coords";s:10:"table_info";s:15:"pma__table_info";s:13:"table_uiprefs";s:18:"pma__table_uiprefs";s:8:"tracking";s:13:"pma__tracking";s:10:"userconfig";s:15:"pma__userconfig";s:10:"usergroups";s:15:"pma__usergroups";s:5:"users";s:10:"pma__users";s:12:"bookmarkwork";b:1;s:8:"mimework";b:1;s:18:"centralcolumnswork";b:1;s:8:"commwork";b:1;s:9:"menuswork";b:1;s:20:"designersettingswork";b:1;s:11:"displaywork";b:1;s:19:"exporttemplateswork";b:1;s:12:"favoritework";b:1;s:7:"navwork";b:1;s:7:"pdfwork";b:1;s:10:"recentwork";b:1;s:7:"relwork";b:1;s:17:"savedsearcheswork";b:1;s:11:"historywork";b:1;s:12:"trackingwork";b:1;s:11:"uiprefswork";b:1;s:14:"userconfigwork";b:1;s:8:"allworks";b:1;}}tmpval|a:16:{s:14:"favoriteTables";a:1:{i:1;a:0:{}}s:12:"recentTables";a:1:{i:1;a:10:{i:0;a:2:{s:2:"db";s:18:"information_schema";s:5:"table";s:6:"tables";}i:1;a:2:{s:2:"db";s:7:"control";s:5:"table";s:7:"importe";}i:2;a:2:{s:2:"db";s:10:"minutas_db";s:5:"table";s:15:"minuta_acuerdos";}i:3;a:2:{s:2:"db";s:10:"minutas_db";s:5:"table";s:7:"minutas";}i:4;a:2:{s:2:"db";s:7:"control";s:5:"table";s:17:"operadoreconomico";}i:5;a:2:{s:2:"db";s:7:"control";s:5:"table";s:8:"concepto";}i:6;a:2:{s:2:"db";s:7:"control";s:5:"table";s:15:"importeconvenio";}i:7;a:2:{s:2:"db";s:7:"control";s:5:"table";s:5:"cargo";}i:8;a:2:{s:2:"db";s:7:"control";s:5:"table";s:5:"login";}i:9;a:2:{s:2:"db";s:7:"control";s:5:"table";s:7:"usuario";}}}s:18:"table_limit_offset";i:0;s:21:"table_limit_offset_db";s:10:"minutas_db";s:13:"table_uiprefs";a:1:{i:1;a:3:{s:7:"control";a:15:{s:6:"recibo";a:0:{}s:7:"usuario";a:0:{}s:7:"importe";a:1:{s:10:"sorted_col";s:25:"`importe`.`idImporte` ASC";}s:9:"economico";a:0:{}s:5:"abono";a:0:{}s:14:"cuentabancaria";a:0:{}s:5:"login";a:0:{}s:5:"owner";a:0:{}s:8:"licencia";a:0:{}s:8:"concepto";a:0:{}s:8:"operador";a:0:{}s:6:"seguro";a:0:{}s:17:"operadoreconomico";a:0:{}s:15:"importeconvenio";a:0:{}s:5:"cargo";a:0:{}}s:10:"minutas_db";a:2:{s:7:"minutas";a:0:{}s:15:"minuta_acuerdos";a:0:{}}s:18:"information_schema";a:1:{s:6:"tables";a:0:{}}}}s:5:"query";a:10:{s:32:"e495331386c774278212e3a35975e510";a:8:{s:3:"sql";s:349:"-- Tabla de acuerdos por minuta
CREATE TABLE minuta_acuerdos (
    id INT AUTO_INCREMENT PRIMARY KEY,
    minuta_id VARCHAR(20) NOT NULL,
    orden_acuerdo INT NOT NULL,
    descripcion TEXT NOT NULL,
    responsable VARCHAR(255),
    fecha_aproximada DATETIME,
    FOREIGN KEY (minuta_id) REFERENCES minutas(minuta_id) ON DELETE CASCADE
);";s:12:"repeat_cells";i:100;s:8:"max_rows";i:25;s:3:"pos";i:0;s:6:"pftext";s:1:"P";s:18:"relational_display";s:1:"K";s:9:"geoOption";s:4:"GEOM";s:14:"display_binary";b:1;}s:32:"fa64e4f52617c02213068e2548048860";a:8:{s:3:"sql";s:91:"-- Índices para mejorar rendimiento
CREATE INDEX idx_minuta_fecha ON minutas(fecha_hora);";s:12:"repeat_cells";i:100;s:8:"max_rows";i:25;s:3:"pos";i:0;s:6:"pftext";s:1:"P";s:18:"relational_display";s:1:"K";s:9:"geoOption";s:4:"GEOM";s:14:"display_binary";b:1;}s:32:"c791b9abce1054d2f63fd457d213c9cd";a:8:{s:3:"sql";s:52:"CREATE INDEX idx_minuta_convoca ON minutas(convoca);";s:12:"repeat_cells";i:100;s:8:"max_rows";i:25;s:3:"pos";i:0;s:6:"pftext";s:1:"P";s:18:"relational_display";s:1:"K";s:9:"geoOption";s:4:"GEOM";s:14:"display_binary";b:1;}s:32:"46f9141e4a9752192404acc273942afc";a:8:{s:3:"sql";s:67:"CREATE INDEX idx_asistentes_minuta ON minuta_asistentes(minuta_id);";s:12:"repeat_cells";i:100;s:8:"max_rows";i:25;s:3:"pos";i:0;s:6:"pftext";s:1:"P";s:18:"relational_display";s:1:"K";s:9:"geoOption";s:4:"GEOM";s:14:"display_binary";b:1;}s:32:"19563a38698847d7bb0a7742560e43c5";a:8:{s:3:"sql";s:61:"CREATE INDEX idx_asuntos_minuta ON minuta_asuntos(minuta_id);";s:12:"repeat_cells";i:100;s:8:"max_rows";i:25;s:3:"pos";i:0;s:6:"pftext";s:1:"P";s:18:"relational_display";s:1:"K";s:9:"geoOption";s:4:"GEOM";s:14:"display_binary";b:1;}s:32:"a3eab9970231164c43af59c1c518a521";a:8:{s:3:"sql";s:63:"CREATE INDEX idx_acuerdos_minuta ON minuta_acuerdos(minuta_id);";s:12:"repeat_cells";i:100;s:8:"max_rows";i:25;s:3:"pos";i:0;s:6:"pftext";s:1:"P";s:18:"relational_display";s:1:"K";s:9:"geoOption";s:4:"GEOM";s:14:"display_binary";b:1;}s:32:"5d692e8185c5adb1df3af9738b2c3600";a:8:{s:3:"sql";s:524:"-- Insertar algunos datos de ejemplo (opcional)
INSERT INTO minutas (minuta_id, fecha_hora, lugar, duracion, convoca, antecedentes, objetivo, relator, orden_dia, proxima_reunion) 
VALUES 
('MIN-2024-001', '2024-01-15 10:00:00', 'Sala de junta A del RUV', '2 horas', 'María Elena López', 
 'Reunión de seguimiento del proyecto de desarrollo', 'Revisar avances y definir próximos pasos', 
 'Norberto Rojas Nava', 'Revisión de avances\nDefinición de tareas\nPlanificación siguiente fase', 
 '2024-01-22 10:00:00');";s:12:"repeat_cells";i:100;s:8:"max_rows";i:25;s:3:"pos";i:0;s:6:"pftext";s:1:"P";s:18:"relational_display";s:1:"K";s:9:"geoOption";s:4:"GEOM";s:14:"display_binary";b:1;}s:32:"0048ae282ba9c99a44c5f2a87569868f";a:8:{s:3:"sql";s:23:"SELECT * FROM `minutas`";s:12:"repeat_cells";i:100;s:8:"max_rows";i:25;s:3:"pos";i:0;s:6:"pftext";s:1:"P";s:18:"relational_display";s:1:"K";s:9:"geoOption";s:4:"GEOM";s:14:"display_binary";b:1;}s:32:"675ba8b9b505082ae42e1563d2a662f2";a:8:{s:3:"sql";s:31:"SELECT * FROM `minuta_acuerdos`";s:12:"repeat_cells";i:100;s:8:"max_rows";i:25;s:3:"pos";i:0;s:6:"pftext";s:1:"P";s:18:"relational_display";s:1:"K";s:9:"geoOption";s:4:"GEOM";s:14:"display_binary";b:1;}s:32:"cc8699ba155c0c181c937391faceb220";a:8:{s:3:"sql";s:260:"SELECT 
    table_schema AS 'minutas_db',
    table_name AS 'minutas',
    ROUND((data_length + index_length) / 1024 / 1024, 2) AS 'Tamaño (MB)'
FROM 
    information_schema.tables
WHERE 
    table_schema = 'minutas_db'
    AND table_name = 'minutas';";s:12:"repeat_cells";i:100;s:8:"max_rows";i:25;s:3:"pos";i:0;s:6:"pftext";s:1:"P";s:18:"relational_display";s:1:"K";s:9:"geoOption";s:4:"GEOM";s:14:"display_binary";b:1;}}s:6:"pftext";s:1:"P";s:18:"relational_display";s:1:"K";s:9:"geoOption";s:3:"WKT";s:14:"display_binary";b:1;s:12:"display_blob";b:0;s:19:"hide_transformation";b:0;s:3:"pos";i:0;s:8:"max_rows";i:25;s:12:"repeat_cells";i:100;s:20:"possible_as_geometry";b:0;}two_factor_check|b:1;cache|a:3:{s:8:"server_1";a:4:{s:15:"userprefs_mtime";i:1749093040;s:14:"userprefs_type";s:2:"db";s:12:"config_mtime";i:1447170388;s:9:"userprefs";a:2:{s:7:"Console";a:1:{s:4:"Mode";s:8:"collapse";}s:4:"lang";s:2:"es";}}s:13:"server_1_root";a:17:{s:17:"is_create_db_priv";b:1;s:14:"is_reload_priv";b:1;s:12:"db_to_create";s:0:"";s:30:"dbs_where_create_table_allowed";a:1:{i:0;s:1:"*";}s:11:"dbs_to_test";b:0;s:9:"proc_priv";b:1;s:10:"table_priv";b:1;s:8:"col_priv";b:1;s:7:"db_priv";b:1;s:11:"binary_logs";a:0:{}s:18:"menu-levels-server";a:13:{s:9:"databases";s:14:"Bases de datos";s:3:"sql";s:3:"SQL";s:6:"status";s:13:"Estado actual";s:6:"rights";s:8:"Usuarios";s:6:"export";s:8:"Exportar";s:6:"import";s:8:"Importar";s:8:"settings";s:14:"Configuración";s:6:"binlog";s:16:"Registro binario";s:11:"replication";s:12:"Replicación";s:4:"vars";s:9:"Variables";s:7:"charset";s:20:"Juegos de caracteres";s:7:"plugins";s:12:"Complementos";s:6:"engine";s:7:"Motores";}s:14:"menu-levels-db";a:14:{s:9:"structure";s:10:"Estructura";s:3:"sql";s:3:"SQL";s:6:"search";s:6:"Buscar";s:5:"query";s:20:"Generar una consulta";s:6:"export";s:8:"Exportar";s:6:"import";s:8:"Importar";s:9:"operation";s:11:"Operaciones";s:10:"privileges";s:11:"Privilegios";s:8:"routines";s:7:"Rutinas";s:6:"events";s:7:"Eventos";s:8:"triggers";s:12:"Disparadores";s:8:"tracking";s:11:"Seguimiento";s:8:"designer";s:10:"Diseñador";s:15:"central_columns";s:18:"Columnas centrales";}s:19:"profiling_supported";b:1;s:17:"menu-levels-table";a:11:{s:6:"browse";s:8:"Examinar";s:9:"structure";s:10:"Estructura";s:3:"sql";s:3:"SQL";s:6:"search";s:6:"Buscar";s:6:"insert";s:8:"Insertar";s:6:"export";s:8:"Exportar";s:6:"import";s:8:"Importar";s:10:"privileges";s:11:"Privilegios";s:9:"operation";s:11:"Operaciones";s:8:"tracking";s:11:"Seguimiento";s:8:"triggers";s:12:"Disparadores";}s:12:"is_superuser";b:1;s:14:"mysql_cur_user";s:14:"root@localhost";s:12:"is_grantuser";b:1;}s:13:"version_check";a:2:{s:8:"response";s:411:"{
    "version": "5.2.2",
    "date": "2025-01-21",
    "releases": [
        {
            "version": "5.2.2",
            "date": "2025-01-21",
            "php_versions": ">=7.2,<8.4",
            "mysql_versions": ">=5.5"
        },
        {
            "version": "4.9.11",
            "date": "2023-02-08",
            "php_versions": ">=5.5,<8.0",
            "mysql_versions": ">=5.5"
        }
    ]
}";s:9:"timestamp";i:1747943818;}}git_location|N;is_git_revision|b:0;ConfigFile1|a:2:{s:7:"Console";a:1:{s:4:"Mode";s:8:"collapse";}s:7:"Servers";a:1:{i:1;a:2:{s:7:"only_db";s:0:"";s:7:"hide_db";s:0:"";}}}debug|a:0:{}flashMessages|a:0:{}__upload_status|a:1:{s:7:"handler";s:47:"PhpMyAdmin\Plugins\Import\Upload\UploadNoplugin";}Import_message|a:2:{s:7:"message";N;s:11:"go_back_url";s:57:"index.php?route=/table/structure&db=control&table=importe";}is_multi_query|b:0;sql_history|a:25:{i:0;a:3:{s:2:"db";s:7:"control";s:5:"table";s:7:"importe";s:8:"sqlquery";s:23:"SELECT * FROM `importe`";}i:1;a:3:{s:2:"db";s:7:"control";s:5:"table";s:7:"importe";s:8:"sqlquery";s:63:"SELECT * FROM `importe` WHERE idconcepto = 1 and estatus = 'A';";}i:2;a:3:{s:2:"db";s:7:"control";s:5:"table";s:7:"importe";s:8:"sqlquery";s:63:"SELECT * FROM `importe` WHERE idconcepto = 1 and estatus = 'A';";}i:3;a:3:{s:2:"db";s:7:"control";s:5:"table";s:7:"importe";s:8:"sqlquery";s:80:"SELECT * FROM `importe` WHERE idconcepto = 1 and estatus = 'A' and monto < '10';";}i:4;a:3:{s:2:"db";s:7:"control";s:5:"table";s:7:"importe";s:8:"sqlquery";s:23:"SELECT * FROM `importe`";}i:5;a:3:{s:2:"db";s:7:"control";s:5:"table";s:7:"importe";s:8:"sqlquery";s:23:"SELECT * FROM `importe`";}i:6;a:3:{s:2:"db";s:7:"control";s:5:"table";s:7:"importe";s:8:"sqlquery";s:23:"SELECT * FROM `importe`";}i:7;a:3:{s:2:"db";s:7:"control";s:5:"table";s:7:"importe";s:8:"sqlquery";s:23:"SELECT * FROM `importe`";}i:8;a:3:{s:2:"db";s:7:"control";s:5:"table";s:7:"importe";s:8:"sqlquery";s:23:"SELECT * FROM `importe`";}i:9;a:3:{s:2:"db";s:7:"control";s:5:"table";s:7:"importe";s:8:"sqlquery";s:23:"SELECT * FROM `importe`";}i:10;a:3:{s:2:"db";s:7:"control";s:5:"table";s:7:"importe";s:8:"sqlquery";s:277:"CREATE PROCEDURE insertarImportes2()
BEGIN
    DECLARE i INT DEFAULT 1;
    WHILE i <= 5000 DO
        INSERT INTO control.importe (idConcepto, monto, fechaRegistro, estatus) 
        VALUES (2, i, '2023-01-03 13:56:39', 'A');
        SET i = i + 1;
    END WHILE;
END;";}i:11;a:3:{s:2:"db";s:7:"control";s:5:"table";s:7:"importe";s:8:"sqlquery";s:25:"CALL insertarImportes2();";}i:12;a:3:{s:2:"db";s:10:"minutas_db";s:5:"table";s:0:"";s:8:"sqlquery";s:90:"CREATE DATABASE IF NOT EXISTS minutas_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;";}i:13;a:3:{s:2:"db";s:10:"minutas_db";s:5:"table";s:0:"";s:8:"sqlquery";s:15:"USE minutas_db;";}i:14;a:3:{s:2:"db";s:8:"autoprod";s:5:"table";s:0:"";s:8:"sqlquery";s:528:"CREATE TABLE minutas (
    id INT AUTO_INCREMENT PRIMARY KEY,
    minuta_id VARCHAR(20) UNIQUE NOT NULL,
    fecha_hora DATETIME NOT NULL,
    lugar VARCHAR(255) NOT NULL,
    duracion VARCHAR(50) NOT NULL,
    convoca VARCHAR(255) NOT NULL,
    antecedentes TEXT,
    objetivo TEXT,
    relator VARCHAR(255) NOT NULL,
    orden_dia TEXT,
    proxima_reunion DATETIME,
    fecha_creacion TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    fecha_modificacion TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);";}i:15;a:3:{s:2:"db";s:8:"autoprod";s:5:"table";s:0:"";s:8:"sqlquery";s:345:"-- Tabla de asistentes por minuta
CREATE TABLE minuta_asistentes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    minuta_id VARCHAR(20) NOT NULL,
    nombre VARCHAR(255) NOT NULL,
    area VARCHAR(255),
    extension_correo VARCHAR(255),
    firma VARCHAR(255),
    FOREIGN KEY (minuta_id) REFERENCES minutas(minuta_id) ON DELETE CASCADE
);";}i:16;a:3:{s:2:"db";s:8:"autoprod";s:5:"table";s:0:"";s:8:"sqlquery";s:292:"-- Tabla de asuntos tratados por minuta
CREATE TABLE minuta_asuntos (
    id INT AUTO_INCREMENT PRIMARY KEY,
    minuta_id VARCHAR(20) NOT NULL,
    orden_asunto INT NOT NULL,
    descripcion TEXT NOT NULL,
    FOREIGN KEY (minuta_id) REFERENCES minutas(minuta_id) ON DELETE CASCADE
);";}i:17;a:3:{s:2:"db";s:8:"autoprod";s:5:"table";s:0:"";s:8:"sqlquery";s:349:"-- Tabla de acuerdos por minuta
CREATE TABLE minuta_acuerdos (
    id INT AUTO_INCREMENT PRIMARY KEY,
    minuta_id VARCHAR(20) NOT NULL,
    orden_acuerdo INT NOT NULL,
    descripcion TEXT NOT NULL,
    responsable VARCHAR(255),
    fecha_aproximada DATETIME,
    FOREIGN KEY (minuta_id) REFERENCES minutas(minuta_id) ON DELETE CASCADE
);";}i:18;a:3:{s:2:"db";s:8:"autoprod";s:5:"table";s:0:"";s:8:"sqlquery";s:63:"CREATE INDEX idx_acuerdos_minuta ON minuta_acuerdos(minuta_id);";}i:19;a:3:{s:2:"db";s:8:"autoprod";s:5:"table";s:0:"";s:8:"sqlquery";s:475:"INSERT INTO minutas (minuta_id, fecha_hora, lugar, duracion, convoca, antecedentes, objetivo, relator, orden_dia, proxima_reunion) 
VALUES 
('MIN-2024-001', '2024-01-15 10:00:00', 'Sala de junta A del RUV', '2 horas', 'María Elena López', 
 'Reunión de seguimiento del proyecto de desarrollo', 'Revisar avances y definir próximos pasos', 
 'Norberto Rojas Nava', 'Revisión de avances\nDefinición de tareas\nPlanificación siguiente fase', 
 '2024-01-22 10:00:00');";}i:20;a:3:{s:2:"db";s:10:"minutas_db";s:5:"table";s:0:"";s:8:"sqlquery";s:524:"-- Insertar algunos datos de ejemplo (opcional)
INSERT INTO minutas (minuta_id, fecha_hora, lugar, duracion, convoca, antecedentes, objetivo, relator, orden_dia, proxima_reunion) 
VALUES 
('MIN-2024-001', '2024-01-15 10:00:00', 'Sala de junta A del RUV', '2 horas', 'María Elena López', 
 'Reunión de seguimiento del proyecto de desarrollo', 'Revisar avances y definir próximos pasos', 
 'Norberto Rojas Nava', 'Revisión de avances\nDefinición de tareas\nPlanificación siguiente fase', 
 '2024-01-22 10:00:00');";}i:21;a:3:{s:2:"db";s:10:"minutas_db";s:5:"table";s:7:"minutas";s:8:"sqlquery";s:23:"SELECT * FROM `minutas`";}i:22;a:3:{s:2:"db";s:10:"minutas_db";s:5:"table";s:15:"minuta_acuerdos";s:8:"sqlquery";s:31:"SELECT * FROM `minuta_acuerdos`";}i:23;a:3:{s:2:"db";s:10:"minutas_db";s:5:"table";s:15:"minuta_acuerdos";s:8:"sqlquery";s:31:"SELECT * FROM `minuta_acuerdos`";}i:24;a:3:{s:2:"db";s:18:"information_schema";s:5:"table";s:6:"tables";s:8:"sqlquery";s:260:"SELECT 
    table_schema AS 'minutas_db',
    table_name AS 'minutas',
    ROUND((data_length + index_length) / 1024 / 1024, 2) AS 'Tamaño (MB)'
FROM 
    information_schema.tables
WHERE 
    table_schema = 'minutas_db'
    AND table_name = 'minutas';";}}sql_from_query_box|b:1;errors|a:0:{}