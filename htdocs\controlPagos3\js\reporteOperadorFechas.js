$(document).ready(function () {
  $("#operador").miCatalogo(
    "operadorcontable",
    "Error en  catalogo de operador"
  );
  $("#concepto").miCatalogo("concepto", "Error en  catalogo de concepto");
  $("#formSemanal").validate({
    submitHandler: function (form) {
      const idConceto = $("#concepto").val();
      const conceptoText = $("#concepto option:selected").text();
      $("#concepto option:selected").remove();
      $("#concepto").val("");
      // $("#headerList").children().last().remove();
      $("#headerList").append(`<th>${conceptoText}</th>`);
      // $("#headerList").append(`<th>Total</th>`);
      $("#conceptosList").append(
        `<input type="hidden" name="concepto[]" value="${idConceto}">`
      );
      $("#genReporte").show();
    },
  });
  $("#genReporte").hide();
  $("#genReporte").validate();

  $("#beginDate")
    .datepicker({
      dateFormat: "yy-mm-dd",
      defaultDate: "-2M",
      changeMonth: true,
      changeYear: true,
      numberOfMonths: 2,
    })
    .on("change", function () {
      $("#endDate").datepicker("option", "minDate", $(this).val());
    });
  $("#endDate")
    .datepicker({
      dateFormat: "yy-mm-dd",
      defaultDate: "NOW",
      changeMonth: true,
      changeYear: true,
      numberOfMonths: 2,
    })
    .on("change", function () {
      $("#beginDate").datepicker("option", "maxDate", $(this).val());
    });
});
