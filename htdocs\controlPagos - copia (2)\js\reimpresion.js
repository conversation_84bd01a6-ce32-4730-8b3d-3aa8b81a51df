$(document).ready(function () {
  if ($.get("tipo")) {
    const tipo = $.get("tipo");
    switch (tipo) {
      case "1":
        $("#folio").attr("name", "r");
        $("#folioReciboForm").attr("action", "reciboPrint.php");
        break;
      case "2":
        $("#folio").attr("name", "con");
        $("#folioReciboForm").attr("action", "pagarePrint.php");
        $("#folioReciboForm").append(
          `<input type="hidden" name="p" value="1">`
        );
        $("#letraFolio").html("P-");
        break;
      case "3":
        $("#folioReciboForm").attr("action", "pagarePrint.php");
        $("#letraFolio").html("C-");
        $("#folio").attr("name", "con");
        break;
      default:
        $(location).attr("href", "php/salir.php");
    }
  } else {
    $(location).attr("href", "php/salir.php");
  }
});
