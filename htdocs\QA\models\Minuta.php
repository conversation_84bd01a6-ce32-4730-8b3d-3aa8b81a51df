<?php
require_once '../config_QA/database.php';

class Minuta {
    private $conn;
    private $table_name = "minutas";

    public $id;
    public $minuta_id;
    public $fecha_hora;
    public $lugar;
    public $duracion;
    public $convoca;
    public $antecedentes;
    public $objetivo;
    public $relator;
    public $orden_dia;
    public $proxima_reunion;
    public $fecha_creacion;
    public $fecha_modificacion;

    public function __construct($db) {
        $this->conn = $db;
    }

    /**
     * Crear nueva minuta
     */
    public function create($data) {
        try {
            $this->conn->beginTransaction();

            // Generar ID único
            $this->minuta_id = Database::generateMinutaId();

            // Insertar minuta principal (marcada como guardado manual)
            $query = "INSERT INTO " . $this->table_name . "
                     (minuta_id, fecha_hora, lugar, duracion, convoca, antecedentes, objetivo, relator, orden_dia, proxima_reunion)
                     VALUES (:minuta_id, :fecha_hora, :lugar, :duracion, :convoca, :antecedentes, :objetivo, :relator, :orden_dia, :proxima_reunion)";

            $stmt = $this->conn->prepare($query);

            // Convertir fechas al formato MySQL
            $fecha_hora = $this->convertDateToMysql($data['fechaHora']);
            $proxima_reunion = !empty($data['proximaReunion']) ? $this->convertDateToMysql($data['proximaReunion']) : null;

            $stmt->bindParam(':minuta_id', $this->minuta_id);
            $stmt->bindParam(':fecha_hora', $fecha_hora);
            $stmt->bindParam(':lugar', $data['lugar']);
            $stmt->bindParam(':duracion', $data['duracion']);
            $stmt->bindParam(':convoca', $data['convoca']);
            $stmt->bindParam(':antecedentes', $data['antecedentes']);
            $stmt->bindParam(':objetivo', $data['objetivo']);
            $stmt->bindParam(':relator', $data['relator']);
            $stmt->bindParam(':orden_dia', $data['ordenDia']);
            $stmt->bindParam(':proxima_reunion', $proxima_reunion);

            if (!$stmt->execute()) {
                throw new Exception("Error al insertar minuta");
            }

            // Insertar asistentes
            $this->insertAsistentes($data['asistentes']);

            // Insertar asuntos tratados
            $this->insertAsuntos($data['asuntosTratados']);

            // Insertar acuerdos
            $this->insertAcuerdos($data['acuerdos']);

            $this->conn->commit();
            return $this->minuta_id;

        } catch (Exception $e) {
            $this->conn->rollback();
            throw $e;
        }
    }

    /**
     * Obtener minuta por ID
     */
    public function getById($minuta_id) {
        $query = "SELECT * FROM " . $this->table_name . " WHERE minuta_id = :minuta_id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':minuta_id', $minuta_id);
        $stmt->execute();

        $minuta = $stmt->fetch();
        if (!$minuta) {
            return null;
        }

        // Obtener datos relacionados
        $minuta['asistentes'] = $this->getAsistentes($minuta_id);
        $minuta['asuntosTratados'] = $this->getAsuntos($minuta_id);
        $minuta['acuerdos'] = $this->getAcuerdos($minuta_id);

        // Convertir fechas al formato del frontend
        $minuta['fecha_hora'] = $this->convertDateFromMysql($minuta['fecha_hora']);
        if ($minuta['proxima_reunion']) {
            $minuta['proxima_reunion'] = $this->convertDateFromMysql($minuta['proxima_reunion']);
        }

        return $minuta;
    }

    /**
     * Obtener todas las minutas (resumen)
     */
    public function getAll($limit = 50, $offset = 0) {
        $query = "SELECT minuta_id, fecha_hora, lugar, convoca, fecha_creacion
                 FROM " . $this->table_name . "
                 ORDER BY fecha_hora DESC
                 LIMIT :limit OFFSET :offset";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
        $stmt->execute();

        $minutas = $stmt->fetchAll();

        // Convertir fechas
        foreach ($minutas as &$minuta) {
            $minuta['fecha_hora'] = $this->convertDateFromMysql($minuta['fecha_hora']);
        }

        return $minutas;
    }

    /**
     * Actualizar minuta existente
     */
    public function update($minuta_id, $data) {
        try {
            $this->conn->beginTransaction();

            // Verificar que la minuta existe
            $checkQuery = "SELECT id FROM " . $this->table_name . " WHERE minuta_id = :minuta_id";
            $checkStmt = $this->conn->prepare($checkQuery);
            $checkStmt->bindParam(':minuta_id', $minuta_id);
            $checkStmt->execute();

            if ($checkStmt->rowCount() === 0) {
                throw new Exception("La minuta con ID $minuta_id no existe");
            }

            // Actualizar minuta principal
            $query = "UPDATE " . $this->table_name . "
                     SET fecha_hora = :fecha_hora,
                         lugar = :lugar,
                         duracion = :duracion,
                         convoca = :convoca,
                         antecedentes = :antecedentes,
                         objetivo = :objetivo,
                         relator = :relator,
                         orden_dia = :orden_dia,
                         proxima_reunion = :proxima_reunion,
                         fecha_modificacion = CURRENT_TIMESTAMP
                     WHERE minuta_id = :minuta_id";

            $stmt = $this->conn->prepare($query);

            // Convertir fechas al formato MySQL
            $fecha_hora = $this->convertDateToMysql($data['fechaHora']);
            $proxima_reunion = !empty($data['proximaReunion']) ? $this->convertDateToMysql($data['proximaReunion']) : null;

            $stmt->bindParam(':minuta_id', $minuta_id);
            $stmt->bindParam(':fecha_hora', $fecha_hora);
            $stmt->bindParam(':lugar', $data['lugar']);
            $stmt->bindParam(':duracion', $data['duracion']);
            $stmt->bindParam(':convoca', $data['convoca']);
            $stmt->bindParam(':antecedentes', $data['antecedentes']);
            $stmt->bindParam(':objetivo', $data['objetivo']);
            $stmt->bindParam(':relator', $data['relator']);
            $stmt->bindParam(':orden_dia', $data['ordenDia']);
            $stmt->bindParam(':proxima_reunion', $proxima_reunion);

            if (!$stmt->execute()) {
                throw new Exception("Error al actualizar minuta principal");
            }

            // Eliminar datos relacionados existentes
            $this->deleteRelatedData($minuta_id);

            // Insertar nuevos datos relacionados
            $this->minuta_id = $minuta_id; // Establecer el ID para los métodos de inserción
            $this->insertAsistentes($data['asistentes']);
            $this->insertAsuntos($data['asuntosTratados']);
            $this->insertAcuerdos($data['acuerdos']);

            $this->conn->commit();
            return true;

        } catch (Exception $e) {
            $this->conn->rollback();
            throw $e;
        }
    }

    /**
     * Eliminar datos relacionados de una minuta
     */
    private function deleteRelatedData($minuta_id) {
        $tables = ['minuta_asistentes', 'minuta_asuntos', 'minuta_acuerdos'];

        foreach ($tables as $table) {
            $query = "DELETE FROM $table WHERE minuta_id = :minuta_id";
            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(':minuta_id', $minuta_id);
            $stmt->execute();
        }
    }

    /**
     * Insertar asistentes
     */
    private function insertAsistentes($asistentes) {
        $query = "INSERT INTO minuta_asistentes (minuta_id, nombre, area, extension_correo, firma)
                 VALUES (:minuta_id, :nombre, :area, :extension_correo, :firma)";
        $stmt = $this->conn->prepare($query);

        foreach ($asistentes as $asistente) {
            if (!empty($asistente['nombre'])) {
                $stmt->bindParam(':minuta_id', $this->minuta_id);
                $stmt->bindParam(':nombre', $asistente['nombre']);
                $stmt->bindParam(':area', $asistente['area']);
                $stmt->bindParam(':extension_correo', $asistente['extensionCorreo']);
                $stmt->bindParam(':firma', $asistente['firma']);
                $stmt->execute();
            }
        }
    }

    /**
     * Insertar asuntos tratados
     */
    private function insertAsuntos($asuntos) {
        $query = "INSERT INTO minuta_asuntos (minuta_id, orden_asunto, descripcion)
                 VALUES (:minuta_id, :orden_asunto, :descripcion)";
        $stmt = $this->conn->prepare($query);

        foreach ($asuntos as $index => $asunto) {
            if (!empty($asunto)) {
                $stmt->bindParam(':minuta_id', $this->minuta_id);
                $stmt->bindParam(':orden_asunto', $index);
                $stmt->bindParam(':descripcion', $asunto);
                $stmt->execute();
            }
        }
    }

    /**
     * Insertar acuerdos
     */
    private function insertAcuerdos($acuerdos) {
        $query = "INSERT INTO minuta_acuerdos (minuta_id, orden_acuerdo, descripcion, responsable, fecha_aproximada)
                 VALUES (:minuta_id, :orden_acuerdo, :descripcion, :responsable, :fecha_aproximada)";
        $stmt = $this->conn->prepare($query);

        foreach ($acuerdos as $index => $acuerdo) {
            if (!empty($acuerdo['descripcion'])) {
                $fecha_aprox = !empty($acuerdo['fechaAprox']) ? $this->convertDateToMysql($acuerdo['fechaAprox']) : null;

                $stmt->bindParam(':minuta_id', $this->minuta_id);
                $stmt->bindParam(':orden_acuerdo', $index);
                $stmt->bindParam(':descripcion', $acuerdo['descripcion']);
                $stmt->bindParam(':responsable', $acuerdo['responsable']);
                $stmt->bindParam(':fecha_aproximada', $fecha_aprox);
                $stmt->execute();
            }
        }
    }

    /**
     * Obtener asistentes de una minuta
     */
    private function getAsistentes($minuta_id) {
        $query = "SELECT nombre, area, extension_correo as extensionCorreo, firma
                 FROM minuta_asistentes WHERE minuta_id = :minuta_id ORDER BY id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':minuta_id', $minuta_id);
        $stmt->execute();
        return $stmt->fetchAll();
    }

    /**
     * Obtener asuntos de una minuta
     */
    private function getAsuntos($minuta_id) {
        $query = "SELECT descripcion FROM minuta_asuntos
                 WHERE minuta_id = :minuta_id ORDER BY orden_asunto";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':minuta_id', $minuta_id);
        $stmt->execute();
        return array_column($stmt->fetchAll(), 'descripcion');
    }

    /**
     * Obtener acuerdos de una minuta
     */
    private function getAcuerdos($minuta_id) {
        $query = "SELECT descripcion, responsable, fecha_aproximada as fechaAprox
                 FROM minuta_acuerdos WHERE minuta_id = :minuta_id ORDER BY orden_acuerdo";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':minuta_id', $minuta_id);
        $stmt->execute();

        $acuerdos = $stmt->fetchAll();
        foreach ($acuerdos as &$acuerdo) {
            if ($acuerdo['fechaAprox']) {
                $acuerdo['fechaAprox'] = $this->convertDateFromMysql($acuerdo['fechaAprox']);
            }
        }
        return $acuerdos;
    }

    /**
     * Convertir fecha del formato frontend (d/m/Y H:i) a MySQL (Y-m-d H:i:s)
     */
    private function convertDateToMysql($dateStr) {
        if (empty($dateStr)) return null;

        // Intentar varios formatos de fecha
        $formats = ['d/m/Y H:i', 'd/m/Y G:i', 'Y-m-d H:i:s', 'Y-m-d H:i'];

        foreach ($formats as $format) {
            $date = DateTime::createFromFormat($format, $dateStr);
            if ($date !== false) {
                return $date->format('Y-m-d H:i:s');
            }
        }

        // Si no funciona ningún formato, intentar con strtotime
        $timestamp = strtotime($dateStr);
        if ($timestamp !== false) {
            return date('Y-m-d H:i:s', $timestamp);
        }

        return null;
    }

    /**
     * Convertir fecha de MySQL (Y-m-d H:i:s) al formato frontend (d/m/Y H:i)
     */
    private function convertDateFromMysql($dateStr) {
        if (empty($dateStr)) return '';
        $date = DateTime::createFromFormat('Y-m-d H:i:s', $dateStr);
        return $date ? $date->format('d/m/Y H:i') : '';
    }
}
?>
