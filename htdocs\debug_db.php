<?php
/**
 * Script de debug para verificar datos en la base de datos
 */

require_once 'config/database.php';

echo "<h1>Debug Base de Datos - Sistema de Minutas</h1>";

try {
    $database = new Database();
    $db = $database->getConnection();
    
    if (!$db) {
        echo "<div style='color: red;'>❌ No se pudo conectar a la base de datos</div>";
        exit;
    }
    
    echo "<div style='color: green;'>✅ Conexión exitosa a la base de datos</div><br>";
    
    // Verificar minutas
    echo "<h2>📋 Tabla: minutas</h2>";
    $stmt = $db->query("SELECT COUNT(*) as total FROM minutas");
    $count = $stmt->fetch()['total'];
    echo "Total de minutas: <strong>$count</strong><br>";
    
    if ($count > 0) {
        $stmt = $db->query("SELECT minuta_id, fecha_hora, lugar, convoca FROM minutas ORDER BY fecha_hora DESC LIMIT 5");
        $minutas = $stmt->fetchAll();
        
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>ID Minuta</th><th>Fecha</th><th>Lugar</th><th>Convoca</th></tr>";
        foreach ($minutas as $minuta) {
            echo "<tr>";
            echo "<td>{$minuta['minuta_id']}</td>";
            echo "<td>{$minuta['fecha_hora']}</td>";
            echo "<td>{$minuta['lugar']}</td>";
            echo "<td>{$minuta['convoca']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Verificar acuerdos
    echo "<h2>🤝 Tabla: minuta_acuerdos</h2>";
    $stmt = $db->query("SELECT COUNT(*) as total FROM minuta_acuerdos");
    $count = $stmt->fetch()['total'];
    echo "Total de acuerdos: <strong>$count</strong><br>";
    
    if ($count > 0) {
        $stmt = $db->query("SELECT minuta_id, descripcion, responsable, fecha_aproximada FROM minuta_acuerdos ORDER BY id DESC LIMIT 5");
        $acuerdos = $stmt->fetchAll();
        
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>ID Minuta</th><th>Descripción</th><th>Responsable</th><th>Fecha Compromiso</th></tr>";
        foreach ($acuerdos as $acuerdo) {
            echo "<tr>";
            echo "<td>{$acuerdo['minuta_id']}</td>";
            echo "<td>" . substr($acuerdo['descripcion'], 0, 50) . "...</td>";
            echo "<td>{$acuerdo['responsable']}</td>";
            echo "<td>{$acuerdo['fecha_aproximada']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Probar la consulta JOIN
    echo "<h2>🔗 Consulta JOIN (como en la API)</h2>";
    $query = "SELECT 
                ma.descripcion,
                ma.responsable,
                ma.fecha_aproximada,
                m.minuta_id,
                m.fecha_hora as fecha_minuta,
                m.lugar,
                m.convoca
              FROM minuta_acuerdos ma
              INNER JOIN minutas m ON ma.minuta_id = m.minuta_id
              ORDER BY m.fecha_hora DESC, ma.orden_acuerdo ASC
              LIMIT 5";
    
    $stmt = $db->query($query);
    $resultados = $stmt->fetchAll();
    
    echo "Resultados de JOIN: <strong>" . count($resultados) . "</strong><br>";
    
    if (count($resultados) > 0) {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>";
        echo "<tr><th>ID Minuta</th><th>Descripción</th><th>Responsable</th><th>Fecha Compromiso</th><th>Fecha Minuta</th><th>Lugar</th><th>Convoca</th></tr>";
        foreach ($resultados as $row) {
            echo "<tr>";
            echo "<td>{$row['minuta_id']}</td>";
            echo "<td>" . substr($row['descripcion'] ?: 'NULL', 0, 30) . "...</td>";
            echo "<td>" . ($row['responsable'] ?: 'NULL') . "</td>";
            echo "<td>" . ($row['fecha_aproximada'] ?: 'NULL') . "</td>";
            echo "<td>" . ($row['fecha_minuta'] ?: 'NULL') . "</td>";
            echo "<td>" . substr($row['lugar'] ?: 'NULL', 0, 20) . "...</td>";
            echo "<td>" . substr($row['convoca'] ?: 'NULL', 0, 20) . "...</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        echo "<h3>JSON de primer resultado:</h3>";
        echo "<pre>" . json_encode($resultados[0], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";
    }
    
    // Verificar estructura de tablas
    echo "<h2>🏗️ Estructura de Tablas</h2>";
    
    echo "<h3>Tabla minutas:</h3>";
    $stmt = $db->query("DESCRIBE minutas");
    $columns = $stmt->fetchAll();
    echo "<ul>";
    foreach ($columns as $col) {
        echo "<li><strong>{$col['Field']}</strong> - {$col['Type']} " . ($col['Null'] == 'YES' ? '(NULL)' : '(NOT NULL)') . "</li>";
    }
    echo "</ul>";
    
    echo "<h3>Tabla minuta_acuerdos:</h3>";
    $stmt = $db->query("DESCRIBE minuta_acuerdos");
    $columns = $stmt->fetchAll();
    echo "<ul>";
    foreach ($columns as $col) {
        echo "<li><strong>{$col['Field']}</strong> - {$col['Type']} " . ($col['Null'] == 'YES' ? '(NULL)' : '(NOT NULL)') . "</li>";
    }
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<div style='color: red;'>❌ Error: " . $e->getMessage() . "</div>";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h1, h2, h3 { color: #333; }
table { border-collapse: collapse; margin: 10px 0; }
th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
th { background-color: #f2f2f2; }
pre { background: #f5f5f5; padding: 10px; border-radius: 4px; overflow-x: auto; }
</style>
