$(document).ready(function () {
  if ($.get("res")) {
    Rsp = $.get("res");
    switch (Rsp) {
      case "0":
        rmsg = "Error en el proceso";
        break;
      case "1":
        rmsg = "Acción realizada correctamente";
        break;
      case "2":
        rmsg =
          "No se pudo activar, modifique el contable en la parte de edición.";
        break;
      default:
        rmsg = "Error al guardar en la base de datos.";
    }
    alertify
      .alert()
      .setting({
        title: "Servitaxis Sitio 152",
        closable: false,
        message: rmsg,
        onok: function () {
          let myUrl = window.location.origin;
          myUrl = `${myUrl}/listaOperador.php`;
          $(location).attr("href", myUrl);
        },
      })
      .show();
  }

  const admin = $("#admin").val();
  $.ajax({
    type: "post",
    url: "php/ajax/listadoOperador.php",
    data: { myVal: admin },
  })
    .done(function (r) {
      R = $.parseJSON(r);
      console.log(R);
      if (R[0] != 0) {
        $.each(R, function (indexInArray, valueOfElement) {
          const {
            idOperador,
            nombreCompleto,
            contable,
            referenciado,
            celular,
            telefono,
            domicilio,
            correo,
            estado,
            vigenciaLicencia,
          } = valueOfElement;
          const append = `<tr ${
            vigenciaLicencia == "Vencida" ? 'class="table-danger"' : ""
          }>
                    <td>${nombreCompleto}</td>
                    <td>${contable}</td>
                    <td>${referenciado}</td>
                    <td>${celular}</td>
                    <td>${telefono === null ? "" : telefono}</td>
                    <td>${domicilio === null ? "" : domicilio}</td>
                    <td>${correo}</td>
                    <td>${vigenciaLicencia}</td>
                    <td>${estado == "A" ? "Activo" : "Inactivo"}</td>
                    <td><button value="${idOperador}-${
            estado == "A" ? "I" : "A"
          }-${contable}" name="myButton" class="btn ${
            estado == "A" ? "btn-warning" : "btn-success"
          }">${estado == "A" ? "Desactivar" : "Activar"}</button></td>
                </tr>`;
          $("#operadorList tbody").append(append);
        });
        $("#operadorList").DataTable({
          language: {
            url: "//cdn.datatables.net/plug-ins/9dcbecd42ad/i18n/Spanish.json",
          },
        });
        $("#operadorList").tableExport({
          formats: ["csv", "txt"],
        });
      }
    })
    .fail(function () {
      alertify.alert("Warning!", "Error al cargar los datos");
    });
});
