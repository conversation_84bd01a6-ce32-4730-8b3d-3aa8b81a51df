<?php
include_once("../conexion.php");
include_once("../configDB.php");
include_once("../classAbono.php");

if (isset($_POST['operador'])) {

    $taxiConect = new foo_mysqli("localhost", USER, PASS, DBNAME);
    $taxiConect->query("SET NAMES utf8");

    $conductor = new abono();
    $conductor->operador = $_POST['operador'];
    $eq = $conductor->aniosKardex($taxiConect);

    $aux = array();
    if ($eq) {
        while ($row = $eq->fetch_array(MYSQLI_ASSOC)) {
            array_push($aux, $row);
        }
    } else {
        $aux = array(0);
    }

    $taxiConect->close();
} else {
    $aux = array(0);
}

echo json_encode($aux);
