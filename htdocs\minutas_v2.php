<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sistema de Gestión de Minutas v2</title>
    <script src="https://unpkg.com/react@18.2.0/umd/react.production.min.js"></script>
    <script src="https://unpkg.com/react-dom@18.2.0/umd/react-dom.production.min.js"></script>
    <script src="https://unpkg.com/jspdf@2.5.1/dist/jspdf.umd.min.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/flatpickr@4.6.13/dist/flatpickr.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/flatpickr@4.6.13/dist/flatpickr.min.js"></script>
    <style>
        .form-label { font-weight: bold; }
        .card { margin-bottom: 1.5rem; }
        .minuta-id-display {
            background: #e3f2fd;
            border: 2px solid #2196f3;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            text-align: center;
        }
        .minuta-id-display h3 {
            color: #1976d2;
            margin: 0;
        }
        .nav-tabs .nav-link.active {
            background-color: #0d6efd;
            color: white;
        }
    </style>
</head>
<body>
    <div id="root"></div>
    
    <script type="text/javascript">
        const { useState, useEffect, useRef } = React;
        const { jsPDF } = window.jspdf;

        const MinutaApp = () => {
            const [activeTab, setActiveTab] = useState('nueva');
            const [currentMinutaId, setCurrentMinutaId] = useState('');
            const [isLoading, setIsLoading] = useState(false);
            const [message, setMessage] = useState({ type: '', text: '' });
            
            // Referencias para los inputs de fecha
            const fechaHoraRef = useRef(null);
            const proximaReunionRef = useRef(null);
            
            const [formData, setFormData] = useState({
                fechaHora: '',
                lugar: '',
                duracion: '',
                convoca: '',
                antecedentes: '',
                objetivo: '',
                relator: '',
                ordenDia: '',
                proximaReunion: '',
                asistentes: [{ nombre: '', area: '', extensionCorreo: '', firma: '' }],
                asuntosTratados: [''],
                acuerdos: [{ descripcion: '', responsable: '', fechaAprox: '' }]
            });

            // Generar nuevo ID al cargar
            useEffect(() => {
                if (activeTab === 'nueva') {
                    generateNewId();
                }
            }, [activeTab]);

            // Inicializar date pickers después del render
            useEffect(() => {
                if (activeTab === 'nueva') {
                    const timer = setTimeout(() => {
                        initializeDatePickers();
                    }, 100);
                    
                    return () => clearTimeout(timer);
                }
            }, [activeTab]);

            const initializeDatePickers = () => {
                // Destruir pickers existentes
                if (fechaHoraRef.current && fechaHoraRef.current._flatpickr) {
                    fechaHoraRef.current._flatpickr.destroy();
                }
                if (proximaReunionRef.current && proximaReunionRef.current._flatpickr) {
                    proximaReunionRef.current._flatpickr.destroy();
                }

                // Inicializar picker principal
                if (fechaHoraRef.current) {
                    flatpickr(fechaHoraRef.current, {
                        enableTime: true,
                        dateFormat: 'd/m/Y H:i',
                        onChange: (selectedDates, dateStr) => {
                            setFormData(prev => ({ ...prev, fechaHora: dateStr }));
                        }
                    });
                }

                // Inicializar picker de próxima reunión
                if (proximaReunionRef.current) {
                    flatpickr(proximaReunionRef.current, {
                        enableTime: true,
                        dateFormat: 'd/m/Y H:i',
                        onChange: (selectedDates, dateStr) => {
                            setFormData(prev => ({ ...prev, proximaReunion: dateStr }));
                        }
                    });
                }
            };

            const generateNewId = async () => {
                try {
                    const response = await fetch('api/minutas.php?action=generate_id');
                    const data = await response.json();
                    if (data.success) {
                        setCurrentMinutaId(data.minuta_id);
                    }
                } catch (error) {
                    console.error('Error generando ID:', error);
                }
            };

            const saveMinuta = async () => {
                setIsLoading(true);
                setMessage({ type: '', text: '' });

                try {
                    const response = await fetch('api/minutas.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(formData)
                    });

                    const data = await response.json();

                    if (data.success) {
                        setMessage({ type: 'success', text: `Minuta guardada exitosamente con ID: ${data.minuta_id}` });
                        setTimeout(() => {
                            resetForm();
                            generateNewId();
                        }, 3000);
                    } else {
                        setMessage({ type: 'error', text: data.error || 'Error al guardar la minuta' });
                    }
                } catch (error) {
                    setMessage({ type: 'error', text: 'Error de conexión al servidor' });
                } finally {
                    setIsLoading(false);
                }
            };

            const resetForm = () => {
                setFormData({
                    fechaHora: '',
                    lugar: '',
                    duracion: '',
                    convoca: '',
                    antecedentes: '',
                    objetivo: '',
                    relator: '',
                    ordenDia: '',
                    proximaReunion: '',
                    asistentes: [{ nombre: '', area: '', extensionCorreo: '', firma: '' }],
                    asuntosTratados: [''],
                    acuerdos: [{ descripcion: '', responsable: '', fechaAprox: '' }]
                });
            };

            const handleInputChange = (e) => {
                setFormData({ ...formData, [e.target.name]: e.target.value });
            };

            const generatePDF = () => {
                const doc = new jsPDF();
                doc.setFontSize(16);
                doc.setFont("helvetica", "bold");
                doc.text('Reunión de Trabajo', 20, 20);
                doc.setFontSize(12);

                let y = 30;
                const fields = [
                    { label: 'ID Minuta:', value: currentMinutaId },
                    { label: 'Fecha y Hora:', value: formData.fechaHora },
                    { label: 'Lugar:', value: formData.lugar },
                    { label: 'Duración:', value: formData.duracion },
                    { label: 'Convoca:', value: formData.convoca },
                    { label: 'Relator:', value: formData.relator }
                ];

                fields.forEach(field => {
                    doc.setFont("helvetica", "bold");
                    doc.text(field.label, 20, y);
                    doc.setFont("helvetica", "normal");
                    doc.text(field.value || '', 60, y);
                    y += 10;
                });

                doc.save('minuta.pdf');
            };

            return React.createElement(
                'div',
                { className: 'container my-4' },
                React.createElement(
                    'h1',
                    { className: 'text-center mb-4 display-4' },
                    'Sistema de Gestión de Minutas v2'
                ),
                
                // Mostrar mensajes
                message.text && React.createElement(
                    'div',
                    { 
                        className: `alert ${message.type === 'success' ? 'alert-success' : 'alert-danger'} alert-dismissible fade show`,
                        role: 'alert'
                    },
                    message.text,
                    React.createElement(
                        'button',
                        {
                            type: 'button',
                            className: 'btn-close',
                            onClick: () => setMessage({ type: '', text: '' })
                        }
                    )
                ),

                // Mostrar ID de minuta
                currentMinutaId && React.createElement(
                    'div',
                    { className: 'minuta-id-display' },
                    React.createElement(
                        'h3',
                        null,
                        `ID de Minuta: ${currentMinutaId}`
                    )
                ),

                // Formulario
                React.createElement(
                    'div',
                    { className: 'card shadow' },
                    React.createElement(
                        'div',
                        { className: 'card-body' },
                        
                        // Fecha y Hora
                        React.createElement(
                            'div',
                            { className: 'mb-3' },
                            React.createElement(
                                'label',
                                { className: 'form-label' },
                                'Fecha y Hora *'
                            ),
                            React.createElement(
                                'input',
                                {
                                    ref: fechaHoraRef,
                                    type: 'text',
                                    name: 'fechaHora',
                                    value: formData.fechaHora,
                                    onChange: handleInputChange,
                                    className: 'form-control',
                                    placeholder: 'Seleccione fecha y hora...',
                                    required: true
                                }
                            )
                        ),

                        // Lugar
                        React.createElement(
                            'div',
                            { className: 'mb-3' },
                            React.createElement(
                                'label',
                                { className: 'form-label' },
                                'Lugar *'
                            ),
                            React.createElement(
                                'input',
                                {
                                    type: 'text',
                                    name: 'lugar',
                                    value: formData.lugar,
                                    onChange: handleInputChange,
                                    className: 'form-control',
                                    required: true
                                }
                            )
                        ),

                        // Duración
                        React.createElement(
                            'div',
                            { className: 'mb-3' },
                            React.createElement(
                                'label',
                                { className: 'form-label' },
                                'Duración *'
                            ),
                            React.createElement(
                                'select',
                                {
                                    name: 'duracion',
                                    value: formData.duracion,
                                    onChange: handleInputChange,
                                    className: 'form-select',
                                    required: true
                                },
                                React.createElement('option', { value: '' }, 'Seleccione duración...'),
                                ['0.5 horas', '1 hora', '1.5 horas', '2 horas', '2.5 horas', '3 horas'].map(option =>
                                    React.createElement('option', { key: option, value: option }, option)
                                )
                            )
                        ),

                        // Convoca
                        React.createElement(
                            'div',
                            { className: 'mb-3' },
                            React.createElement(
                                'label',
                                { className: 'form-label' },
                                'Convoca *'
                            ),
                            React.createElement(
                                'input',
                                {
                                    type: 'text',
                                    name: 'convoca',
                                    value: formData.convoca,
                                    onChange: handleInputChange,
                                    className: 'form-control',
                                    required: true
                                }
                            )
                        ),

                        // Relator
                        React.createElement(
                            'div',
                            { className: 'mb-3' },
                            React.createElement(
                                'label',
                                { className: 'form-label' },
                                'Relator *'
                            ),
                            React.createElement(
                                'input',
                                {
                                    type: 'text',
                                    name: 'relator',
                                    value: formData.relator,
                                    onChange: handleInputChange,
                                    className: 'form-control',
                                    required: true
                                }
                            )
                        ),

                        // Próxima Reunión
                        React.createElement(
                            'div',
                            { className: 'mb-3' },
                            React.createElement(
                                'label',
                                { className: 'form-label' },
                                'Fecha Próxima Reunión'
                            ),
                            React.createElement(
                                'input',
                                {
                                    ref: proximaReunionRef,
                                    type: 'text',
                                    name: 'proximaReunion',
                                    value: formData.proximaReunion,
                                    onChange: handleInputChange,
                                    className: 'form-control',
                                    placeholder: 'Seleccione fecha y hora...'
                                }
                            )
                        ),

                        // Botones
                        React.createElement(
                            'div',
                            { className: 'd-flex gap-2' },
                            React.createElement(
                                'button',
                                {
                                    onClick: saveMinuta,
                                    className: 'btn btn-success',
                                    disabled: isLoading
                                },
                                isLoading ? 'Guardando...' : 'Guardar en Base de Datos'
                            ),
                            React.createElement(
                                'button',
                                {
                                    onClick: generatePDF,
                                    className: 'btn btn-info'
                                },
                                'Exportar a PDF'
                            )
                        )
                    )
                )
            );
        };

        const root = ReactDOM.createRoot(document.getElementById('root'));
        root.render(React.createElement(MinutaApp));
    </script>
</body>
</html>
