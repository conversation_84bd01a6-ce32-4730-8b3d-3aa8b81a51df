$(document).ready(function () {
  if ($.get("res")) {
    Rsp = $.get("res");
    switch (Rsp) {
      case "0":
        rmsg = "Error en el proceso";
        break;
      case "1":
        rmsg = "Baja realizada correctamente";
        break;
    }
    alertify
      .alert()
      .setting({
        title: "Servitaxis Sitio 152",
        closable: false,
        message: rmsg,
        onok: function () {
          let myUrl = window.location.origin;
          myUrl = `${myUrl}/bajaPropietario.php`;
          $(location).attr("href", myUrl);
        },
      })
      .show();
  }
  $("#formBaja").validate();

  $("#divBtnBaja").hide();

  $("#owner")
    .miCatalogo("ownerlist", "Catalogo propietario")
    .change(function (e) {
      e.preventDefault();
      $("#divBtnBaja").show();
    });
});
