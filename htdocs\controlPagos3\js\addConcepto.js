$(document).ready(function () {
  $("#concepto").cambiaMayus();

  if ($.get("resp")) {
    resp = $.get("resp");
    switch (resp) {
      case "0":
        msg = "Erro<PERSON> al guardar, intente nuevamente.";
        break;
      case "1":
        msg = "Concepto guardado satisfacctoriamente";
        break;
    }
    alertify.alert("Servitaxis Sitio 152", msg);
  }

  $("#removeImporte")
    .attr("disabled", true)
    .click(function () {
      $("#renglones").children("div:last-child").remove();
      $("#renglones").children("br:last-child").remove();

      arrayControl = $("#renglones").children("div").length;
      if (arrayControl == 1) {
        $("#removeImporte").attr("disabled", true);
      }
    });

  $("#addIporte").click(function () {
    $("#removeImporte").attr("disabled", false);
    arrayControl = $("#renglones").children("div").length;
    auxCount = arrayControl + 1;

    $("#renglones").append(
      '<div class="row justify-content-center">' +
        '<div class="col-lg-4"><hr>' +
        '<label for="importe' +
        auxCount +
        '">' +
        "<h5>Importe:</h5>" +
        "</label>" +
        '<div class="input-group mb-3">' +
        '<span class="input-group-text">$</span>' +
        '<input type="number" name="importe[]" id="importe' +
        auxCount +
        '" class="form-control" aria-label="Amount (to the nearest dollar)">' +
        '<span class="input-group-text">.00</span>' +
        "</div>" +
        "</div>" +
        "</div>"
    );
  });

  const admin = $("#admin").val();
  $.ajax({
    type: "post",
    url: "php/ajax/listadoConcepto.php",
    data: { myVal: admin },
  })
    .done(function (r) {
      R = $.parseJSON(r);
      if (R[0] != 0) {
        $.each(R, function (indexInArray, valueOfElement) {
          const { Concepto, Montos } = valueOfElement;
          const append = `<tr>
                    <td>${Concepto}</td>
                    <td>${Montos}</td>
                </tr>`;
          $("#listaConcepto tbody").append(append);
        });
        $("#listaConcepto").DataTable({
          language: {
            url: "//cdn.datatables.net/plug-ins/9dcbecd42ad/i18n/Spanish.json",
          },
        });
        $("#listaConcepto").tableExport({
          formats: ["csv", "txt"],
        });
      }
    })
    .fail(function () {
      alertify.alert("Warning!", "Error al cargar los datos");
    });
});
