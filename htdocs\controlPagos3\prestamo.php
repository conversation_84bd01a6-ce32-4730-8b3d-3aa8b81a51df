<?php include_once('php/session.php'); ?>
<!DOCTYPE html>
<html>

<head>
    <meta charset="UTF-8">

    <?php include_once("includes/head.html"); ?>

    <script src="js/prestamo.js"></script>

</head>

<body class="bg-light">
    <div class="container">
        <br>
        <div id="header"></div>

        <br>
        <?php include_once("includes/menu.html"); ?>

        <br>
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="card border-success mb-3" style="max-width: 100%;">
                    <div class="card-header bg-transparent border-success">
                        <h5 class="card-title">Busqueda de recibos</h5>
                        <form id="busqueda" autocomplete="off">
                            <div class="row justify-content-center">
                                <div class="col-lg-6">
                                    <div class="input-group mb-3">
                                        <input type="text" class="form-control" id="buscadorOp" name="buscadorOp" required placeholder="Buscador de Operador" aria-label="Buscador de Operador" aria-describedby="button-addon2">
                                        <button class="btn btn-outline-primary">Buscar</button>
                                    </div>
                                    <label id="buscadorOp-error" class="error" for="buscadorOp"></label>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="card-body border-success">
                        <div class="row justify-content-center">
                            <div class="col-lg-5">
                                <h5>Operador</h5>
                                <h4 id="operador"></h4>
                            </div>
                            <div class="col-lg-2">
                                <h5>Contable</h5>
                                <h4 id="contable"></h4>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer bg-transparent border-succes">
                        <form method="POST" action="php/savePrestamo.php" id="prestamoData" autocomplete="off">
                            <input type="hidden" id="operadorID" name="operadorID">
                            <div class="mt-4 row justify-content-center">
                                <div class="col-lg-3">
                                    <div class="form-group">
                                        <label for="monto">
                                            <h5>Cantidad</h5>
                                        </label>
                                        <div class="input-group mb-3">
                                            <span class="input-group-text">$</span>
                                            <input type="number" class="form-control" name="monto" id="monto" required>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-3">
                                    <div class="form-group">
                                        <label for="noCheque">
                                            <h5>Número de Cheque</h5>
                                        </label>
                                        <input type="number" class="form-control" minlength="7" maxlength="7" name="noCheque" id="noCheque" required>
                                    </div>
                                </div>
                                <div class="col-lg-3">
                                    <div class="form-group">
                                        <label for="fechaCheque">
                                            <h5>Fecha expedición cheque</h5>
                                        </label>
                                        <input class="form-control" name="fechaCheque" id="fechaCheque" required>
                                    </div>
                                </div>
                            </div>
                            <br>
                            <div class="row justify-content-center">
                                <div class="col-lg-4">
                                    <div class="mt-auto p-2 bd-highlight">
                                        <div class="d-grid gap-2">
                                            <button class="btn btn-lg btn-success">Guardar</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        <div id="footer"></div>
    </div>
</body>

</html>