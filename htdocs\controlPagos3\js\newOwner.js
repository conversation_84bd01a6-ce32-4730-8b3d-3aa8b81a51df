$(document).ready(function () {
  $("#nombre,#aPaterno,#aMaterno").cambiaMayus();

  if ($.get("resp")) {
    resp = $.get("resp");
    switch (resp) {
      case "0":
        msg = "E<PERSON><PERSON> al guardar, intente nuevamente.";
        break;
      case "1":
        msg = "Nuevo dueño guardado satisfacctoriamente";
        break;
    }
    alertify.alert("Servitaxis Sitio 152", msg);
  }

  $("#userForm").validate({
    rules: {
      correoOwner: {
        remote: "php/ajax/validaVariable.php",
      },
    },
    messages: {
      correoOwner: {
        remote: "El correo ya es utilizado por un dueño previo.",
      },
    },
  });
});
