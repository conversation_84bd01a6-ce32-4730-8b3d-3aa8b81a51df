<?php
include_once("../conexion.php");
include_once("../configDB.php");
include_once("../classProcesaVariable.php");
include_once("../classOwner.php");

if (isset($_POST['datoBusca'])) {

    $taxiConect = new foo_mysqli("localhost", USER, PASS, DBNAME);
    $taxiConect->query("SET NAMES utf8");
    $owner = new procesaVariable($_POST['datoBusca']);

    $buscado = new owner("", "", "", "", "", "");
    $buscado->identificador = $owner->valor;
    $aux = $buscado->dataOwner($taxiConect);

    $taxiConect->close();
} else {
    $aux = array(0);
}

echo json_encode($aux);
