<?php
include_once("conexion.php");
include_once("configDB.php");
include_once("classProcesaVariable.php");
include_once("classUsuario.php");

if (isset($_POST['nombre']) and isset($_POST['correoUser'])) {

    $nombre = new procesaVariable($_POST['nombre']);
    $aPaterno = new procesaVariable($_POST['aPaterno']);
    $aMaterno = new procesaVariable($_POST['aMaterno']);
    $telefono = new procesaVariable($_POST['telefono']);
    $correo = new procesaVariable($_POST['correoUser']);
    $tipoUser = new procesaVariable($_POST['tipoUser']);
    $userName = new procesaVariable($_POST['userName']);
    $passwd = new procesaVariable($_POST['passwd']);

    $mailUser = strtolower($correo->valor);

    $taxiConect = new foo_mysqli("localhost", USER, PASS, DBNAME);
    $taxiConect->query("SET NAMES utf8");

    $aux =  $taxiConect->iniciaTransaccion();

    $myUser = new usuario($nombre->valor,
    $aPaterno->valor,
    $aMaterno->valor,
    $userName->valor,
    $passwd->valor,
    $mailUser,
    $tipoUser->valor,'A',
    $telefono->valor);
    if($myUser->saveUsuario($taxiConect)){
        if(!$myUser->saveLogin($taxiConect)){
            $aux = 0;
        }
    }
    else{
        $aux = 0;
    }

    $var = $taxiConect->finTransaccion($aux,'1','0');
    $taxiConect->resetTabla($aux,'idUsuario','usuario');

    $taxiConect->close();
} else {
    $var = '0';
}

header("Location: ../newUser.php?resp=$var");
