-- =====================================================
-- ACTUALIZACIONES PARA GESTIÓN DE TEMAS
-- Sistema de Actividades
-- =====================================================

USE minutas_db;

-- Agregar campos adicionales a la tabla de temas si no existen
ALTER TABLE actividades_temas 
ADD COLUMN IF NOT EXISTS color VARCHAR(7) DEFAULT '#3498db' COMMENT 'Color hexadecimal para el tema',
ADD COLUMN IF NOT EXISTS icono VARCHAR(50) DEFAULT 'fas fa-tag' COMMENT 'Clase de icono FontAwesome',
ADD COLUMN IF NOT EXISTS orden INT DEFAULT 999 COMMENT 'Orden de visualización',
ADD COLUMN IF NOT EXISTS usuario_creacion VARCHAR(255) COMMENT 'Usuario que creó el tema';

-- Actualizar temas existentes con colores e iconos
UPDATE actividades_temas SET 
    color = '#e74c3c', icono = 'fas fa-code', orden = 1 
WHERE nombre = 'Desarrollo de Software';

UPDATE actividades_temas SET 
    color = '#3498db', icono = 'fas fa-users', orden = 2 
WHERE nombre = 'Reuniones';

UPDATE actividades_temas SET 
    color = '#f39c12', icono = 'fas fa-file-alt', orden = 3 
WHERE nombre = 'Documentación';

UPDATE actividades_temas SET 
    color = '#9b59b6', icono = 'fas fa-graduation-cap', orden = 4 
WHERE nombre = 'Capacitación';

UPDATE actividades_temas SET 
    color = '#e67e22', icono = 'fas fa-tools', orden = 5 
WHERE nombre = 'Soporte Técnico';

UPDATE actividades_temas SET 
    color = '#2ecc71', icono = 'fas fa-chart-line', orden = 6 
WHERE nombre = 'Planificación';

UPDATE actividades_temas SET 
    color = '#1abc9c', icono = 'fas fa-bug', orden = 7 
WHERE nombre = 'Revisión y Testing';

UPDATE actividades_temas SET 
    color = '#34495e', icono = 'fas fa-wrench', orden = 8 
WHERE nombre = 'Mantenimiento';

UPDATE actividades_temas SET 
    color = '#8e44ad', icono = 'fas fa-search', orden = 9 
WHERE nombre = 'Investigación';

UPDATE actividades_temas SET 
    color = '#95a5a6', icono = 'fas fa-briefcase', orden = 10 
WHERE nombre = 'Administración';

UPDATE actividades_temas SET 
    color = '#16a085', icono = 'fas fa-bullhorn', orden = 11 
WHERE nombre = 'Comunicación';

UPDATE actividades_temas SET 
    color = '#7f8c8d', icono = 'fas fa-ellipsis-h', orden = 12 
WHERE nombre = 'Otros';

-- Procedimiento para crear nuevo tema
DELIMITER //
CREATE OR REPLACE PROCEDURE CrearNuevoTema(
    IN p_nombre VARCHAR(100),
    IN p_descripcion TEXT,
    IN p_color VARCHAR(7),
    IN p_icono VARCHAR(50),
    IN p_usuario VARCHAR(255)
)
BEGIN
    DECLARE tema_existe INT DEFAULT 0;
    DECLARE nuevo_orden INT DEFAULT 999;
    
    -- Verificar si el tema ya existe
    SELECT COUNT(*) INTO tema_existe 
    FROM actividades_temas 
    WHERE nombre = p_nombre AND activo = 1;
    
    IF tema_existe > 0 THEN
        SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = 'El tema ya existe';
    ELSE
        -- Obtener el siguiente orden
        SELECT COALESCE(MAX(orden), 0) + 1 INTO nuevo_orden 
        FROM actividades_temas;
        
        -- Insertar nuevo tema
        INSERT INTO actividades_temas (
            nombre, 
            descripcion, 
            color, 
            icono, 
            orden, 
            usuario_creacion, 
            activo
        ) VALUES (
            p_nombre, 
            p_descripcion, 
            COALESCE(p_color, '#3498db'), 
            COALESCE(p_icono, 'fas fa-tag'), 
            nuevo_orden, 
            p_usuario, 
            1
        );
        
        SELECT 'Tema creado exitosamente' as mensaje, LAST_INSERT_ID() as tema_id;
    END IF;
END //
DELIMITER ;

-- Procedimiento para desactivar tema
DELIMITER //
CREATE OR REPLACE PROCEDURE DesactivarTema(
    IN p_tema_id INT,
    IN p_usuario VARCHAR(255)
)
BEGIN
    DECLARE actividades_count INT DEFAULT 0;
    
    -- Verificar si hay actividades usando este tema
    SELECT COUNT(*) INTO actividades_count 
    FROM actividades a
    INNER JOIN actividades_temas t ON a.tema = t.nombre
    WHERE t.id = p_tema_id;
    
    IF actividades_count > 0 THEN
        SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = 'No se puede desactivar el tema porque tiene actividades asociadas';
    ELSE
        UPDATE actividades_temas 
        SET activo = 0 
        WHERE id = p_tema_id;
        
        SELECT 'Tema desactivado exitosamente' as mensaje;
    END IF;
END //
DELIMITER ;

-- Vista mejorada para temas
CREATE OR REPLACE VIEW vista_temas AS
SELECT 
    t.id,
    t.nombre,
    t.descripcion,
    t.color,
    t.icono,
    t.orden,
    t.activo,
    t.fecha_creacion,
    t.usuario_creacion,
    COUNT(a.id) as total_actividades,
    COUNT(CASE WHEN a.progreso = 'Vigente' THEN 1 END) as actividades_vigentes,
    COUNT(CASE WHEN a.progreso = 'Completado' THEN 1 END) as actividades_completadas
FROM actividades_temas t
LEFT JOIN actividades a ON t.nombre = a.tema
GROUP BY t.id, t.nombre, t.descripcion, t.color, t.icono, t.orden, t.activo, t.fecha_creacion, t.usuario_creacion
ORDER BY t.orden ASC, t.nombre ASC;

-- Mostrar resultado
SELECT 'Actualizaciones de temas aplicadas exitosamente' as mensaje;
SHOW TABLES LIKE '%actividades%';
