<?php
include_once("conexion.php");
include_once("configDB.php");
include_once("classProcesaVariable.php");
include_once("classCargo.php");
include_once("session.php");

if (isset($_POST['monto']) and isset($_POST['operadorID'])) {

    $monto = new procesaVariable($_POST['monto']);
    $noCheque = new procesaVariable($_POST['noCheque']);
    $operador = new procesaVariable($_POST['operadorID']);
    $fechaCheque = new procesaVariable($_POST['fechaCheque']);

    $usuario = $_SESSION['idUser'];
    $tipoUsuario = $_SESSION['userType'];

    $taxiConect = new foo_mysqli("localhost", USER, PASS, DBNAME);
    $taxiConect->query("SET NAMES utf8");

    $var = $taxiConect->iniciaTransaccion();

    $prestamo = new cargo('P', $monto->valor, $operador->valor, $noCheque->valor, $usuario, $tipoUsuario);
    if ($prestamo->saveCargo($taxiConect)) {
        $prestamo->fechaCheque = $fechaCheque->valor;
        if ($prestamo->updatePresatamo($taxiConect)) {
            $var = 1;
        } else {
            $var = 0;
        }
    } else {
        $var = 0;
    }

    $link = $taxiConect->finTransaccion($var, "../pagarePrint.php?con=$noCheque->valor&p=1", '../prestamo.php?resp=0');

    $taxiConect->close();
} else {
    $var = '0';
}

header("Location: $link");
