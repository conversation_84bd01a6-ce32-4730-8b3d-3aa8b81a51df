<?php
include_once("conexion.php");
include_once("configDB.php");
include_once("classProcesaVariable.php");
include_once("classEconomico.php");
include_once("classSeguro.php");

if (isset($_POST['owner']) and isset($_POST['poliza'])) {

    $owner = new procesaVariable($_POST['owner']);
    $marca = new procesaVariable($_POST['marca']);
    $modelo = new procesaVariable($_POST['modelo']);
    $placa = new procesaVariable($_POST['placa']);
    $anio = new procesaVariable($_POST['anio']);
    $aseguradora = new procesaVariable($_POST['aseguradora']);
    $poliza = new procesaVariable($_POST['poliza']);
    $vigenciaBegin = new procesaVariable($_POST['vigenciaBegin']);
    $vigenciaEnd = new procesaVariable($_POST['vigenciaEnd']);
    $noSerie = new procesaVariable($_POST['niv']);
    $economico = new procesaVariable($_POST['economico']);
    $propietarioAuto = new procesaVariable($_POST['propietarioAuto']);
    $motor = new procesaVariable($_POST['motor']);

    $taxiConect = new foo_mysqli("localhost", USER, PASS, DBNAME);
    $taxiConect->query("SET NAMES utf8");

    $var = $taxiConect->iniciaTransaccion();

    $auto = new economico(
        $owner->valor,
        $marca->valor,
        $modelo->valor,
        $placa->valor,
        $anio->valor,
        $noSerie->valor,
        $economico->valor,
        'A',
        $propietarioAuto->valor,
        $motor->valor
    );

    if ($auto->save($taxiConect)) {
        $seguroAuto =  new seguro($auto->identificador, $aseguradora->valor, $poliza->valor, $vigenciaBegin->valor, $vigenciaEnd->valor);
        if ($seguroAuto->save($taxiConect)) {
            $var = 1;
        } else {
            $var = 0;
        }
    } else {
        $var = '0';
    }

    $link = $taxiConect->finTransaccion($var, '1', '0');

    $taxiConect->resetTabla($var, 'idSeguro', 'seguro');
    $taxiConect->resetTabla($var, 'idEconomico', 'economico');

    $taxiConect->close();
} else {
    $var = '0';
}

header("Location: ../newEconomico.php?resp=$var");
