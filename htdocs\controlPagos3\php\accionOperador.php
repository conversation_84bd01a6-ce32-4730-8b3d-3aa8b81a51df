<?php
include_once("conexion.php");
include_once("configDB.php");
include_once("classProcesaVariable.php");
include_once("classOperador.php");

if (isset($_POST['admin'])) {
    $idOperador = new procesaVariable($_POST['myButton']);

    $taxiConect = new foo_mysqli("localhost", USER, PASS, DBNAME);
    $taxiConect->query("SET NAMES utf8");

    $data = explode("-", $idOperador->valor);
    $conductor =  new operador($data[1]);
    $conductor->identificador = $data[0];
    $conductor->contable = $data[2];

    if ($data[1] == 'A' and $conductor->validaContable($taxiConect)) {
        if ($conductor->activaDesactiva($taxiConect)) {
            $var = 1;
        } else {
            $var = '0';
        }
    } elseif ($data[1] == 'I') {
        if ($conductor->activaDesactiva($taxiConect)) {
            $var = 1;
        } else {
            $var = '0';
        }
    } else {
        $var = 2;
    }

    $taxiConect->close();
} else {
    $var = '0';
}
header("Location: ../listaOperador.php?res=$var");
