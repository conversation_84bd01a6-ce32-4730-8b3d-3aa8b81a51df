<?php include_once('php/session.php'); ?>
<!DOCTYPE html>
<html>

<head>
    <meta charset="UTF-8">
    <?php include_once("includes/head.html"); ?>
    <script src="js/kardex.js"></script>

</head>

<body class="bg-light">
    <div class="container">
        <br>
        <div id="header"></div>
        <?php include_once("includes/menu.html"); ?>

        <br>
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="card border-success mb-3" style="max-width: 100%;">
                    <div class="card-header bg-info p-2 text-dark">
                        <h3>Kardex</h3>
                    </div>
                    <div class="card-body">
                        <form method="POST" action="php/generaKardex.php" id="formNewPass">
                            <div class="row justify-content-center text-center">
                                <h4>Seleccione operador</h4>
                            </div>
                            <div class="row mt-4 justify-content-center">
                                <div class="col-lg-4">
                                    <label for="operador">
                                        <h5>Operador:</h5>
                                    </label>
                                    <select class="form-control" name="operador" id="operador" required>
                                        <option value="">Selecione una opción ...</option>
                                    </select>
                                </div>
                            </div>
                            <div class="row mt-4 justify-content-center" id="divAnio">
                                <!-- <div class="col-lg-3">
                                    <label for="owner">
                                        <h5>Año:</h5>
                                    </label>
                                    <select class="form-control" name="anio" id="anio" required>
                                        <option value="">Selecione una opción ...</option>
                                    </select>
                                </div> -->

                                <div class="col-lg-3">
                                    <label for="beginDate">
                                        <h5>Desde:</h5>
                                    </label>
                                    <input class="form-control" name="beginDate" id="beginDate" required readonly>
                                </div>
                                <div class="col-lg-3">
                                    <label for="endDate">
                                        <h5>Hasta:</h5>
                                    </label>
                                    <input class="form-control" name="endDate" id="endDate" required readonly>
                                </div>
                            </div>

                            <div class="row justify-content-center mt-4" id="divGenera">
                                <div class="col-lg-3">
                                    <div class="d-grid gap-2">
                                        <button type="submit" class="btn btn-success">Generar</button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        <div id="footer"></div>
    </div>
</body>

</html>