<?php
include_once("../conexion.php");
include_once("../configDB.php");
include_once("../classAbono.php");
include_once("../classImporte.php");

if (isset($_POST['concepto'])) {

	$taxiConect = new foo_mysqli("localhost", USER, PASS, DBNAME);
	$taxiConect->query("SET NAMES utf8");

	$concepto = $_POST['concepto'];
	$idOperado = $_POST['idOperado'];

	$importe = new importe($concepto,'A');	

	$aux = array('x');

	$aux = $importe->buscaImporte($taxiConect, $aux);

	if ($aux[0] != 0) {
		$transac = new abono();
		$transac->concepto = $concepto;
		$transac->operador = $idOperado;
		$transac->ultimaSemana($taxiConect);

		$aux['semana'] = $transac->semana;
		$aux['anio'] = $transac->anio;;

		$transac->siguienteSemana();

		$aux['semanaSig'] = $transac->semana;
		$aux['anioSemana'] = $transac->anio;

		if(isset($_POST['importeA'])){
			$viejoImporte = new importe($concepto,'I');
			$aux = $viejoImporte->ultimoImporteDesactivado($taxiConect,$aux);
		}
	}

	$taxiConect->close();
} else {
	$aux = array(0);
}

echo json_encode($aux);
