<?php
include_once("conexion.php");
include_once("configDB.php");
include_once("classProcesaVariable.php");
include_once("classAbono.php");
include_once("classRecibo.php");

if (isset($_POST['dateDeposito']) and isset($_POST['movimiento'])) {


    $recibo = new procesaVariable($_POST['reciboHide']);
    $dateDeposito = new procesaVariable($_POST['dateDeposito']);
    $movimiento = new procesaVariable($_POST['movimiento']);

    $taxiConect = new foo_mysqli("localhost", USER, PASS, DBNAME);
    $taxiConect->query("SET NAMES utf8");

    $q = "UPDATE recibo SET 
        fechaDeposito = '$dateDeposito->valor',
        movimiento = '$movimiento->valor'
        WHERE idRecibo='$recibo->valor'";
    $eq = $taxiConect->query($q);

    $link = "../fechaDeposito.php?resp=1";

    if (!$eq) {
        $link = "../fechaDeposito.php?resp=0";
    }

    $taxiConect->close();
} else {
    $link = "../fechaDeposito.php?resp=0";
}

header("Location: $link");
