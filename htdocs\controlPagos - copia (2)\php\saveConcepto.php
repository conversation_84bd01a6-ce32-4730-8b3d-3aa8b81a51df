<?php
include_once("conexion.php");
include_once("configDB.php");
include_once("classProcesaVariable.php");
include_once("classImporte.php");

if (isset($_POST['concepto'])) {
    $concepto = new procesaVariable($_POST['concepto']);
    $importe = new procesaVariable($_POST['importe']);

    $taxiConect = new foo_mysqli("localhost", USER, PASS, DBNAME);
    $taxiConect->query("SET NAMES utf8");

    // Iniciar transacción
    $transac = $taxiConect->iniciaTransaccion();

    // Primero guardar el concepto
    $insert = "INSERT INTO concepto(idConcepto, descripcion) VALUES (NULL, '$concepto->valor')";
    $exeInsert = $taxiConect->query($insert);
    
    if ($exeInsert) {
        $idConcepto = $taxiConect->insert_id;
        
        // Luego guardar los importes asociados al concepto
        $importes = $importe->valor;
        foreach ($importes as $m) {
            if (!empty($m)) { // Solo guardar importes con valor
                $nuevoImporte = new importe($idConcepto, 'A', $m);
                if (!$nuevoImporte->nuevoImporte($taxiConect, $transac)) {
                    $transac = 0;
                    break;
                }
            }
        }
    } else {
        $transac = 0;
    }

    // Finalizar transacción
    $link = $taxiConect->finTransaccion($transac, '../addConcepto.php?resp=1', '../addConcepto.php?resp=0');

    $taxiConect->close();
} else {
    $link = '../addConcepto.php?resp=0';
}

header("Location: $link");
