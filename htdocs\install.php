<?php
/**
 * Script de instalación para el Sistema de Gestión de Minutas
 * Este archivo ayuda a verificar la configuración y crear la base de datos
 */

// Configuración de la base de datos
$config = [
    'host' => 'localhost',
    'username' => 'root',
    'password' => '',
    'database' => 'minutas_db'
];

$errors = [];
$success = [];

// Función para conectar a MySQL sin especificar base de datos
function connectToMySQL($config) {
    try {
        $pdo = new PDO(
            "mysql:host=" . $config['host'],
            $config['username'],
            $config['password'],
            [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
        );
        return $pdo;
    } catch (PDOException $e) {
        return false;
    }
}

// Función para verificar si la base de datos existe
function databaseExists($pdo, $dbname) {
    try {
        $stmt = $pdo->prepare("SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = ?");
        $stmt->execute([$dbname]);
        return $stmt->rowCount() > 0;
    } catch (PDOException $e) {
        return false;
    }
}

// Procesar formulario de instalación
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $config['host'] = $_POST['host'] ?? 'localhost';
    $config['username'] = $_POST['username'] ?? 'root';
    $config['password'] = $_POST['password'] ?? '';
    $config['database'] = $_POST['database'] ?? 'minutas_db';
    
    // Intentar conectar a MySQL
    $pdo = connectToMySQL($config);
    
    if (!$pdo) {
        $errors[] = "No se pudo conectar a MySQL. Verificar credenciales.";
    } else {
        $success[] = "Conexión a MySQL exitosa.";
        
        // Verificar si la base de datos existe
        if (!databaseExists($pdo, $config['database'])) {
            // Crear base de datos
            try {
                $pdo->exec("CREATE DATABASE `{$config['database']}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
                $success[] = "Base de datos '{$config['database']}' creada exitosamente.";
            } catch (PDOException $e) {
                $errors[] = "Error creando base de datos: " . $e->getMessage();
            }
        } else {
            $success[] = "Base de datos '{$config['database']}' ya existe.";
        }
        
        // Conectar a la base de datos específica
        try {
            $pdo->exec("USE `{$config['database']}`");
            
            // Leer y ejecutar el script SQL
            $sqlFile = __DIR__ . '/database/create_tables.sql';
            if (file_exists($sqlFile)) {
                $sql = file_get_contents($sqlFile);
                
                // Remover comentarios y dividir en statements
                $sql = preg_replace('/--.*$/m', '', $sql);
                $statements = array_filter(array_map('trim', explode(';', $sql)));
                
                foreach ($statements as $statement) {
                    if (!empty($statement) && !preg_match('/^(CREATE DATABASE|USE)/i', $statement)) {
                        try {
                            $pdo->exec($statement);
                        } catch (PDOException $e) {
                            // Ignorar errores de "tabla ya existe"
                            if (strpos($e->getMessage(), 'already exists') === false) {
                                $errors[] = "Error ejecutando SQL: " . $e->getMessage();
                            }
                        }
                    }
                }
                
                $success[] = "Tablas creadas/verificadas exitosamente.";
                
                // Actualizar archivo de configuración
                $configContent = "<?php\n";
                $configContent .= "/**\n * Configuración de la base de datos para el sistema de minutas\n */\n\n";
                $configContent .= "class Database {\n";
                $configContent .= "    private \$host = '{$config['host']}';\n";
                $configContent .= "    private \$db_name = '{$config['database']}';\n";
                $configContent .= "    private \$username = '{$config['username']}';\n";
                $configContent .= "    private \$password = '{$config['password']}';\n";
                $configContent .= "    private \$conn;\n\n";
                
                // Agregar el resto del contenido de la clase Database
                $originalConfig = file_get_contents(__DIR__ . '/config/database.php');
                $classContent = preg_replace('/class Database \{.*?private \$conn;/s', '', $originalConfig);
                $classContent = preg_replace('/^<\?php.*?\*\//s', '', $classContent);
                $configContent .= trim($classContent);
                
                if (file_put_contents(__DIR__ . '/config/database.php', $configContent)) {
                    $success[] = "Archivo de configuración actualizado.";
                } else {
                    $errors[] = "No se pudo actualizar el archivo de configuración.";
                }
                
            } else {
                $errors[] = "No se encontró el archivo create_tables.sql";
            }
            
        } catch (PDOException $e) {
            $errors[] = "Error conectando a la base de datos: " . $e->getMessage();
        }
    }
}

?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Instalación - Sistema de Gestión de Minutas</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container my-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h2 class="mb-0">Instalación del Sistema de Gestión de Minutas</h2>
                    </div>
                    <div class="card-body">
                        
                        <?php if (!empty($errors)): ?>
                            <div class="alert alert-danger">
                                <h5>Errores encontrados:</h5>
                                <ul class="mb-0">
                                    <?php foreach ($errors as $error): ?>
                                        <li><?php echo htmlspecialchars($error); ?></li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                        <?php endif; ?>
                        
                        <?php if (!empty($success)): ?>
                            <div class="alert alert-success">
                                <h5>Instalación exitosa:</h5>
                                <ul class="mb-0">
                                    <?php foreach ($success as $msg): ?>
                                        <li><?php echo htmlspecialchars($msg); ?></li>
                                    <?php endforeach; ?>
                                </ul>
                                <?php if (empty($errors)): ?>
                                    <hr>
                                    <p class="mb-0">
                                        <strong>¡Instalación completada!</strong><br>
                                        Ahora puedes acceder al sistema: 
                                        <a href="sistema_minutas.php" class="btn btn-primary btn-sm">Ir al Sistema</a>
                                    </p>
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>
                        
                        <form method="POST">
                            <div class="mb-3">
                                <label for="host" class="form-label">Servidor MySQL</label>
                                <input type="text" class="form-control" id="host" name="host" 
                                       value="<?php echo htmlspecialchars($config['host']); ?>" required>
                                <div class="form-text">Generalmente 'localhost' para XAMPP</div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="username" class="form-label">Usuario MySQL</label>
                                <input type="text" class="form-control" id="username" name="username" 
                                       value="<?php echo htmlspecialchars($config['username']); ?>" required>
                                <div class="form-text">Generalmente 'root' para XAMPP</div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="password" class="form-label">Contraseña MySQL</label>
                                <input type="password" class="form-control" id="password" name="password" 
                                       value="<?php echo htmlspecialchars($config['password']); ?>">
                                <div class="form-text">Dejar vacío si no tienes contraseña (XAMPP por defecto)</div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="database" class="form-label">Nombre de la Base de Datos</label>
                                <input type="text" class="form-control" id="database" name="database" 
                                       value="<?php echo htmlspecialchars($config['database']); ?>" required>
                                <div class="form-text">Se creará automáticamente si no existe</div>
                            </div>
                            
                            <button type="submit" class="btn btn-primary">Instalar Sistema</button>
                        </form>
                        
                        <hr>
                        
                        <h5>Requisitos del Sistema</h5>
                        <ul>
                            <li>PHP 7.4 o superior ✓</li>
                            <li>MySQL 5.7 o superior ✓</li>
                            <li>Extensión PDO MySQL ✓</li>
                            <li>Servidor web (Apache/Nginx) ✓</li>
                        </ul>
                        
                        <h5>Después de la Instalación</h5>
                        <ol>
                            <li>Acceder al sistema mediante <code>sistema_minutas.php</code></li>
                            <li>Crear tu primera minuta</li>
                            <li>Verificar que la exportación PDF/Word funcione</li>
                            <li>Eliminar este archivo <code>install.php</code> por seguridad</li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
