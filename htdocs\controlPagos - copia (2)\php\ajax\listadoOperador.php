<?php
include_once("../conexion.php");
include_once("../configDB.php");
include_once("../classUsuario.php");
include_once("../classOperador.php");

if (isset($_POST['myVal']) and $_POST['myVal'] == 8709011993) {

    $taxiConect = new foo_mysqli("localhost", USER, PASS, DBNAME);
    $taxiConect->query("SET NAMES utf8");

    $conductor = new operador('A');
    $eq = $conductor->operadorList($taxiConect);

    $aux = array();
    if ($eq->num_rows != 0) {
        while ($row = $eq->fetch_array(MYSQLI_ASSOC)) {
            array_push($aux, $row);
        }
    } else {
        $aux = array(0);
    }

    $taxiConect->close();
} else {
    $aux = array(0);
}

echo json_encode($aux);
