<?php include_once('php/session.php'); ?>
<!DOCTYPE html>
<html>

<head>
    <meta charset="UTF-8">
    <?php include_once("includes/head.html"); ?>
    <script src="js/editOperador.js"></script>

</head>

<body class="bg-light">
    <div class="container">
        <br>
        <div id="header"></div>
        <?php include_once("includes/menu.html"); ?>
        <div class="mt-4 row justify-content-center">
            <div class="col-lg-10">
                <div class="card border-success mb-3" style="max-width: 100%;">
                    <div class="card-header bg-info p-2 text-dark bg-opacity-10">
                        <h3>Editar Operador</h3>
                    </div>
                    <div class="card-body">
                        <input type="hidden" name="admin" value="8709011992">
                        <div class="row justify-content-center text-center">
                            <h4>Escribe un contable o nombre de operador</h4>
                        </div>
                        <div class="mt-4 row justify-content-center">
                            <div class="col-lg-8">
                                <form id="busqueda" autocomplete="off">
                                    <div class="row justify-content-center">
                                        <div class="col-lg-6">
                                            <div class="input-group mb-3">
                                                <input type="text" class="form-control" id="buscadorOp" name="buscadorOp" required placeholder="Buscador de Operador" aria-label="Buscador de Operador" aria-describedby="button-addon2">
                                                <button class="btn btn-outline-primary">Buscar</button>
                                            </div>
                                            <label id="buscadorOp-error" class="error" for="buscadorOp"></label>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                        <hr>
                        <form method="POST" action="php/editaOperador.php" id="editForm" autocomplete="off">                                                   
                            <div class="row mt-4 justify-content-center text-center">
                                <h4>Datos del Operador</h4>
                            </div>
                            <div class="row mt-4 justify-content-center">
                                <div class="col-lg-4">
                                    <label for="nombre">
                                        <h5>Nombre:</h5>
                                    </label>
                                    <input name="nombre" id="nombre" maxlength="30" class="form-control" required>
                                </div>
                                <div class="col-lg-4">
                                    <label for="aPaterno">
                                        <h5>Apellido Paterno:</h5>
                                    </label>
                                    <input class="form-control" maxlength="30" name="aPaterno" id="aPaterno" required>
                                </div>
                                <div class="col-lg-4">
                                    <label for="aMaterno">
                                        <h5>Apellido Materno:</h5>
                                    </label>
                                    <input class="form-control" maxlength="30" name="aMaterno" id="aMaterno" required>
                                </div>
                            </div>
                            <div class="row mt-4 justify-content-center">
                                <div class="col-lg-4">
                                    <label for="contable">
                                        <h5>Contable:</h5>
                                    </label>
                                    <input type="number" class="form-control" name="contable" id="contable" required>
                                </div>
                                <div class="col-lg-4">
                                    <label for="referenciado">
                                        <h5>Referenciado:</h5>
                                    </label>
                                    <input type="number" class="form-control" maxlength="7" minlength="7" name="referenciado" id="referenciado" required>
                                </div>
                                <div class="col-lg-4">
                                    <label for="celular">
                                        <h5>Celular:</h5>
                                    </label>
                                    <input type="number" class="form-control" min="1" maxlength="10" minlength="10" name="celular" id="celular" required>
                                </div>
                            </div>
                            <div class="row mt-4 justify-content-center">
                                <div class="col-lg-4">
                                    <label for="correo">
                                        <h5>Correo:</h5>
                                    </label>
                                    <input type="email" class="form-control" maxlength="80" name="correo" id="correo" required>
                                </div>
                                <div class="col-lg-4">
                                    <label for="telefono">
                                        <h5>Telefono de casa:</h5>
                                    </label>
                                    <input type="number" class="form-control" minlength="10" maxlength="10" name="telefono" id="telefono">
                                </div>
                                <div class="col-lg-4">
                                    <label for="direccion">
                                        <h5>Dirección:</h5>
                                    </label>
                                    <input class="form-control" maxlength="100" name="direccion" id="direccion">
                                </div>
                            </div>
                            <input type="hidden" name="idOperador" id="idOperador">
                            <div class="row mt-4 justify-content-center" id="divBtnBaja">
                                <div class="col-lg-4">
                                    <div class="d-grid gap-2">
                                        <button type="submit" class="btn btn-lg btn-warning">Guardar</button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        <div id="footer"></div>
    </div>
</body>

</html>