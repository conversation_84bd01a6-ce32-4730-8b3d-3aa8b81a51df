<?php include_once('php/session.php'); ?>
<!DOCTYPE html>
<html>

<head>
    <meta charset="UTF-8">
    <?php include_once("includes/head.html"); ?>
    <script src="js/reporteOperadorFechasVertical.js"></script>

</head>

<body class="bg-light">
    <div class="container">
        <br>
        <div id="header"></div>
        <?php include_once("includes/menu.html"); ?>

        <br>
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="card border-success mb-3" style="max-width: 100%;">
                    <div class="card-header bg-info bg-opacity-25 p-2 text-dark">
                        <h3>Registros por rango de fecha</h3>
                    </div>
                    <div class="card-body">
                        <form action="php/reporteOperadorFechas.php" method="POST" id="genReporte" autocomplete="off">
                            <input type="hidden" name="concepto" value="0">
                            <div class="row justify-content-center text-center">
                                <h4>Seleccione año y semana del reporte a generar</h4>
                            </div>
                            <div class="row mt-4 justify-content-center">
                                <div class="col-lg-4">
                                    <label for="operador">
                                        <h5>Operador:</h5>
                                    </label>
                                    <select class="form-control" name="operador" id="operador" required>
                                        <option value="">Selecione una opción ...</option>
                                    </select>
                                </div>
                            </div>
                            <div class="row mt-4 justify-content-center">
                                <div class="col-lg-3">
                                    <label for="anio">
                                        <h5>Desde:</h5>
                                    </label>
                                    <input name="beginDate" id="beginDate" required class="form-control" readonly>
                                </div>
                                <div class="col-lg-3">
                                    <label for="semana">
                                        <h5>Hasta:</h5>
                                    </label>
                                    <input name="endDate" id="endDate" required class="form-control" readonly>
                                </div>
                            </div>
                            <div class="row mt-4 justify-content-center">
                                <div class="col-lg-3 text-center">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" name="soloAbonos" id="soloAbonos" value="S">
                                        <label class="form-check-label" for="soloAbonos">
                                            <h5>Solo abonos pagados</h5>
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div class="row justify-content-center mt-4">
                                <div class="col-lg-3">
                                    <input type="hidden" name="tipoReporte" id="tipoReporte" value="2">
                                    <div class="d-grid gap-2">
                                        <button name="btnGenera" id="btnGenera" class="btn btn-lg btn-success">Generar reporte</button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        <div id="footer"></div>
    </div>
</body>

</html>