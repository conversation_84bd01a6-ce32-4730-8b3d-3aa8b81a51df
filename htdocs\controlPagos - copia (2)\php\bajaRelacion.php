<?php
include_once("conexion.php");
include_once("configDB.php");
include_once("classProcesaVariable.php");

// if (isset($_POST['idOperador']) and isset($_POST['idEconomico'])) {
if (isset($_POST['acctionList'])) {

    $data = new procesaVariable($_POST['acctionList']);
    $dataArray = explode("-", $data->valor);
    $idOperador = $dataArray[0];
    $idEconomico = $dataArray[1];

    $taxiConect = new foo_mysqli("localhost", USER, PASS, DBNAME);
    $taxiConect->query("SET NAMES utf8");

    $update = "UPDATE operadoreconomico SET estado = 'I' 
        WHERE idOperador = '$idOperador' 
        AND idEconomico = '$idEconomico';";

    $eUpdate = $taxiConect->query($update);

    if ($eUpdate) {
        $var = 1;
    } else {
        $var = 0;
    }

    $taxiConect->close();
} else {
    $var = '0';
}

header("Location: ../muestraRelacion.php?res=$var");
