<?php
if (isset($_POST['convenio'])) {
    $convenio = $_POST['convenio'];
    $tipoCargo = $_POST['tipoCargo'];

    ob_start();
    include_once('pagare.php');
    $content = ob_get_clean();

    include_once('../html2pdf/html2pdf.class.php');
    try {
        $html2pdf = new HTML2PDF('P', 'letter', 'es', true, 'UTF-8', 8);
        //$html2pdf->setDefaultFont('freemono');
        $html2pdf->pdf->SetDisplayMode('fullpage');
        $html2pdf->writeHTML($content, isset($_GET['vuehtml']));
        $html2pdf->Output("pagare$tipoCargo-$convenio.pdf", 'D');
    } catch (HTML2PDF_exception $e) {
        echo $e;
    }
} else {
    header("Location: salir.php");
}
