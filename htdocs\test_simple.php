<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Simple - Minutas</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/flatpickr@4.6.13/dist/flatpickr.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/flatpickr@4.6.13/dist/flatpickr.min.js"></script>
</head>
<body>
    <div class="container my-4">
        <h2>Test Simple - Crear Minuta</h2>
        
        <form id="minutaForm">
            <div class="mb-3">
                <label for="fechaHora" class="form-label">Fecha y Hora *</label>
                <input type="text" id="fechaHora" name="fechaHora" class="form-control" placeholder="Seleccione fecha y hora..." required>
            </div>
            
            <div class="mb-3">
                <label for="lugar" class="form-label">Lugar *</label>
                <input type="text" id="lugar" name="lugar" class="form-control" required>
            </div>
            
            <div class="mb-3">
                <label for="duracion" class="form-label">Duración *</label>
                <select id="duracion" name="duracion" class="form-select" required>
                    <option value="">Seleccione duración...</option>
                    <option value="1 hora">1 hora</option>
                    <option value="2 horas">2 horas</option>
                    <option value="3 horas">3 horas</option>
                </select>
            </div>
            
            <div class="mb-3">
                <label for="convoca" class="form-label">Convoca *</label>
                <input type="text" id="convoca" name="convoca" class="form-control" required>
            </div>
            
            <div class="mb-3">
                <label for="relator" class="form-label">Relator *</label>
                <input type="text" id="relator" name="relator" class="form-control" required>
            </div>
            
            <button type="submit" class="btn btn-primary">Guardar Minuta</button>
        </form>
        
        <div id="result" class="mt-4"></div>
    </div>

    <script>
        // Inicializar date picker
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM cargado, inicializando flatpickr...');
            
            const fechaInput = document.getElementById('fechaHora');
            if (fechaInput) {
                const picker = flatpickr(fechaInput, {
                    enableTime: true,
                    dateFormat: 'd/m/Y H:i',
                    onChange: function(selectedDates, dateStr, instance) {
                        console.log('Fecha seleccionada:', dateStr);
                    }
                });
                console.log('Flatpickr inicializado:', picker);
            } else {
                console.error('No se encontró el elemento fechaHora');
            }
        });

        // Manejar envío del formulario
        document.getElementById('minutaForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const data = {
                fechaHora: formData.get('fechaHora'),
                lugar: formData.get('lugar'),
                duracion: formData.get('duracion'),
                convoca: formData.get('convoca'),
                relator: formData.get('relator'),
                antecedentes: 'Test antecedentes',
                objetivo: 'Test objetivo',
                ordenDia: 'Test orden del día',
                proximaReunion: '',
                asistentes: [
                    {
                        nombre: 'Test Asistente',
                        area: 'Test Area',
                        extensionCorreo: '<EMAIL>',
                        firma: 'Test Firma'
                    }
                ],
                asuntosTratados: ['Test asunto 1'],
                acuerdos: [
                    {
                        descripcion: 'Test acuerdo',
                        responsable: 'Test responsable',
                        fechaAprox: ''
                    }
                ]
            };
            
            console.log('Enviando datos:', data);
            
            try {
                const response = await fetch('api/minutas.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });
                
                const result = await response.json();
                console.log('Respuesta:', result);
                
                document.getElementById('result').innerHTML = `
                    <div class="alert ${result.success ? 'alert-success' : 'alert-danger'}">
                        ${result.success ? 
                            `¡Éxito! Minuta guardada con ID: ${result.minuta_id}` : 
                            `Error: ${result.error}`
                        }
                    </div>
                `;
                
            } catch (error) {
                console.error('Error:', error);
                document.getElementById('result').innerHTML = `
                    <div class="alert alert-danger">
                        Error de conexión: ${error.message}
                    </div>
                `;
            }
        });
    </script>
</body>
</html>
