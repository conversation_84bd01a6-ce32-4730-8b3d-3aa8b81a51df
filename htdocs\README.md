# Sistema de Gestión de Minutas

Este es un sistema completo para la gestión de minutas de reuniones que permite crear, guardar, consultar y exportar minutas con funcionalidades avanzadas.

## Características

- ✅ **Crear nuevas minutas** con ID único autogenerado
- ✅ **Guardar minutas en base de datos MySQL**
- ✅ **Consultar minutas existentes** por ID
- ✅ **Exportar a PDF y Word** (funcionalidad original mantenida)
- ✅ **Lista de minutas recientes**
- ✅ **Interfaz intuitiva** con pestañas
- ✅ **Validación de datos**
- ✅ **Asistentes predefinidos** con autocompletado
- ✅ **Gestión dinámica** de asistentes, asuntos y acuerdos

## Estructura del Proyecto

```
htdocs/
├── sistema_minutas.php          # Archivo principal del sistema
├── indexMinutas.html           # Archivo original (mantenido)
├── config/
│   └── database.php            # Configuración de base de datos
├── models/
│   └── Minuta.php             # Modelo de datos para minutas
├── api/
│   └── minutas.php            # API REST para operaciones CRUD
├── database/
│   └── create_tables.sql      # Script para crear las tablas
└── README.md                  # Este archivo
```

## Instalación

### 1. Configurar Base de Datos

1. Abrir phpMyAdmin o tu cliente MySQL preferido
2. Ejecutar el script `database/create_tables.sql` para crear la base de datos y tablas:

```sql
-- El script creará:
-- - Base de datos: minutas_db
-- - Tabla: minutas (datos principales)
-- - Tabla: minuta_asistentes (asistentes por minuta)
-- - Tabla: minuta_asuntos (asuntos tratados)
-- - Tabla: minuta_acuerdos (acuerdos tomados)
```

### 2. Configurar Conexión

Editar el archivo `config/database.php` y ajustar los parámetros de conexión:

```php
private $host = 'localhost';
private $db_name = 'minutas_db';
private $username = 'root';      // Tu usuario MySQL
private $password = '';          // Tu contraseña MySQL
```

### 3. Verificar Permisos

Asegurar que el servidor web tenga permisos de lectura en todos los archivos del proyecto.

### 4. Acceder al Sistema

Abrir en el navegador: `http://localhost/sistema_minutas.php`

## Uso del Sistema

### Crear Nueva Minuta

1. Ir a la pestaña "Nueva Minuta"
2. El sistema generará automáticamente un ID único (formato: MIN-YYYY-MM-XXX)
3. Llenar todos los campos requeridos:
   - Fecha y Hora
   - Lugar
   - Duración
   - Convoca
   - Relator
   - Al menos un asistente
   - Al menos un asunto tratado
   - Al menos un acuerdo
4. Hacer clic en "Guardar en Base de Datos"
5. Opcionalmente exportar a PDF o Word

### Consultar Minutas

1. Ir a la pestaña "Consultar Minutas"
2. **Opción 1**: Buscar por ID específico
   - Ingresar el ID en el campo de búsqueda
   - Hacer clic en "Buscar"
3. **Opción 2**: Seleccionar de la lista de minutas recientes
   - Hacer clic en cualquier tarjeta de minuta
4. Una vez encontrada, se puede exportar a PDF o Word

## Formato de ID de Minutas

El sistema genera IDs únicos con el formato: `MIN-YYYY-MM-XXX`

- `MIN`: Prefijo fijo
- `YYYY`: Año actual
- `MM`: Mes actual
- `XXX`: Número secuencial de 3 dígitos

Ejemplos:
- `MIN-2024-01-001` (Primera minuta de enero 2024)
- `MIN-2024-01-002` (Segunda minuta de enero 2024)
- `MIN-2024-02-001` (Primera minuta de febrero 2024)

## API Endpoints

El sistema incluye una API REST en `api/minutas.php`:

### GET Requests
- `GET api/minutas.php?action=generate_id` - Generar nuevo ID
- `GET api/minutas.php?action=list&limit=20` - Listar minutas
- `GET api/minutas.php?id=MIN-2024-01-001` - Obtener minuta específica

### POST Requests
- `POST api/minutas.php` - Crear nueva minuta

## Tecnologías Utilizadas

- **Frontend**: React 18, Bootstrap 5, Flatpickr
- **Backend**: PHP 7.4+, MySQL 5.7+
- **Exportación**: jsPDF, HTML to Word
- **Estilos**: CSS3, Bootstrap

## Características Técnicas

### Base de Datos
- Diseño normalizado con relaciones apropiadas
- Índices para optimizar consultas
- Soporte para UTF-8
- Eliminación en cascada para mantener integridad

### Seguridad
- Prepared statements para prevenir SQL injection
- Validación de datos en frontend y backend
- Manejo de errores robusto

### Usabilidad
- Interfaz responsiva
- Autocompletado para campos comunes
- Validación en tiempo real
- Mensajes de estado claros

## Solución de Problemas

### Error de Conexión a Base de Datos
1. Verificar que MySQL esté ejecutándose
2. Comprobar credenciales en `config/database.php`
3. Asegurar que la base de datos `minutas_db` existe

### Error 404 en API
1. Verificar que mod_rewrite esté habilitado
2. Comprobar permisos de archivos
3. Revisar la ruta en las llamadas AJAX

### Problemas de Exportación
1. Verificar que las librerías jsPDF estén cargando
2. Comprobar bloqueadores de pop-ups
3. Revisar consola del navegador para errores JavaScript

## Mantenimiento

### Respaldo de Base de Datos
Ejecutar regularmente:
```bash
mysqldump -u root -p minutas_db > backup_minutas_$(date +%Y%m%d).sql
```

### Limpieza de Datos
El sistema mantiene todas las minutas. Para limpiar datos antiguos, crear scripts personalizados según necesidades.

## Soporte

Para reportar problemas o solicitar nuevas características, documentar:
1. Versión del navegador
2. Versión de PHP/MySQL
3. Pasos para reproducir el problema
4. Mensajes de error específicos
