<?php
include_once("conexion.php");
include_once("configDB.php");
include_once("classProcesaVariable.php");
include_once("classUsuario.php");

if (isset($_POST['admin'])) {
    $btnVal = new procesaVariable($_POST['myButton']);

    $taxiConect = new foo_mysqli("localhost", USER, PASS, DBNAME);
    $taxiConect->query("SET NAMES utf8");

    $data = explode("-", $btnVal->valor);
    $user =  new usuario("", "", "", "", "", "", $data[2], $data[1], "");
    $user->identificador = $data[0];

    if ($user->bajaLogin($taxiConect)) {
        $var = 1;
    } else {
        $var = 0;
    }

    $taxiConect->close();
} else {
    $var = '0';
}
header("Location: ../listaLogin.php?res=$var");
