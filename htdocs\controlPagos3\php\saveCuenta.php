<?php
include_once("conexion.php");
include_once("configDB.php");
include_once("classProcesaVariable.php");

if (isset($_POST['nombre']) and isset($_POST['cuenta'])) {

    $banco = new procesaVariable($_POST['nombre']);
    $cuenta = new procesaVariable($_POST['cuenta']);

    $taxiConect = new foo_mysqli("localhost", USER, PASS, DBNAME);
    $taxiConect->query("SET NAMES utf8");

    $insert = "INSERT INTO cuentaBancaria(idCuentaBancaria, banco, estado) 
        VALUES ('$cuenta->valor','$banco->valor','A')";

    $driver = new mysqli_driver();
    $driver->report_mode = MYSQLI_REPORT_STRICT | MYSQLI_REPORT_ERROR;

    $var = 1;
    try {
        $exeInsert = $taxiConect->query($insert);
    } catch (mysqli_sql_exception $e) {
        $var = $e->getCode();
    }

    $taxiConect->close();
} else {
    $var = '0';
}

header("Location: ../newCuenta.php?resp=$var");
