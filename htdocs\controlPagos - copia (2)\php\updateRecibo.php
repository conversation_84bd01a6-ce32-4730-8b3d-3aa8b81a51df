<?php
include_once("conexion.php");
include_once("configDB.php");
include_once("classProcesaVariable.php");
include_once("classAbono.php");
include_once("classRecibo.php");
include_once("session.php");

if (isset($_POST['reciboHide']) and isset($_POST['montoEfectivo']) and isset($_POST['montoCheque'])) {

    $recibo = new procesaVariable($_POST['reciboHide']);
    $efectivo = new procesaVariable($_POST['montoEfectivo']);
    $cheque = new procesaVariable($_POST['montoCheque']);
    $numCheque = new procesaVariable($_POST['numCheque']);

    $tipoUsuario = $_SESSION['userType'];
    $idUser = $_SESSION['idUser'];

    $taxiConect = new foo_mysqli("localhost", USER, PASS, DBNAME);
    $taxiConect->query("SET NAMES utf8");

    $q = "UPDATE recibo SET estado='P',
        idUsuario = '$idUser', 
        tipoUsuario = '$tipoUsuario',
        efectivo='$efectivo->valor',
        cheque='$cheque->valor',
        numeroCheque=" . $numCheque->nulo() . ",
        fechaPago=CURRENT_TIMESTAMP() 
        WHERE idRecibo='$recibo->valor'";
    $eq = $taxiConect->query($q);

    $link = "../reciboPrint.php?r=$recibo->valor";

    if (!$eq) {
        $link = "../reciboPago.php?resp=0";
    }

    $taxiConect->close();
} else {
    $link = "../reciboPago.php?resp=0";
}

header("Location: $link");
