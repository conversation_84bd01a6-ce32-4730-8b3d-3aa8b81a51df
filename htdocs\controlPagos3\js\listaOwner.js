$(document).ready(function () {
  if ($.get("res")) {
    Rsp = $.get("res");
    switch (Rsp) {
      case "0":
        rmsg = "Error en el proceso";
        break;
      case "1":
        rmsg = "Acción realizada correctamente";
        break;
      case "2":
        rmsg =
          "No se pudo activar, modifique el contable en la parte de edición.";
        break;
      case "11":
        rmsg = "La edición se guardo correctamente.";
        break;
      default:
        rmsg = "Error al guardar en la base de datos.";
    }
    alertify
      .alert()
      .setting({
        title: "Servitaxis Sitio 152",
        closable: false,
        message: rmsg,
        onok: function () {
          let myUrl = window.location.origin;
          myUrl = `${myUrl}/listaOwner.php`;
          $(location).attr("href", myUrl);
        },
      })
      .show();
  }

  const admin = $("#admin").val();
  $.ajax({
    type: "post",
    url: "php/ajax/listadoOwners.php",
    data: { myVal: admin },
  })
    .done(function (r) {
      R = $.parseJSON(r);
      console.log(R);
      if (R[0] != 0) {
        $.each(R, function (indexInArray, valueOfElement) {
          const { idOwner, nombreCompleto, telefono, correo, estado, Tipo } =
            valueOfElement;
          const append = `<tr>                    
                    <td>${nombreCompleto}</td>
                    <td>${Tipo}</td>
                    <td>${telefono === null ? "" : telefono}</td>
                    <td>${correo}</td>
                    <td>${estado == "A" ? "Activo" : "Inactivo"}</td>
                    <td><button value="${idOwner}" name="ownerEdit" class="btn btn-info">Editar</button></td>
                    <td><button value="${idOwner}-${
            estado == "A" ? "I" : "A"
          }" name="ownerAction" class="btn ${
            estado == "A" ? "btn-warning" : "btn-success"
          }">${estado == "A" ? "Desactivar" : "Activar"}</button></td>
                </tr>`;
          $("#ownerList tbody").append(append);
        });
        $("#ownerList").DataTable({
          language: {
            url: "//cdn.datatables.net/plug-ins/9dcbecd42ad/i18n/Spanish.json",
          },
        });
        $("#ownerList").tableExport({
          formats: ["csv", "txt"],
        });
      }
    })
    .fail(function () {
      alertify.alert("Warning!", "Error al cargar los datos");
    });
});
