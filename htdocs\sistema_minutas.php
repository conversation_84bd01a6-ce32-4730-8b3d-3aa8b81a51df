<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sistema de Gestión de Minutas</title>
    <script src="https://unpkg.com/react@18.2.0/umd/react.production.min.js"></script>
    <script src="https://unpkg.com/react-dom@18.2.0/umd/react-dom.production.min.js"></script>
    <script src="https://unpkg.com/jspdf@2.5.1/dist/jspdf.umd.min.js"></script>
    <script src="https://unpkg.com/jspdf-autotable@3.5.31/dist/jspdf.plugin.autotable.min.js"></script>
    <script src="https://unpkg.com/xlsx@0.18.5/dist/xlsx.full.min.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-QWTKZyjpPEjISv5WaRU9OFeRpok6YctnYmDr5pNlyT2bRjXh0JMhjY6hW+ALEwIH" crossorigin="anonymous">
    <link href="https://cdn.jsdelivr.net/npm/flatpickr@4.6.13/dist/flatpickr.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/flatpickr@4.6.13/dist/flatpickr.min.js"></script>
    <style>
        .form-label { font-weight: bold; }
        .card { margin-bottom: 1.5rem; }
        .btn-remove { font-size: 0.875rem; }
        .flatpickr-calendar { z-index: 1050; }
        .firma-input { width: 100%; }
        .minuta-id-display {
            background: #e3f2fd;
            border: 2px solid #2196f3;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            text-align: center;
        }
        .minuta-id-display h3 {
            color: #1976d2;
            margin: 0;
        }
        .nav-tabs .nav-link.active {
            background-color: #0d6efd;
            color: white;
        }
        .search-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .minuta-card {
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .minuta-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .loading {
            text-align: center;
            padding: 20px;
        }
        .alert-success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .alert-danger {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .border-left-primary {
            border-left: 4px solid #007bff !important;
        }
        .text-sm {
            font-size: 0.875rem;
        }
        .acuerdo-card {
            transition: all 0.3s ease;
            border: 1px solid #e3e6f0;
        }
        .acuerdo-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        }
        .badge-status {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
        }
        .filters-panel {
            background: #f8f9fa;
            border-radius: 8px;
        }
        .filter-count {
            font-size: 0.9rem;
            color: #6c757d;
        }
        .acuerdo-description {
            max-height: 100px;
            overflow-y: auto;
        }
        .table-responsive {
            border-radius: 8px;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            overflow-x: auto;
            min-height: 400px;
        }
        .table {
            min-width: 1200px;
            margin-bottom: 0;
        }
        .table th {
            border-top: none;
            font-weight: 600;
            font-size: 0.9rem;
            white-space: nowrap;
            padding: 12px 8px;
            background-color: #343a40 !important;
            color: white !important;
            position: sticky;
            top: 0;
            z-index: 10;
        }
        .table td {
            vertical-align: middle;
            font-size: 0.85rem;
            padding: 10px 8px;
            border-bottom: 1px solid #dee2e6;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .table tbody tr:hover {
            background-color: #f8f9fa;
        }
        .text-truncate {
            max-width: 1px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        .export-buttons {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #dee2e6;
        }
        .table-container {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .col-numero { width: 60px; min-width: 60px; }
        .col-id-minuta { width: 120px; min-width: 120px; }
        .col-descripcion { width: 300px; min-width: 250px; }
        .col-responsable { width: 150px; min-width: 120px; }
        .col-fecha-compromiso { width: 140px; min-width: 120px; }
        .col-estado { width: 100px; min-width: 80px; }
        .col-fecha-minuta { width: 140px; min-width: 120px; }
        .col-lugar { width: 200px; min-width: 150px; }
        .col-convoca { width: 150px; min-width: 120px; }
        .col-acciones { width: 80px; min-width: 80px; }

        /* Estilos para el select de estado */
        .estado-select {
            border: 1px solid #dee2e6;
            border-radius: 4px;
            font-size: 0.875rem;
        }
        .estado-select:focus {
            border-color: #86b7fe;
            box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
        }
        .estado-select option {
            padding: 4px 8px;
        }
    </style>
</head>
<body>
    <div id="root"></div>
    <script type="text/javascript">
        const { useState, useEffect } = React;
        const { jsPDF } = window.jspdf;

        // Datos predefinidos (mantenidos del código original)
        const asistentesPredefinidos = [
               //Dirección
               { nombre: 'Agustín Díaz Lastra', area: 'Dirección General', extensionCorreo: '<EMAIL>' },
            { nombre: 'Juana Elizabeth Castro González', area: 'Dirección General', extensionCorreo: '<EMAIL>' },
            { nombre: 'Elizabeth Luna Reyna', area: 'Dirección General', extensionCorreo: '<EMAIL>' },
            { nombre: 'Lidia Michelett López Roque', area: 'Dirección General', extensionCorreo: '<EMAIL>' },
            { nombre: 'Tomas Alberto Alamina Aguilar', area: 'Dirección General', extensionCorreo: '<EMAIL>' },
            { nombre: 'Fernanda Vazquez Posada', area: 'Dirección General', extensionCorreo: '@ruv.org.mx' },
            { nombre: 'Juan Carlos Leal Barcenas', area: 'Dirección General', extensionCorreo: '@ruv.org.mx' },
            { nombre: 'Reyna Catalina Nevarez Rascon', area: 'Contraloría', extensionCorreo: '<EMAIL>' },
            { nombre: 'Mario Rafael Reyna Mandujano', area: 'Dirección General', extensionCorreo: '<EMAIL>' },
            { nombre: 'Juan Manuel Rodriguez Gomez', area: 'Contraloría', extensionCorreo: '<EMAIL>' },
            { nombre: 'Brenda Maniau Mancila', area: 'Enlace Especializado de Coordinación y Comunicación', extensionCorreo: '<EMAIL>' },

            //Desarrollo
            { nombre: 'Maria Elena López', area: 'Gerente de Desarrollo', extensionCorreo: '<EMAIL>' },
            { nombre: 'Norberto Rojas Nava', area: 'Consultor de Aplicaciones Móviles y Portales', extensionCorreo: '<EMAIL>' },
            { nombre: 'Adrian Alberto Casas Lopez', area: 'Consultor de Desarrollo y Mantenimiento de Sistemas', extensionCorreo: '<EMAIL>' },
            { nombre: 'Edgar Oscar Gómez Lara', area: 'Consultor y Planificador de Recursos Empresariales', extensionCorreo: '<EMAIL>' },
            { nombre: 'Mayeli Ayerim González Hernández', area: 'Consultor de Calidad', extensionCorreo: '<EMAIL>' },
            { nombre: 'Azael Antonio Sandoval', area: 'Ejecutivo Desarrollador de Sistemas', extensionCorreo: '<EMAIL>' },
            { nombre: 'Raúl Hernández Torres', area: 'Ejecutivo de gestión ERP', extensionCorreo: '<EMAIL>' },
            { nombre: 'Kenya Isabel Anaya Nolasco', area: 'Enlace Especializado de Aplicaciones Móviles y Portales', extensionCorreo: '<EMAIL>' },
            { nombre: 'Gerardo Sánchez Cabrera', area: 'Enlace Especializado de Pruebas', extensionCorreo: '<EMAIL>' },
            { nombre: 'Mirian Martínez Gutierrez', area: 'Enlace Especializado Documentador de Procesos', extensionCorreo: '<EMAIL>' },
            { nombre: 'José Alberto Padilla Castellanos', area: 'Enlace Especializado Programador Analista de Requerimientos', extensionCorreo: '<EMAIL>' },
            //Finanzas
            { nombre: 'Abraham Rojas Unda', area: 'Gerente de Administración y Finanzas', extensionCorreo: '<EMAIL>' },
            { nombre: 'Zolain Elvira Cázares Graillo', area: 'Consultora de Finanzas e Impuestos', extensionCorreo: '<EMAIL>' },
            { nombre: 'Katia Cristina Esquivel Leos', area: '', extensionCorreo: '<EMAIL>' },
            { nombre: 'María Fernanda De la Cruz Barranco', area: 'Ejecutivo de Adquisiciones', extensionCorreo: '<EMAIL>' },
            { nombre: 'Yesica Isis Romero Guevara', area: '', extensionCorreo: '<EMAIL>' },
            { nombre: 'Yoselin Pérez Torrijos', area: 'Enlace Especializado de Administración', extensionCorreo: '<EMAIL>' },
            //Operaciones
            { nombre: 'Paul Enrique Tinoco Manrique', area: 'Gerente de Operaciones y Servicios', extensionCorreo: '<EMAIL>' },
            { nombre: 'Alberto Julian Dominguez Maldonado', area: 'Consultor Geografico', extensionCorreo: '<EMAIL>' },
            { nombre: 'Susumo Rodrigo Yamasaki Tanikawa', area: 'Consultor de Operación y Mejora Continua', extensionCorreo: '<EMAIL>' },
            { nombre: 'Maria Antonieta Pichardo Encino', area: 'Ejecutivo de Servicios', extensionCorreo: '<EMAIL>' },
            { nombre: 'Arlette Vanessa Oceguera', area: 'Analista', extensionCorreo: '<EMAIL>' },
            { nombre: 'Niridia Abigail Terán Carrillo', area: '', extensionCorreo: '<EMAIL>' },
            { nombre: 'Maria del Carmen Martinez Benitez', area: 'Enlace especializado de Seguimiento y Planeación', extensionCorreo: '<EMAIL>' },
            //Infraestructura
            { nombre: 'César Amaury Aguilera Gómez', area: 'Gerente de Infraestructura y Mantenimiento', extensionCorreo: '<EMAIL>' },
            { nombre: 'Nadia Mariana Zardaneta Vázquez', area: 'Administrador de Base de Datos', extensionCorreo: '<EMAIL>' },
            { nombre: 'Victor Manuel Cortes López', area: 'Administrador de Infraestructura', extensionCorreo: '<EMAIL>' },
            { nombre: 'Julio Rául Fernández Vázquez', area: 'Ejecutivo de Sistemas', extensionCorreo: '<EMAIL>' },
            { nombre: 'Julio Cesar Gayosso Armenta', area: '', extensionCorreo: '<EMAIL>' },
            { nombre: 'Oscar Daniel Hernández Carrioza', area: 'Enlace Especializado de Infrestructura', extensionCorreo: '<EMAIL>' },
            { nombre: 'Luz Amanda Valdez Juárez', area: 'Enlace Especializado de Infrestructura', extensionCorreo: '<EMAIL>' },
            //Jurídico
            { nombre: 'Jose Hernan Valencia Santos', area: 'Gerente de Jurídico', extensionCorreo: '<EMAIL>' },
            { nombre: 'Alma Lilia Estrada Salazar', area: '', extensionCorreo: '<EMAIL>' },
            { nombre: 'Fernanda Vaxquez Posada', area: 'Jurídico', extensionCorreo: '<EMAIL>' }
        ];

        const predefinedUsers = [
            'Agustín Díaz Lastra',
            'Juana Elizabeth Castro González',
            'Elizabeth Luna Reyna',
            'Lidia Michelett López Roque',
            'Tomas Alberto Alamina Aguilar',
            'Fernanda Vazquez Posada',
            'Juan Carlos Leal Barcenas',
            'Reyna Catalina Nevarez Rascon',
            'Mario Rafael Reyna Mandujano',
            'Juan Manuel Rodriguez Gomez',
            'Brenda Maniau Mancila',
            'Maria Elena López',
            'Norberto Rojas Nava',
            'Adrian Alberto Casas Lopez',
            'Edgar Oscar Gómez Lara',
            'Mayeli Ayerim Gonzalez Hernandez',
            'Azael Antonio Sandoval',
            'Raúl Hernández Torres',
            'Kenya Isabel Anaya Nolasco',
            'Gerardo Sánchez Cabrera',
            'Mirian Martinez Gutierrez',
            'Jose Alberto Padilla Castellanos',
            'Abraham Rojas Unda',
            'Zolain Elvira Cázares Graillo',
            'Katia Cristina Esquivel Leos',
            'María Fernanda De la Cruz Barranco',
            'Yesica Isis Romero Guevara',
            'Yoselin Pérez Torrijos',
            'Paul Enrique Tinoco Manrique',
            'Alberto Julian Dominguez Maldonado',
            'Susumo Rodrigo Yamasaki Tanikawa',
            'Maria Antonieta Pichardo Encino',
            'Arlette Vanessa Oceguera',
            'Niridia Abigail Teran Carrillo',
            'Maria del Carmen Martinez Benitez',
            'César Amaury Aguilera Gomez',
            'Nadia Mariana Zardaneta Vázquez',
            'Victor Manuel Cortes Lopez',
            'Julio Rául Fernández Vázquez',
            'Julio Cesar Gayosso Armenta',
            'Oscar Daniel Hernandez Carrioza',
            'Luz Amanda Valdez Juárez',
            'Jose Hernan Valencia Santos',
            'Alma Lilia Estrada Salazar'
        ];

        const predefinedLugares = [
            'Sala de junta A del RUV',
            'Sala de junta B del RUV',
            'Sala de juntas de Dirección General del RUV',
            'Reunión de Teams'
        ];

        const duracionOpciones = [
            '0.5 horas', '1 hora', '1.5 horas', '2 horas',
            '2.5 horas', '3 horas', '3.5 horas', '4 horas'
        ];

        // Componente principal
        const MinutaApp = () => {
            const [activeTab, setActiveTab] = useState('nueva');
            const [currentMinutaId, setCurrentMinutaId] = useState('');
            const [isLoading, setIsLoading] = useState(false);
            const [message, setMessage] = useState({ type: '', text: '' });
            const [searchId, setSearchId] = useState('');
            const [minutasList, setMinutasList] = useState([]);
            const [viewingMinuta, setViewingMinuta] = useState(null);
            const [acuerdosList, setAcuerdosList] = useState([]);
            const [filteredAcuerdos, setFilteredAcuerdos] = useState([]);
            const [filters, setFilters] = useState({
                responsable: '',
                estado: '',
                fechaDesde: '',
                fechaHasta: ''
            });
            const [isEditing, setIsEditing] = useState(false);
            const [editingMinuta, setEditingMinuta] = useState(null);
            const [duplicateWarning, setDuplicateWarning] = useState(false);

            const [formData, setFormData] = useState({
                fechaHora: '',
                lugar: '',
                duracion: '',
                convoca: '',
                antecedentes: '',
                objetivo: '',
                relator: '',
                ordenDia: '',
                proximaReunion: '',
                asistentes: [{ nombre: '', area: '', extensionCorreo: '', firma: '', isCustom: true }],
                asuntosTratados: [''],
                acuerdos: [{ descripcion: '', responsable: '', fechaAprox: '' }]
            });

            // Generar nuevo ID al cargar
            useEffect(() => {
                if (activeTab === 'nueva') {
                    generateNewId();
                }
            }, [activeTab]);

            // Configurar date pickers
            useEffect(() => {
                if (activeTab !== 'nueva') return;

                // Usar setTimeout más largo para asegurar que React haya renderizado
                const timer = setTimeout(() => {
                    console.log('Inicializando date pickers...');

                    // Destruir pickers existentes primero
                    const existingPickers = document.querySelectorAll('.flatpickr-input');
                    existingPickers.forEach(input => {
                        if (input._flatpickr) {
                            input._flatpickr.destroy();
                        }
                    });

                    // Inicializar picker para fecha y hora principal
                    const fechaHoraElement = document.getElementById('fechaHora');
                    if (fechaHoraElement) {
                        console.log('Inicializando fechaHora picker');
                        flatpickr(fechaHoraElement, {
                            enableTime: true,
                            dateFormat: 'd/m/Y H:i',
                            defaultDate: formData.fechaHora || null,
                            locale: {
                                firstDayOfWeek: 1
                            },
                            onChange: (selectedDates, dateStr) => {
                                console.log('Fecha hora cambiada:', dateStr);
                                setFormData(prev => ({ ...prev, fechaHora: dateStr }));
                            }
                        });
                    } else {
                        console.log('Elemento fechaHora no encontrado');
                    }

                    // Inicializar picker para próxima reunión
                    const proximaReunionElement = document.getElementById('proximaReunion');
                    if (proximaReunionElement) {
                        console.log('Inicializando proximaReunion picker');
                        flatpickr(proximaReunionElement, {
                            enableTime: true,
                            dateFormat: 'd/m/Y H:i',
                            defaultDate: formData.proximaReunion || null,
                            locale: {
                                firstDayOfWeek: 1
                            },
                            onChange: (selectedDates, dateStr) => {
                                console.log('Próxima reunión cambiada:', dateStr);
                                setFormData(prev => ({ ...prev, proximaReunion: dateStr }));
                            }
                        });
                    } else {
                        console.log('Elemento proximaReunion no encontrado');
                    }

                    // Inicializar pickers para fechas de acuerdos
                    formData.acuerdos.forEach((_, index) => {
                        const acuerdoElement = document.getElementById(`fechaAprox-${index}`);
                        if (acuerdoElement) {
                            console.log(`Inicializando fechaAprox-${index} picker`);
                            flatpickr(acuerdoElement, {
                                enableTime: true,
                                dateFormat: 'd/m/Y H:i',
                                defaultDate: formData.acuerdos[index].fechaAprox || null,
                                locale: {
                                    firstDayOfWeek: 1
                                },
                                onChange: (selectedDates, dateStr) => {
                                    console.log(`Fecha acuerdo ${index} cambiada:`, dateStr);
                                    const updatedAcuerdos = [...formData.acuerdos];
                                    updatedAcuerdos[index].fechaAprox = dateStr;
                                    setFormData(prev => ({ ...prev, acuerdos: updatedAcuerdos }));
                                }
                            });
                        } else {
                            console.log(`Elemento fechaAprox-${index} no encontrado`);
                        }
                    });
                }, 500); // Aumentar el timeout

                return () => {
                    clearTimeout(timer);
                };
            }, [activeTab, formData.acuerdos.length]);

            // Efecto adicional para reinicializar pickers cuando cambie el formData
            useEffect(() => {
                if (activeTab === 'nueva') {
                    const timer = setTimeout(() => {
                        // Reinicializar solo si los elementos existen
                        const fechaHoraElement = document.getElementById('fechaHora');
                        if (fechaHoraElement && !fechaHoraElement._flatpickr) {
                            flatpickr(fechaHoraElement, {
                                enableTime: true,
                                dateFormat: 'd/m/Y H:i',
                                onChange: (selectedDates, dateStr) => {
                                    setFormData(prev => ({ ...prev, fechaHora: dateStr }));
                                }
                            });
                        }
                    }, 200);

                    return () => clearTimeout(timer);
                }
            }, [activeTab]);

            // Función para actualizar datos del formulario
            const updateFormData = (field, value) => {
                setFormData(prev => ({
                    ...prev,
                    [field]: value
                }));

                // Verificar duplicados en tiempo real para campos clave
                if (['fechaHora', 'lugar', 'convoca'].includes(field)) {
                    const newFormData = { ...formData, [field]: value };
                    if (newFormData.fechaHora && newFormData.lugar && newFormData.convoca) {
                        checkDuplicatesRealTime(newFormData);
                    } else {
                        setDuplicateWarning(false);
                    }
                }
            };

            // Verificación de duplicados en tiempo real
            const checkDuplicatesRealTime = async (dataToCheck) => {
                try {
                    const params = new URLSearchParams({
                        action: 'check_duplicate',
                        fecha_hora: dataToCheck.fechaHora,
                        lugar: dataToCheck.lugar,
                        convoca: dataToCheck.convoca
                    });

                    if (isEditing && editingMinuta) {
                        params.append('exclude_id', editingMinuta.minuta_id);
                    }

                    const response = await fetch(`api/minutas.php?${params}`);
                    const data = await response.json();

                    setDuplicateWarning(data.success && data.is_duplicate);
                } catch (error) {
                    console.error('Error verificando duplicados en tiempo real:', error);
                    setDuplicateWarning(false);
                }
            };

            // API Functions
            const generateNewId = async () => {
                try {
                    const response = await fetch('api/minutas.php?action=generate_id');
                    const data = await response.json();
                    if (data.success) {
                        setCurrentMinutaId(data.minuta_id);
                    }
                } catch (error) {
                    console.error('Error generando ID:', error);
                }
            };

            // Función para verificar duplicados antes de guardar
            const checkForDuplicates = async () => {
                try {
                    const params = new URLSearchParams({
                        action: 'check_duplicate',
                        fecha_hora: formData.fechaHora,
                        lugar: formData.lugar,
                        convoca: formData.convoca
                    });

                    if (isEditing && editingMinuta) {
                        params.append('exclude_id', editingMinuta.minuta_id);
                    }

                    const response = await fetch(`api/minutas.php?${params}`);
                    const data = await response.json();

                    return data.success ? data.is_duplicate : false;
                } catch (error) {
                    console.error('Error verificando duplicados:', error);
                    return false;
                }
            };

            const saveMinuta = async () => {
                if (isEditing) {
                    updateMinuta();
                    return;
                }

                // Verificar duplicados antes de guardar
                const isDuplicate = await checkForDuplicates();
                if (isDuplicate) {
                    const confirmSave = confirm(
                        '⚠️ ADVERTENCIA: Ya existe una minuta con la misma fecha, lugar y convocante.\n\n' +
                        '¿Está seguro de que desea guardar esta minuta?\n\n' +
                        'Datos detectados:\n' +
                        `• Fecha: ${formData.fechaHora}\n` +
                        `• Lugar: ${formData.lugar}\n` +
                        `• Convoca: ${formData.convoca}`
                    );

                    if (!confirmSave) {
                        setMessage({
                            type: 'error',
                            text: '❌ Guardado cancelado. Modifique algún campo para evitar duplicados o confirme si realmente desea crear una minuta duplicada.'
                        });
                        return;
                    }
                }

                setIsLoading(true);
                setMessage({ type: '', text: '' });

                try {
                    const response = await fetch('api/minutas.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(formData)
                    });

                    const data = await response.json();

                    if (data.success) {
                        setMessage({
                            type: 'success',
                            text: `✅ ¡Minuta guardada exitosamente! ID asignado: ${data.minuta_id}. La minuta ha sido almacenada en la base de datos y está disponible para consulta.`
                        });
                        setCurrentMinutaId(data.minuta_id);
                        // Limpiar formulario después de un tiempo más largo para que el usuario vea el mensaje
                        setTimeout(() => {
                            setMessage({ type: 'success', text: '🔄 Preparando nueva minuta...' });
                            setTimeout(() => {
                                resetForm();
                                generateNewId();
                                setMessage({ type: '', text: '' });
                            }, 1000);
                        }, 4000);
                    } else {
                        setMessage({ type: 'error', text: data.error || 'Error al guardar la minuta' });
                    }
                } catch (error) {
                    setMessage({ type: 'error', text: 'Error de conexión al servidor' });
                } finally {
                    setIsLoading(false);
                }
            };

            const searchMinuta = async (minutaId = null) => {
                const idToSearch = minutaId || searchId.trim();

                if (!idToSearch) {
                    setMessage({ type: 'error', text: 'Ingrese un ID de minuta para buscar' });
                    return;
                }

                setIsLoading(true);
                setMessage({ type: '', text: '' });

                try {
                    const response = await fetch(`api/minutas.php?id=${encodeURIComponent(idToSearch)}`);
                    const data = await response.json();

                    if (data.success) {
                        setViewingMinuta(data.data);
                        setMessage({ type: 'success', text: 'Minuta encontrada' });
                        if (minutaId) {
                            setSearchId(minutaId); // Actualizar el campo de búsqueda
                        }
                    } else {
                        setMessage({ type: 'error', text: 'Minuta no encontrada' });
                        setViewingMinuta(null);
                    }
                } catch (error) {
                    setMessage({ type: 'error', text: 'Error de conexión al servidor' });
                } finally {
                    setIsLoading(false);
                }
            };

            // Función para iniciar edición de minuta
            const startEditMinuta = (minuta) => {
                setIsEditing(true);
                setEditingMinuta(minuta);

                // Cargar datos en el formulario
                setFormData({
                    fechaHora: minuta.fecha_hora,
                    lugar: minuta.lugar,
                    duracion: minuta.duracion,
                    convoca: minuta.convoca,
                    antecedentes: minuta.antecedentes,
                    objetivo: minuta.objetivo,
                    relator: minuta.relator,
                    ordenDia: minuta.orden_dia,
                    proximaReunion: minuta.proxima_reunion,
                    asistentes: minuta.asistentes || [{ nombre: '', area: '', extensionCorreo: '', firma: '', isCustom: true }],
                    asuntosTratados: minuta.asuntos_tratados || [''],
                    acuerdos: minuta.acuerdos || [{ descripcion: '', responsable: '', fechaAprox: '' }]
                });

                setActiveTab('nueva'); // Cambiar a la pestaña de formulario
                setMessage({ type: 'info', text: `Editando minuta: ${minuta.minuta_id}` });
            };

            // Función para cancelar edición
            const cancelEdit = () => {
                setIsEditing(false);
                setEditingMinuta(null);
                resetForm();
                setMessage({ type: '', text: '' });
            };

            // Función para actualizar minuta
            const updateMinuta = async () => {
                if (!editingMinuta) {
                    setMessage({ type: 'error', text: 'No hay minuta seleccionada para editar' });
                    return;
                }

                setIsLoading(true);
                try {
                    const dataToSend = {
                        ...formData,
                        minuta_id: editingMinuta.minuta_id
                    };

                    const response = await fetch('api/minutas.php', {
                        method: 'PUT',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(dataToSend)
                    });

                    const data = await response.json();

                    if (data.success) {
                        setMessage({ type: 'success', text: `Minuta ${editingMinuta.minuta_id} actualizada exitosamente` });
                        setIsEditing(false);
                        setEditingMinuta(null);
                        resetForm();

                        // Actualizar la vista si estamos viendo esta minuta
                        if (viewingMinuta && viewingMinuta.minuta_id === editingMinuta.minuta_id) {
                            searchMinuta(editingMinuta.minuta_id);
                        }

                        // Actualizar lista de minutas si está cargada
                        if (minutasList.length > 0) {
                            loadMinutasList();
                        }
                    } else {
                        setMessage({ type: 'error', text: data.error || 'Error al actualizar la minuta' });
                    }
                } catch (error) {
                    setMessage({ type: 'error', text: 'Error al actualizar la minuta: ' + error.message });
                } finally {
                    setIsLoading(false);
                }
            };

            const loadMinutasList = async () => {
                setIsLoading(true);
                try {
                    const response = await fetch('api/minutas.php?action=list&limit=20');
                    const data = await response.json();
                    if (data.success) {
                        setMinutasList(data.data);
                    }
                } catch (error) {
                    console.error('Error cargando lista:', error);
                } finally {
                    setIsLoading(false);
                }
            };

            // Cargar lista cuando se cambia a la pestaña de consulta
            useEffect(() => {
                if (activeTab === 'consultar') {
                    loadMinutasList();
                } else if (activeTab === 'acuerdos') {
                    loadAcuerdosList();
                }
            }, [activeTab]);

            const loadAcuerdosList = async () => {
                setIsLoading(true);
                try {
                    const response = await fetch('api/minutas.php?action=acuerdos&limit=100');
                    const data = await response.json();
                    if (data.success) {
                        console.log('Datos de acuerdos recibidos:', data.data);
                        // Debug: mostrar primer acuerdo para verificar estructura
                        if (data.data.length > 0) {
                            console.log('Primer acuerdo:', data.data[0]);
                            console.log('Campos del primer acuerdo:', Object.keys(data.data[0]));
                        }
                        setAcuerdosList(data.data);
                        setFilteredAcuerdos(data.data);
                    }
                } catch (error) {
                    console.error('Error cargando acuerdos:', error);
                } finally {
                    setIsLoading(false);
                }
            };

            // Función para calcular el estado real del acuerdo
            const calculateAcuerdoStatus = (fechaCompromiso, estadoManual = 'vigente') => {
                // Si está marcado como completado, siempre es completado
                if (estadoManual === 'completado') {
                    return 'completado';
                }

                // Si no hay fecha, es vigente por defecto
                if (!fechaCompromiso || fechaCompromiso.trim() === '') {
                    return 'vigente';
                }

                try {
                    // Manejar formato dd/mm/yyyy hh:mm o dd/mm/yyyy
                    let fechaParts, horaParts = ['23', '59']; // Por defecto al final del día

                    if (fechaCompromiso.includes(' ')) {
                        // Formato con hora: dd/mm/yyyy hh:mm
                        const [fechaPart, horaPart] = fechaCompromiso.split(' ');
                        fechaParts = fechaPart.split('/');
                        horaParts = horaPart.split(':');
                    } else {
                        // Solo fecha: dd/mm/yyyy
                        fechaParts = fechaCompromiso.split('/');
                    }

                    if (fechaParts.length !== 3) {
                        return 'vigente';
                    }

                    const [dia, mes, año] = fechaParts;
                    const [hora, minuto] = horaParts;

                    // Crear fecha del acuerdo
                    const fechaAcuerdo = new Date(parseInt(año), parseInt(mes) - 1, parseInt(dia), parseInt(hora), parseInt(minuto));

                    // Fecha actual
                    const ahora = new Date();

                    // Si la fecha ya pasó, es vencido (a menos que esté completado)
                    if (fechaAcuerdo < ahora) {
                        return 'vencido';
                    } else {
                        return 'vigente';
                    }
                } catch (error) {
                    return 'vigente';
                }
            };

            // Función para obtener la información visual del estado
            const getStatusDisplay = (estado) => {
                switch (estado) {
                    case 'completado':
                        return {
                            className: 'text-success fw-bold',
                            text: '✅ Completado',
                            bgClass: 'bg-success',
                            textClass: 'text-white'
                        };
                    case 'vencido':
                        return {
                            className: 'text-danger fw-bold',
                            text: '⚠️ Vencido',
                            bgClass: 'bg-danger',
                            textClass: 'text-white'
                        };
                    case 'vigente':
                    default:
                        return {
                            className: 'text-warning fw-bold',
                            text: '🕒 Vigente',
                            bgClass: 'bg-warning',
                            textClass: 'text-dark'
                        };
                }
            };

            // Función para filtrar acuerdos
            const applyFilters = () => {
                let filtered = [...acuerdosList];

                // Filtro por responsable
                if (filters.responsable) {
                    filtered = filtered.filter(acuerdo =>
                        acuerdo.responsable &&
                        acuerdo.responsable.toLowerCase().includes(filters.responsable.toLowerCase())
                    );
                }

                // Filtro por estado
                if (filters.estado) {
                    filtered = filtered.filter(acuerdo => {
                        const estadoReal = calculateAcuerdoStatus(acuerdo.fecha_aproximada, acuerdo.estado);
                        return estadoReal === filters.estado.toLowerCase();
                    });
                }

                // Filtro por fecha desde
                if (filters.fechaDesde) {
                    const fechaDesde = new Date(filters.fechaDesde);
                    filtered = filtered.filter(acuerdo => {
                        if (!acuerdo.fecha_aproximada) return false;
                        try {
                            // Manejar formato dd/mm/yyyy hh:mm o dd/mm/yyyy
                            const fechaPart = acuerdo.fecha_aproximada.includes(' ') ?
                                acuerdo.fecha_aproximada.split(' ')[0] : acuerdo.fecha_aproximada;
                            const [dia, mes, año] = fechaPart.split('/');
                            const fechaAcuerdo = new Date(parseInt(año), parseInt(mes) - 1, parseInt(dia));
                            return fechaAcuerdo >= fechaDesde;
                        } catch (error) {
                            console.error('Error procesando fecha para filtro desde:', acuerdo.fecha_aproximada);
                            return false;
                        }
                    });
                }

                // Filtro por fecha hasta
                if (filters.fechaHasta) {
                    const fechaHasta = new Date(filters.fechaHasta);
                    filtered = filtered.filter(acuerdo => {
                        if (!acuerdo.fecha_aproximada) return false;
                        try {
                            // Manejar formato dd/mm/yyyy hh:mm o dd/mm/yyyy
                            const fechaPart = acuerdo.fecha_aproximada.includes(' ') ?
                                acuerdo.fecha_aproximada.split(' ')[0] : acuerdo.fecha_aproximada;
                            const [dia, mes, año] = fechaPart.split('/');
                            const fechaAcuerdo = new Date(parseInt(año), parseInt(mes) - 1, parseInt(dia));
                            return fechaAcuerdo <= fechaHasta;
                        } catch (error) {
                            console.error('Error procesando fecha para filtro hasta:', acuerdo.fecha_aproximada);
                            return false;
                        }
                    });
                }

                setFilteredAcuerdos(filtered);
            };

            // Aplicar filtros cuando cambien
            useEffect(() => {
                applyFilters();
            }, [filters, acuerdosList]);

            // Función para limpiar filtros
            const clearFilters = () => {
                setFilters({
                    responsable: '',
                    estado: '',
                    fechaDesde: '',
                    fechaHasta: ''
                });
            };

            // Función para navegar a una minuta específica
            const goToMinuta = (minutaId) => {
                setActiveTab('consultar');
                setViewingMinuta(null); // Limpiar vista anterior
                // Buscar la minuta directamente
                setTimeout(() => {
                    searchMinuta(minutaId);
                }, 100);
            };

            // Función para exportar a Excel
            const exportToExcel = (data) => {
                if (!data || data.length === 0) {
                    setMessage({ type: 'error', text: 'No hay datos para exportar' });
                    return;
                }

                // Preparar datos para Excel
                const excelData = data.map((acuerdo, index) => ({
                    '#': index + 1,
                    'ID Minuta': acuerdo.minuta_id,
                    'Descripción del Acuerdo': acuerdo.descripcion,
                    'Responsable': acuerdo.responsable || 'No asignado',
                    'Fecha Compromiso': acuerdo.fecha_aproximada || 'No definida',
                    'Estado': getStatusDisplay(calculateAcuerdoStatus(acuerdo.fecha_aproximada, acuerdo.estado)).text,
                    'Fecha Minuta': acuerdo.fecha_minuta,
                    'Lugar': acuerdo.lugar,
                    'Convoca': acuerdo.convoca
                }));

                // Crear libro de trabajo
                const wb = XLSX.utils.book_new();
                const ws = XLSX.utils.json_to_sheet(excelData);

                // Ajustar ancho de columnas
                const colWidths = [
                    { wch: 5 },   // #
                    { wch: 15 },  // ID Minuta
                    { wch: 50 },  // Descripción
                    { wch: 20 },  // Responsable
                    { wch: 18 },  // Fecha Compromiso
                    { wch: 12 },  // Estado
                    { wch: 18 },  // Fecha Minuta
                    { wch: 30 },  // Lugar
                    { wch: 20 }   // Convoca
                ];
                ws['!cols'] = colWidths;

                // Agregar hoja al libro
                XLSX.utils.book_append_sheet(wb, ws, 'Repositorio de Acuerdos');

                // Generar nombre de archivo con fecha
                const fecha = new Date().toLocaleDateString('es-ES').replace(/\//g, '-');
                const filename = `Repositorio_Acuerdos_${fecha}.xlsx`;

                // Descargar archivo
                XLSX.writeFile(wb, filename);

                setMessage({ type: 'success', text: `Archivo Excel exportado: ${filename}` });
            };

            // Función para exportar a PDF
            const exportToPDF = (data) => {
                if (!data || data.length === 0) {
                    setMessage({ type: 'error', text: 'No hay datos para exportar' });
                    return;
                }

                const doc = new jsPDF('l', 'mm', 'a4'); // Orientación horizontal

                // Título
                doc.setFontSize(16);
                doc.setFont("helvetica", "bold");
                doc.text('Repositorio de Acuerdos', 20, 20);

                // Fecha de generación
                doc.setFontSize(10);
                doc.setFont("helvetica", "normal");
                const fechaGeneracion = new Date().toLocaleDateString('es-ES');
                doc.text(`Generado el: ${fechaGeneracion}`, 20, 30);
                doc.text(`Total de acuerdos: ${data.length}`, 20, 35);

                // Preparar datos para la tabla
                const tableData = data.map((acuerdo, index) => [
                    index + 1,
                    acuerdo.minuta_id || 'N/A',
                    acuerdo.descripcion ?
                        (acuerdo.descripcion.length > 40 ? acuerdo.descripcion.substring(0, 40) + '...' : acuerdo.descripcion) :
                        'Sin descripción',
                    acuerdo.responsable || 'No asignado',
                    acuerdo.fecha_aproximada || 'No definida',
                    getStatusDisplay(calculateAcuerdoStatus(acuerdo.fecha_aproximada, acuerdo.estado)).text,
                    acuerdo.fecha_minuta || 'No disponible',
                    acuerdo.lugar ?
                        (acuerdo.lugar.length > 20 ? acuerdo.lugar.substring(0, 20) + '...' : acuerdo.lugar) :
                        'No especificado',
                    acuerdo.convoca ?
                        (acuerdo.convoca.length > 15 ? acuerdo.convoca.substring(0, 15) + '...' : acuerdo.convoca) :
                        'No especificado'
                ]);

                // Crear tabla
                doc.autoTable({
                    head: [['#', 'ID Minuta', 'Descripción', 'Responsable', 'Fecha Compromiso', 'Estado', 'Fecha Minuta', 'Lugar', 'Convoca']],
                    body: tableData,
                    startY: 45,
                    styles: {
                        fontSize: 8,
                        cellPadding: 2
                    },
                    headStyles: {
                        fillColor: [52, 58, 64],
                        textColor: 255,
                        fontStyle: 'bold'
                    },
                    columnStyles: {
                        0: { halign: 'center', cellWidth: 10 },  // #
                        1: { cellWidth: 25 },                    // ID Minuta
                        2: { cellWidth: 60 },                    // Descripción
                        3: { cellWidth: 30 },                    // Responsable
                        4: { cellWidth: 25 },                    // Fecha Compromiso
                        5: { halign: 'center', cellWidth: 20 },  // Estado
                        6: { cellWidth: 25 },                    // Fecha Minuta
                        7: { cellWidth: 35 },                    // Lugar
                        8: { cellWidth: 30 }                     // Convoca
                    },
                    didParseCell: function(data) {
                        // Colorear celdas de estado
                        if (data.column.index === 5) { // Columna Estado
                            const estado = data.cell.text[0];
                            if (estado.includes('Vencido')) {
                                data.cell.styles.fillColor = [248, 215, 218]; // Rojo claro
                                data.cell.styles.textColor = [114, 28, 36];   // Rojo oscuro
                            } else if (estado.includes('Vigente')) {
                                data.cell.styles.fillColor = [255, 243, 205]; // Amarillo claro
                                data.cell.styles.textColor = [133, 77, 14];   // Amarillo oscuro
                            } else if (estado.includes('Completado')) {
                                data.cell.styles.fillColor = [212, 237, 218]; // Verde claro
                                data.cell.styles.textColor = [21, 87, 36];    // Verde oscuro
                            } else {
                                data.cell.styles.fillColor = [233, 236, 239]; // Gris claro
                                data.cell.styles.textColor = [108, 117, 125]; // Gris oscuro
                            }
                        }
                    }
                });

                // Generar nombre de archivo con fecha
                const fecha = new Date().toLocaleDateString('es-ES').replace(/\//g, '-');
                const filename = `Repositorio_Acuerdos_${fecha}.pdf`;

                // Descargar archivo
                doc.save(filename);

                setMessage({ type: 'success', text: `Archivo PDF exportado: ${filename}` });
            };

            // Función para actualizar estado de acuerdo
            const updateAcuerdoStatus = async (acuerdoId, nuevoEstado) => {
                try {
                    setIsLoading(true);

                    // Debug: verificar que tenemos el ID
                    console.log('Actualizando acuerdo:', { acuerdoId, nuevoEstado });

                    if (!acuerdoId) {
                        setMessage({ type: 'error', text: 'ID de acuerdo requerido para actualización' });
                        return;
                    }

                    const response = await fetch('api/minutas.php', {
                        method: 'PUT',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            action: 'update_acuerdo_status',
                            acuerdo_id: acuerdoId,
                            estado: nuevoEstado
                        })
                    });

                    const data = await response.json();

                    if (data.success) {
                        const estadoTexto = nuevoEstado === 'completado' ? 'Completado' : 'Vigente';
                        setMessage({
                            type: 'success',
                            text: `Estado del acuerdo actualizado a: ${estadoTexto}`
                        });

                        // Recargar lista de acuerdos
                        loadAcuerdosList();
                    } else {
                        setMessage({ type: 'error', text: data.error || 'Error al actualizar el estado del acuerdo' });
                    }
                } catch (error) {
                    setMessage({ type: 'error', text: 'Error de conexión al actualizar el estado' });
                } finally {
                    setIsLoading(false);
                }
            };

            const resetForm = () => {
                setFormData({
                    fechaHora: '',
                    lugar: '',
                    duracion: '',
                    convoca: '',
                    antecedentes: '',
                    objetivo: '',
                    relator: '',
                    ordenDia: '',
                    proximaReunion: '',
                    asistentes: [{ nombre: '', area: '', extensionCorreo: '', firma: '', isCustom: true }],
                    asuntosTratados: [''],
                    acuerdos: [{ descripcion: '', responsable: '', fechaAprox: '' }]
                });
            };

            // Event handlers (mantenidos del código original)
            const handleInputChange = (e, index = null, field = null, arrayField = null) => {
                if (arrayField) {
                    const updatedArray = [...formData[arrayField]];
                    if (arrayField === 'asistentes' && field === 'nombre') {
                        const selectedAsistente = asistentesPredefinidos.find(a => a.nombre === e.target.value);
                        updatedArray[index] = {
                            ...updatedArray[index],
                            nombre: e.target.value,
                            area: selectedAsistente ? selectedAsistente.area : updatedArray[index].area,
                            extensionCorreo: selectedAsistente ? selectedAsistente.extensionCorreo : updatedArray[index].extensionCorreo,
                            isCustom: !selectedAsistente
                        };
                    } else if (arrayField === 'asuntosTratados') {
                        updatedArray[index] = e.target.value;
                    } else {
                        updatedArray[index][field] = e.target.value;
                    }
                    setFormData({ ...formData, [arrayField]: updatedArray });
                } else {
                    // Usar updateFormData para campos principales que pueden afectar duplicados
                    if (['fechaHora', 'lugar', 'convoca'].includes(e.target.name)) {
                        updateFormData(e.target.name, e.target.value);
                    } else {
                        setFormData({ ...formData, [e.target.name]: e.target.value });
                    }
                }
            };

            const addAsistente = () => {
                setFormData({
                    ...formData,
                    asistentes: [...formData.asistentes, { nombre: '', area: '', extensionCorreo: '', firma: '', isCustom: true }]
                });
            };

            const removeAsistente = (index) => {
                setFormData({
                    ...formData,
                    asistentes: formData.asistentes.filter((_, i) => i !== index)
                });
            };

            const addAcuerdo = () => {
                setFormData({
                    ...formData,
                    acuerdos: [...formData.acuerdos, { descripcion: '', responsable: '', fechaAprox: '' }]
                });
            };

            const removeAcuerdo = (index) => {
                setFormData({
                    ...formData,
                    acuerdos: formData.acuerdos.filter((_, i) => i !== index)
                });
            };

            const addAsunto = () => {
                setFormData({
                    ...formData,
                    asuntosTratados: [...formData.asuntosTratados, '']
                });
            };

            const removeAsunto = (index) => {
                setFormData({
                    ...formData,
                    asuntosTratados: formData.asuntosTratados.filter((_, i) => i !== index)
                });
            };

            // Funciones de exportación (mantenidas del código original)
            const generatePDF = (data = formData) => {
                const doc = new jsPDF();
                doc.setFontSize(16);
                doc.setFont("helvetica", "bold");
                doc.text('Reunión de Trabajo', 20, 20);
                doc.setFontSize(12);

                let y = 30;
                const fields = [
                    { label: 'Fecha y Hora:', value: data.fechaHora || data.fecha_hora },
                    { label: 'Lugar:', value: data.lugar },
                    { label: 'Duración:', value: data.duracion },
                    { label: 'Convoca:', value: data.convoca },
                    { label: 'Relator:', value: data.relator }
                ];

                fields.forEach(field => {
                    doc.setFont("helvetica", "bold");
                    doc.text(field.label, 20, y);
                    doc.setFont("helvetica", "normal");
                    doc.text(field.value || '', 60, y);
                    y += 10;
                });

                const multiLineFields = [
                    { label: 'Antecedentes:', value: data.antecedentes },
                    { label: 'Objetivo:', value: data.objetivo },
                    { label: 'Orden del Día:', value: data.ordenDia || data.orden_dia },
                    { label: 'Fecha Próxima Reunión:', value: data.proximaReunion || data.proxima_reunion }
                ];

                multiLineFields.forEach(field => {
                    doc.setFont("helvetica", "bold");
                    doc.text(field.label, 20, y);
                    doc.setFont("helvetica", "normal");
                    const splitText = doc.splitTextToSize(field.value || '', 170);
                    doc.text(splitText, 20, y + 10);
                    y += 10 + splitText.length * 7;
                });

                doc.setFont("helvetica", "bold");
                doc.text('Asistentes de Reunión:', 20, y);
                y += 10;
                doc.setFont("helvetica", "normal");
                data.asistentes.forEach((asistente, index) => {
                    const text = `${index + 1}. ${asistente.nombre || ''} - ${asistente.area || ''} - ${asistente.extensionCorreo || ''} - Firma: ${asistente.firma || ''}`;
                    doc.text(text, 20, y);
                    y += 10;
                });

                y += 10;
                doc.setFont("helvetica", "bold");
                doc.text('Inicio de Reunión', 20, y);
                y += 10;

                doc.setFont("helvetica", "bold");
                doc.text('Asuntos Tratados:', 20, y);
                doc.setFont("helvetica", "normal");
                const asuntosText = data.asuntosTratados
                    .map((asunto, index) => asunto ? `${index + 1}. ${asunto}` : '')
                    .filter(a => a)
                    .join('\n');
                const asuntosSplit = doc.splitTextToSize(asuntosText || '', 170);
                doc.text(asuntosSplit, 20, y + 10);
                y += 10 + asuntosSplit.length * 7;

                doc.setFont("helvetica", "bold");
                doc.text('Acuerdos:', 20, y);
                y += 10;
                data.acuerdos.forEach((acuerdo, index) => {
                    doc.setFont("helvetica", "normal");
                    const descSplit = doc.splitTextToSize(`${index + 1}. ${acuerdo.descripcion || ''}`, 170);
                    doc.text(descSplit, 20, y);
                    y += descSplit.length * 7;
                    doc.setFont("helvetica", "bold");
                    doc.text('Responsable:', 20, y);
                    doc.setFont("helvetica", "normal");
                    doc.text(acuerdo.responsable || '', 60, y);
                    y += 10;
                    doc.setFont("helvetica", "bold");
                    doc.text('Fecha Aprox.:', 20, y);
                    doc.setFont("helvetica", "normal");
                    doc.text(acuerdo.fechaAprox || '', 60, y);
                    y += 10;
                });

                // Generar nombre de archivo con ID de minuta
                const fileName = data.minuta_id ? `${data.minuta_id}.pdf` : 'minuta.pdf';

                // Agregar número de página en el pie de página
                const pageCount = doc.internal.getNumberOfPages();
                for (let i = 1; i <= pageCount; i++) {
                    doc.setPage(i);
                    doc.setFontSize(10);
                    doc.setFont("helvetica", "normal");
                    doc.text(`Página ${i} de ${pageCount}`, 105, 285, { align: 'center' });
                }

                doc.save(fileName);
            };

            const generateWord = (data = formData) => {
                const htmlContent = `\uFEFF<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:w="urn:schemas-microsoft-com:office:word" xmlns="http://www.w3.org/TR/REC-html40">
                    <head>
                        <meta charset="UTF-8">
                        <meta name="ProgId" content="Word.Document">
                        <meta name="Generator" content="Microsoft Word 15">
                        <meta name="Originator" content="Microsoft Word 15">
                        <!--[if !mso]>
                        <style>
                            v\\:* {behavior:url(#default#VML);}
                            o\\:* {behavior:url(#default#VML);}
                            w\\:* {behavior:url(#default#VML);}
                            .shape {behavior:url(#default#VML);}
                        </style>
                        <![endif]-->
                        <style>
                            @page {
                                size: 8.5in 11in;
                                margin: 1in;
                                mso-header-margin: 0.5in;
                                mso-footer-margin: 0.5in;
                                mso-paper-source: 0;
                            }
                            @page Section1 {
                                mso-footer: f1;
                            }
                            body {
                                font-family: Arial, sans-serif;
                                font-size: 12pt;
                                margin: 0;
                                padding: 0;
                            }
                            .footer {
                                position: fixed;
                                bottom: 0;
                                width: 100%;
                                text-align: center;
                                font-size: 10pt;
                                border-top: 1px solid #000;
                                padding-top: 5px;
                                margin-top: 20px;
                            }
                        </style>
                    </head>
                    <body class="Section1">
                        <h1 style="font-size:16pt;text-align:center;">Reunión de Trabajo</h1>
                        <p><b>Fecha y Hora:</b> ${data.fechaHora || data.fecha_hora || ''}</p>
                        <p><b>Lugar:</b> ${data.lugar || ''}</p>
                        <p><b>Duración:</b> ${data.duracion || ''}</p>
                        <p><b>Convoca:</b> ${data.convoca || ''}</p>
                        <p><b>Antecedentes:</b> ${(data.antecedentes || '').replace(/\n/g, '<br>')}</p>
                        <p><b>Objetivo:</b> ${(data.objetivo || '').replace(/\n/g, '<br>')}</p>
                        <p><b>Relator:</b> ${data.relator || ''}</p>

                         <table border="1" style="border-collapse:collapse;width:100%;font-size:10pt;">
                            <tr style="background-color: rgb(209, 214, 218);">
                                <h3 style="width:25%; text-align: center;">Orden del Día:</h3>
                            </tr>
                         </table>

                        <p style="text-align:center;"><b></b><br>${(data.ordenDia || data.orden_dia || '').replace(/\n/g, '<br>')}</p>
                        <p><b>Fecha Próxima Reunión:</b> ${data.proximaReunion || data.proxima_reunion || ''}</p>
                        <h2 style="font-size:14pt;text-align:center;">Asistentes de Reunión</h2>
                        <table border="1" style="border-collapse:collapse;width:100%;font-size:10pt;">
                            <tr>
                                <th style="width:25%;">Nombre</th>
                                <th style="width:25%;">Área</th>
                                <th style="width:25%;">Extensión y Correo</th>
                                <th style="width:25%;">Firma</th>
                            </tr>
                            ${data.asistentes.map(a => `
                                <tr>
                                    <td>${a.nombre || ''}</td>
                                    <td>${a.area || ''}</td>
                                    <td>${a.extensionCorreo || ''}</td>
                                    <td>${a.firma || ''}</td>
                                </tr>
                            `).join('')}
                        </table>
                        <h2 style="font-size:14pt;text-align:center;">Inicio de Reunión</h2>
                        <p style="text-align:center;"><b>Asuntos Tratados:</b><br>${data.asuntosTratados
                            .map((asunto, index) => asunto ? `${index + 1}. ${asunto.replace(/\n/g, '<br>')}` : '')
                            .filter(a => a)
                            .join('<br>') || ''}</p>
                        <h2 style="font-size:14pt;text-align:center;">Acuerdos</h2>
                        <div style="text-align:left;">
                        ${data.acuerdos.map((a, i) => `
                            <p style="text-align:left;">${i + 1}. ${(a.descripcion || '').replace(/\n/g, '<br>')}<br>
                            <b>Responsable:</b> ${a.responsable || ''}<br>
                            <b>Fecha Aprox.:</b> ${a.fechaAprox || ''}</p>
                        `).join('')}
                        </div>

                        <!-- Pie de página con numeración -->
                        <div class="footer">
                            <p style="margin: 0; font-size: 10pt; text-align: center;">
                                <span style="mso-field-code: PAGE ">1</span> de <span style="mso-field-code: NUMPAGES ">1</span>
                            </p>
                        </div>

                        <!-- Configuración de pie de página para Word -->
                        <div style="mso-element:footer" id="f1">
                            <p style="text-align:center; font-size:10pt; margin:0;">
                                Página <span style="mso-field-code: PAGE ">1</span> de <span style="mso-field-code: NUMPAGES ">1</span>
                            </p>
                        </div>
                    </body>
                </html>`;
                const blob = new Blob([htmlContent], { type: 'application/msword' });
                const link = document.createElement('a');
                link.href = URL.createObjectURL(blob);

                // Generar nombre de archivo con ID de minuta
                const fileName = data.minuta_id ? `${data.minuta_id}.doc` : 'minuta.doc';
                link.download = fileName;
                link.click();
            };

            // Componente de navegación por pestañas
            const TabNavigation = () => {
                return React.createElement(
                    'ul',
                    { className: 'nav nav-tabs mb-4' },
                    [
                        { id: 'nueva', label: isEditing ? `✏️ Editando: ${editingMinuta?.minuta_id}` : 'Nueva Minuta' },
                        { id: 'consultar', label: 'Consultar Minutas' },
                        { id: 'acuerdos', label: 'Repositorio de Acuerdos' }
                    ].map(tab => React.createElement(
                        'li',
                        { key: tab.id, className: 'nav-item' },
                        React.createElement(
                            'button',
                            {
                                className: `nav-link ${activeTab === tab.id ? 'active' : ''}`,
                                onClick: () => {
                                    setActiveTab(tab.id);
                                    setMessage({ type: '', text: '' });
                                    setViewingMinuta(null);
                                }
                            },
                            tab.label
                        )
                    ))
                );
            };

            // Componente de mensajes
            const MessageAlert = () => {
                if (!message.text) return null;

                return React.createElement(
                    'div',
                    {
                        className: `alert ${message.type === 'success' ? 'alert-success' : 'alert-danger'} alert-dismissible fade show`,
                        role: 'alert'
                    },
                    message.text,
                    React.createElement(
                        'button',
                        {
                            type: 'button',
                            className: 'btn-close',
                            onClick: () => setMessage({ type: '', text: '' })
                        }
                    )
                );
            };

            // Componente de carga
            const LoadingSpinner = () => {
                if (!isLoading) return null;

                return React.createElement(
                    'div',
                    { className: 'loading' },
                    React.createElement(
                        'div',
                        { className: 'spinner-border text-primary', role: 'status' },
                        React.createElement(
                            'span',
                            { className: 'visually-hidden' },
                            'Cargando...'
                        )
                    )
                );
            };

            // Componente de búsqueda de minutas
            const SearchSection = () => {
                return React.createElement(
                    'div',
                    { className: 'search-section' },
                    React.createElement(
                        'h4',
                        { className: 'mb-3' },
                        'Buscar Minuta por ID'
                    ),
                    React.createElement(
                        'div',
                        { className: 'row g-3' },
                        React.createElement(
                            'div',
                            { className: 'col-md-8' },
                            React.createElement(
                                'input',
                                {
                                    type: 'text',
                                    className: 'form-control',
                                    placeholder: 'Ingrese el ID de la minuta (ej: MIN-2024-01-001)',
                                    value: searchId,
                                    onChange: (e) => setSearchId(e.target.value),
                                    onKeyPress: (e) => {
                                        if (e.key === 'Enter') {
                                            searchMinuta(searchId);
                                        }
                                    }
                                }
                            )
                        ),
                        React.createElement(
                            'div',
                            { className: 'col-md-4' },
                            React.createElement(
                                'button',
                                {
                                    className: 'btn btn-primary w-100',
                                    onClick: () => searchMinuta(searchId),
                                    disabled: isLoading
                                },
                                'Buscar'
                            )
                        )
                    )
                );
            };

            // Componente de lista de minutas
            const MinutasList = () => {
                return React.createElement(
                    'div',
                    null,
                    React.createElement(
                        'h4',
                        { className: 'mb-3' },
                        'Minutas Recientes'
                    ),
                    React.createElement(
                        'div',
                        { className: 'row' },
                        minutasList.map(minuta => React.createElement(
                            'div',
                            { key: minuta.minuta_id, className: 'col-md-6 col-lg-4 mb-3' },
                            React.createElement(
                                'div',
                                {
                                    className: 'card minuta-card h-100',
                                    onClick: () => {
                                        setSearchId(minuta.minuta_id);
                                        searchMinuta(minuta.minuta_id);
                                    }
                                },
                                React.createElement(
                                    'div',
                                    { className: 'card-body' },
                                    React.createElement(
                                        'h6',
                                        { className: 'card-title text-primary' },
                                        minuta.minuta_id
                                    ),
                                    React.createElement(
                                        'p',
                                        { className: 'card-text small' },
                                        React.createElement(
                                            'strong',
                                            null,
                                            'Fecha: '
                                        ),
                                        minuta.fecha_hora,
                                        React.createElement('br'),
                                        React.createElement(
                                            'strong',
                                            null,
                                            'Lugar: '
                                        ),
                                        minuta.lugar,
                                        React.createElement('br'),
                                        React.createElement(
                                            'strong',
                                            null,
                                            'Convoca: '
                                        ),
                                        minuta.convoca
                                    )
                                )
                            )
                        ))
                    )
                );
            };

            // Componente para mostrar minuta encontrada
            const MinutaViewer = ({ minuta }) => {
                if (!minuta) return null;

                return React.createElement(
                    'div',
                    { className: 'card' },
                    React.createElement(
                        'div',
                        { className: 'card-header d-flex justify-content-between align-items-center' },
                        React.createElement(
                            'h5',
                            { className: 'mb-0' },
                            `Minuta: ${minuta.minuta_id}`
                        ),
                        React.createElement(
                            'div',
                            { className: 'd-flex gap-2' },
                            React.createElement(
                                'button',
                                {
                                    className: 'btn btn-warning btn-sm',
                                    onClick: () => startEditMinuta(minuta)
                                },
                                '✏️ Editar'
                            ),
                            React.createElement(
                                'button',
                                {
                                    className: 'btn btn-success btn-sm',
                                    onClick: () => generatePDF(minuta)
                                },
                                '📄 PDF'
                            ),
                            React.createElement(
                                'button',
                                {
                                    className: 'btn btn-primary btn-sm',
                                    onClick: () => generateWord(minuta)
                                },
                                '📝 Word'
                            )
                        )
                    ),
                    React.createElement(
                        'div',
                        { className: 'card-body' },
                        React.createElement(
                            'div',
                            { className: 'row' },
                            React.createElement(
                                'div',
                                { className: 'col-md-6' },
                                React.createElement(
                                    'p',
                                    null,
                                    React.createElement('strong', null, 'Fecha y Hora: '),
                                    minuta.fecha_hora
                                ),
                                React.createElement(
                                    'p',
                                    null,
                                    React.createElement('strong', null, 'Lugar: '),
                                    minuta.lugar
                                ),
                                React.createElement(
                                    'p',
                                    null,
                                    React.createElement('strong', null, 'Duración: '),
                                    minuta.duracion
                                ),
                                React.createElement(
                                    'p',
                                    null,
                                    React.createElement('strong', null, 'Convoca: '),
                                    minuta.convoca
                                )
                            ),
                            React.createElement(
                                'div',
                                { className: 'col-md-6' },
                                React.createElement(
                                    'p',
                                    null,
                                    React.createElement('strong', null, 'Relator: '),
                                    minuta.relator
                                ),
                                React.createElement(
                                    'p',
                                    null,
                                    React.createElement('strong', null, 'Próxima Reunión: '),
                                    minuta.proxima_reunion || 'No definida'
                                )
                            )
                        ),
                        minuta.antecedentes && React.createElement(
                            'div',
                            { className: 'mb-3' },
                            React.createElement('strong', null, 'Antecedentes:'),
                            React.createElement('p', null, minuta.antecedentes)
                        ),
                        minuta.objetivo && React.createElement(
                            'div',
                            { className: 'mb-3' },
                            React.createElement('strong', null, 'Objetivo:'),
                            React.createElement('p', null, minuta.objetivo)
                        ),
                        minuta.orden_dia && React.createElement(
                            'div',
                            { className: 'mb-3' },
                            React.createElement('strong', null, 'Orden del Día:'),
                            React.createElement('p', null, minuta.orden_dia)
                        ),
                        React.createElement(
                            'div',
                            { className: 'mb-3' },
                            React.createElement('strong', null, 'Asistentes:'),
                            React.createElement(
                                'ul',
                                null,
                                minuta.asistentes.map((asistente, index) => React.createElement(
                                    'li',
                                    { key: index },
                                    `${asistente.nombre} - ${asistente.area} - ${asistente.extensionCorreo}`
                                ))
                            )
                        ),
                        React.createElement(
                            'div',
                            { className: 'mb-3' },
                            React.createElement('strong', null, 'Asuntos Tratados:'),
                            React.createElement(
                                'ol',
                                null,
                                minuta.asuntosTratados.map((asunto, index) => React.createElement(
                                    'li',
                                    { key: index },
                                    asunto
                                ))
                            )
                        ),
                        React.createElement(
                            'div',
                            { className: 'mb-3' },
                            React.createElement('strong', null, 'Acuerdos:'),
                            React.createElement(
                                'ol',
                                null,
                                minuta.acuerdos.map((acuerdo, index) => React.createElement(
                                    'li',
                                    { key: index },
                                    React.createElement(
                                        'div',
                                        null,
                                        React.createElement('p', null, acuerdo.descripcion),
                                        React.createElement('small', null, `Responsable: ${acuerdo.responsable || 'No asignado'}`),
                                        React.createElement('br'),
                                        React.createElement('small', null, `Fecha aproximada: ${acuerdo.fechaAprox || 'No definida'}`)
                                    )
                                ))
                            )
                        )
                    )
                );
            };

            // Componente para mostrar repositorio de acuerdos
            const AcuerdosRepository = () => {
                if (acuerdosList.length === 0) {
                    return React.createElement(
                        'div',
                        { className: 'text-center py-5' },
                        React.createElement(
                            'h4',
                            { className: 'text-muted' },
                            'No hay acuerdos registrados'
                        ),
                        React.createElement(
                            'p',
                            { className: 'text-muted' },
                            'Los acuerdos aparecerán aquí cuando se creen minutas con acuerdos.'
                        )
                    );
                }

                // Obtener lista única de responsables para el filtro
                const responsablesUnicos = [...new Set(acuerdosList
                    .map(a => a.responsable)
                    .filter(r => r && r.trim() !== '')
                )].sort();

                return React.createElement(
                    'div',
                    null,
                    // Header con título y botón actualizar
                    React.createElement(
                        'div',
                        { className: 'd-flex justify-content-between align-items-center mb-4' },
                        React.createElement(
                            'h4',
                            { className: 'mb-0' },
                            `Repositorio de Acuerdos (${filteredAcuerdos.length} de ${acuerdosList.length})`
                        ),
                        React.createElement(
                            'button',
                            {
                                className: 'btn btn-outline-primary btn-sm',
                                onClick: loadAcuerdosList
                            },
                            '🔄 Actualizar'
                        )
                    ),

                    // Panel de filtros
                    React.createElement(
                        'div',
                        { className: 'card mb-4' },
                        React.createElement(
                            'div',
                            { className: 'card-header' },
                            React.createElement(
                                'h6',
                                { className: 'mb-0' },
                                '🔍 Filtros de Búsqueda'
                            )
                        ),
                        React.createElement(
                            'div',
                            { className: 'card-body' },
                            React.createElement(
                                'div',
                                { className: 'row g-3' },
                                // Filtro por responsable
                                React.createElement(
                                    'div',
                                    { className: 'col-md-3' },
                                    React.createElement(
                                        'label',
                                        { className: 'form-label' },
                                        'Responsable'
                                    ),
                                    React.createElement(
                                        'select',
                                        {
                                            className: 'form-select',
                                            value: filters.responsable,
                                            onChange: (e) => setFilters({...filters, responsable: e.target.value})
                                        },
                                        React.createElement('option', { value: '' }, 'Todos'),
                                        responsablesUnicos.map(responsable =>
                                            React.createElement('option', { key: responsable, value: responsable }, responsable)
                                        )
                                    )
                                ),
                                // Filtro por estado
                                React.createElement(
                                    'div',
                                    { className: 'col-md-3' },
                                    React.createElement(
                                        'label',
                                        { className: 'form-label' },
                                        'Estado'
                                    ),
                                    React.createElement(
                                        'select',
                                        {
                                            className: 'form-select',
                                            value: filters.estado,
                                            onChange: (e) => setFilters({...filters, estado: e.target.value})
                                        },
                                        React.createElement('option', { value: '' }, 'Todos'),
                                        React.createElement('option', { value: 'vigente' }, 'Vigente'),
                                        React.createElement('option', { value: 'vencido' }, 'Vencido'),
                                        React.createElement('option', { value: 'completado' }, 'Completado')
                                    )
                                ),
                                // Filtro fecha desde
                                React.createElement(
                                    'div',
                                    { className: 'col-md-3' },
                                    React.createElement(
                                        'label',
                                        { className: 'form-label' },
                                        'Fecha desde'
                                    ),
                                    React.createElement(
                                        'input',
                                        {
                                            type: 'date',
                                            className: 'form-control',
                                            value: filters.fechaDesde,
                                            onChange: (e) => setFilters({...filters, fechaDesde: e.target.value})
                                        }
                                    )
                                ),
                                // Filtro fecha hasta
                                React.createElement(
                                    'div',
                                    { className: 'col-md-3' },
                                    React.createElement(
                                        'label',
                                        { className: 'form-label' },
                                        'Fecha hasta'
                                    ),
                                    React.createElement(
                                        'input',
                                        {
                                            type: 'date',
                                            className: 'form-control',
                                            value: filters.fechaHasta,
                                            onChange: (e) => setFilters({...filters, fechaHasta: e.target.value})
                                        }
                                    )
                                )
                            ),
                            React.createElement(
                                'div',
                                { className: 'mt-3' },
                                React.createElement(
                                    'button',
                                    {
                                        className: 'btn btn-outline-secondary btn-sm',
                                        onClick: clearFilters
                                    },
                                    '🗑️ Limpiar Filtros'
                                )
                            )
                        )
                    ),
                    // Botones de exportación
                    React.createElement(
                        'div',
                        { className: 'export-buttons mb-3 d-flex gap-2 align-items-center' },
                        React.createElement(
                            'span',
                            { className: 'fw-bold text-muted' },
                            '📤 Exportar datos:'
                        ),
                        React.createElement(
                            'button',
                            {
                                className: 'btn btn-success btn-sm',
                                onClick: () => exportToExcel(filteredAcuerdos),
                                disabled: filteredAcuerdos.length === 0
                            },
                            '📊 Exportar a Excel'
                        ),
                        React.createElement(
                            'button',
                            {
                                className: 'btn btn-danger btn-sm',
                                onClick: () => exportToPDF(filteredAcuerdos),
                                disabled: filteredAcuerdos.length === 0
                            },
                            '📄 Exportar a PDF'
                        )
                    ),

                    // Tabla de acuerdos
                    filteredAcuerdos.length === 0 ? React.createElement(
                        'div',
                        { className: 'text-center py-5' },
                        React.createElement(
                            'h5',
                            { className: 'text-muted' },
                            '🔍 No se encontraron acuerdos con los filtros aplicados'
                        ),
                        React.createElement(
                            'p',
                            { className: 'text-muted' },
                            'Intenta ajustar los filtros para ver más resultados.'
                        )
                    ) : React.createElement(
                        'div',
                        { className: 'table-container' },
                        React.createElement(
                            'div',
                            { className: 'table-responsive' },
                            React.createElement(
                                'table',
                                { className: 'table table-striped table-hover' },
                                React.createElement(
                                    'thead',
                                    { className: 'table-dark' },
                                    React.createElement(
                                        'tr',
                                        null,
                                        React.createElement('th', { scope: 'col', className: 'text-center col-numero' }, '#'),
                                        React.createElement('th', { scope: 'col', className: 'col-id-minuta' }, 'ID Minuta'),
                                        React.createElement('th', { scope: 'col', className: 'col-descripcion' }, 'Descripción del Acuerdo'),
                                        React.createElement('th', { scope: 'col', className: 'col-responsable' }, 'Responsable'),
                                        React.createElement('th', { scope: 'col', className: 'col-fecha-compromiso' }, 'Fecha Compromiso'),
                                        React.createElement('th', { scope: 'col', className: 'col-estado' }, 'Estado'),
                                        React.createElement('th', { scope: 'col', className: 'col-fecha-minuta' }, 'Fecha Minuta'),
                                        React.createElement('th', { scope: 'col', className: 'col-lugar' }, 'Lugar'),
                                        React.createElement('th', { scope: 'col', className: 'col-convoca' }, 'Convoca'),
                                        React.createElement('th', { scope: 'col', className: 'text-center col-acciones' }, 'Ver Minuta')
                                    )
                                ),
                            React.createElement(
                                'tbody',
                                null,
                                filteredAcuerdos.map((acuerdo, index) => {
                                    // Debug: verificar estructura del acuerdo
                                    if (index === 0) {
                                        console.log('Estructura del acuerdo:', acuerdo);
                                    }
                                    return React.createElement(
                                    'tr',
                                    { key: index },
                                    React.createElement(
                                        'td',
                                        { className: 'text-center fw-bold' },
                                        index + 1
                                    ),
                                    React.createElement(
                                        'td',
                                        null,
                                        React.createElement(
                                            'span',
                                            { className: 'badge bg-primary' },
                                            acuerdo.minuta_id
                                        )
                                    ),
                                    React.createElement(
                                        'td',
                                        { style: { maxWidth: '300px' } },
                                        React.createElement(
                                            'div',
                                            {
                                                className: 'text-truncate',
                                                title: acuerdo.descripcion || 'Sin descripción'
                                            },
                                            acuerdo.descripcion || React.createElement(
                                                'span',
                                                { className: 'text-muted fst-italic' },
                                                'Sin descripción'
                                            )
                                        )
                                    ),
                                    React.createElement(
                                        'td',
                                        null,
                                        acuerdo.responsable || React.createElement(
                                            'span',
                                            { className: 'text-muted fst-italic' },
                                            'No asignado'
                                        )
                                    ),
                                    React.createElement(
                                        'td',
                                        null,
                                        acuerdo.fecha_aproximada || React.createElement(
                                            'span',
                                            { className: 'text-muted fst-italic' },
                                            'No definida'
                                        )
                                    ),
                                    React.createElement(
                                        'td',
                                        { className: 'text-center' },
                                        (() => {
                                            const estadoReal = calculateAcuerdoStatus(acuerdo.fecha_aproximada, acuerdo.estado);
                                            const displayInfo = getStatusDisplay(estadoReal);

                                            return React.createElement(
                                                'select',
                                                {
                                                    className: `form-select form-select-sm ${displayInfo.className}`,
                                                    value: acuerdo.estado || 'vigente',
                                                    onChange: (e) => updateAcuerdoStatus(acuerdo.id, e.target.value),
                                                    disabled: isLoading,
                                                    style: { minWidth: '140px' }
                                                },
                                                React.createElement(
                                                    'option',
                                                    { value: 'vigente' },
                                                    estadoReal === 'vencido' ? '⚠️ Vencido' : '🕒 Vigente'
                                                ),
                                                React.createElement(
                                                    'option',
                                                    { value: 'completado' },
                                                    '✅ Completado'
                                                )
                                            );
                                        })()
                                    ),
                                    React.createElement(
                                        'td',
                                        null,
                                        acuerdo.fecha_minuta || React.createElement(
                                            'span',
                                            { className: 'text-muted fst-italic' },
                                            'No disponible'
                                        )
                                    ),
                                    React.createElement(
                                        'td',
                                        { style: { maxWidth: '200px' } },
                                        React.createElement(
                                            'div',
                                            {
                                                className: 'text-truncate',
                                                title: acuerdo.lugar || 'No especificado'
                                            },
                                            acuerdo.lugar || React.createElement(
                                                'span',
                                                { className: 'text-muted fst-italic' },
                                                'No especificado'
                                            )
                                        )
                                    ),
                                    React.createElement(
                                        'td',
                                        { style: { maxWidth: '150px' } },
                                        React.createElement(
                                            'div',
                                            {
                                                className: 'text-truncate',
                                                title: acuerdo.convoca || 'No especificado'
                                            },
                                            acuerdo.convoca || React.createElement(
                                                'span',
                                                { className: 'text-muted fst-italic' },
                                                'No especificado'
                                            )
                                        )
                                    ),
                                    React.createElement(
                                        'td',
                                        { className: 'text-center' },
                                        React.createElement(
                                            'button',
                                            {
                                                className: 'btn btn-outline-primary btn-sm',
                                                onClick: () => goToMinuta(acuerdo.minuta_id),
                                                title: 'Ver minuta completa'
                                            },
                                            React.createElement(
                                                'i',
                                                { className: 'fas fa-file-alt me-1' }
                                            ),
                                            'Ver'
                                        )
                                    )
                                );
                                })
                            )
                        )
                    )
                )
                );
            };

            // Componente principal de renderizado
            return React.createElement(
                'div',
                { className: 'container my-4' },
                React.createElement(
                    'h1',
                    { className: 'text-center mb-4 display-4' },
                    'Sistema de Gestión de Minutas'
                ),
                React.createElement(TabNavigation),
                React.createElement(MessageAlert),
                React.createElement(LoadingSpinner),

                // Pestaña Nueva Minuta
                activeTab === 'nueva' && React.createElement(
                    'div',
                    null,
                    currentMinutaId && React.createElement(
                        'div',
                        { className: 'minuta-id-display' },
                        React.createElement(
                            'h3',
                            null,
                            `ID de Minuta: ${currentMinutaId}`
                        )
                    ),
                    React.createElement(
                        'div',
                        { className: 'card shadow' },
                        React.createElement(
                            'div',
                            { className: 'card-body' },
                            [
                                { label: 'Fecha y Hora', name: 'fechaHora', type: 'input', placeholder: 'Seleccione fecha y hora...', id: 'fechaHora' },
                                { label: 'Lugar', name: 'lugar', type: 'input', placeholder: 'Seleccione o escriba un lugar...', datalistId: 'lugares' },
                                { label: 'Duración', name: 'duracion', type: 'select', placeholder: 'Seleccione duración...' },
                                { label: 'Convoca', name: 'convoca', type: 'input', placeholder: 'Seleccione o escriba un usuario...', datalistId: 'users' },
                                { label: 'Antecedentes', name: 'antecedentes', type: 'textarea', placeholder: '', rows: 4 },
                                { label: 'Objetivo', name: 'objetivo', type: 'textarea', placeholder: '', rows: 4 },
                                { label: 'Relator', name: 'relator', type: 'input', placeholder: 'Seleccione o escriba un usuario...', datalistId: 'users' },
                                { label: 'Orden del Día', name: 'ordenDia', type: 'textarea', placeholder: '', rows: 6 },
                                { label: 'Fecha Próxima Reunión', name: 'proximaReunion', type: 'input', placeholder: 'Seleccione fecha y hora...', id: 'proximaReunion' }
                            ].map(field => React.createElement(
                                'div',
                                { key: field.name, className: 'mb-3' },
                                React.createElement(
                                    'label',
                                    { className: 'form-label' },
                                    field.label
                                ),
                                field.type === 'select' ? React.createElement(
                                    'select',
                                    {
                                        name: field.name,
                                        value: formData[field.name],
                                        onChange: handleInputChange,
                                        className: 'form-select'
                                    },
                                    React.createElement('option', { value: '' }, field.placeholder),
                                    duracionOpciones.map((option, i) => React.createElement(
                                        'option',
                                        { key: i, value: option },
                                        option
                                    ))
                                ) : React.createElement(
                                    field.type === 'input' ? 'input' : 'textarea',
                                    {
                                        name: field.name,
                                        id: field.id,
                                        value: formData[field.name],
                                        onChange: handleInputChange,
                                        className: 'form-control',
                                        placeholder: field.placeholder,
                                        rows: field.rows,
                                        list: field.datalistId
                                    }
                                ),
                                field.datalistId && React.createElement(
                                    'datalist',
                                    { id: field.datalistId },
                                    (field.datalistId === 'lugares' ? predefinedLugares : predefinedUsers).map((option, i) => React.createElement(
                                        'option',
                                        { key: i, value: option }
                                    ))
                                )
                            )),

                            // Sección de Asistentes (código original mantenido)
                            React.createElement(
                                'div',
                                { className: 'mb-4' },
                                React.createElement(
                                    'h2',
                                    { className: 'h4 mb-3 text-center' },
                                    'Asistentes de Reunión'
                                ),
                                formData.asistentes.map((asistente, index) => React.createElement(
                                    'div',
                                    { key: index, className: 'card mb-3' },
                                    React.createElement(
                                        'div',
                                        { className: 'card-body' },
                                        React.createElement(
                                            'div',
                                            { className: 'row g-3' },
                                            [
                                                {
                                                    label: 'Nombre',
                                                    type: 'input',
                                                    value: asistente.nombre,
                                                    onChange: e => handleInputChange(e, index, 'nombre', 'asistentes'),
                                                    placeholder: 'Seleccione o escriba un nombre...',
                                                    datalistId: `asistentes-${index}`
                                                },
                                                {
                                                    label: 'Área',
                                                    type: 'input',
                                                    value: asistente.area,
                                                    onChange: e => handleInputChange(e, index, 'area', 'asistentes'),
                                                    readOnly: !asistente.isCustom
                                                },
                                                {
                                                    label: 'Extensión y Correo',
                                                    type: 'input',
                                                    value: asistente.extensionCorreo,
                                                    onChange: e => handleInputChange(e, index, 'extensionCorreo', 'asistentes'),
                                                    readOnly: !asistente.isCustom
                                                },
                                                {
                                                    label: 'Firma',
                                                    type: 'input',
                                                    value: asistente.firma,
                                                    onChange: e => handleInputChange(e, index, 'firma', 'asistentes'),
                                                    placeholder: '',
                                                    className: 'firma-input'
                                                }
                                            ].map((field, i) => React.createElement(
                                                'div',
                                                { key: i, className: field.label === 'Firma' ? 'col-md-12' : 'col-md-6' },
                                                React.createElement(
                                                    'label',
                                                    { className: 'form-label' },
                                                    field.label
                                                ),
                                                React.createElement(
                                                    'input',
                                                    {
                                                        type: 'text',
                                                        value: field.value,
                                                        onChange: field.onChange,
                                                        readOnly: field.readOnly,
                                                        className: `form-control ${field.readOnly ? 'bg-light' : ''} ${field.className || ''}`,
                                                        placeholder: field.placeholder,
                                                        list: field.datalistId
                                                    }
                                                ),
                                                field.datalistId && React.createElement(
                                                    'datalist',
                                                    { id: field.datalistId },
                                                    asistentesPredefinidos.map((a, j) => React.createElement(
                                                        'option',
                                                        { key: j, value: a.nombre }
                                                    ))
                                                )
                                            ))
                                        ),
                                        React.createElement(
                                            'button',
                                            {
                                                onClick: () => removeAsistente(index),
                                                className: 'btn btn-link text-danger mt-2'
                                            },
                                            'Eliminar Asistente'
                                        )
                                    )
                                )),
                                React.createElement(
                                    'button',
                                    {
                                        onClick: addAsistente,
                                        className: 'btn btn-primary mt-2'
                                    },
                                    'Agregar Asistente'
                                )
                            ),

                            // Sección Inicio de Reunión
                            React.createElement(
                                'h2',
                                { className: 'h4 mb-3 text-start' },
                                'Inicio de Reunión'
                            ),

                            // Sección de Asuntos Tratados
                            React.createElement(
                                'div',
                                { className: 'mb-4' },
                                React.createElement(
                                    'h2',
                                    { className: 'h4 mb-3 text-center' },
                                    'Asuntos Tratados'
                                ),
                                formData.asuntosTratados.map((asunto, index) => React.createElement(
                                    'div',
                                    { key: index, className: 'card mb-3' },
                                    React.createElement(
                                        'div',
                                        { className: 'card-body' },
                                        React.createElement(
                                            'div',
                                            { className: 'mb-3' },
                                            React.createElement(
                                                'label',
                                                { className: 'form-label' },
                                                `Asunto ${index + 1}`
                                            ),
                                            React.createElement(
                                                'textarea',
                                                {
                                                    value: asunto,
                                                    onChange: e => handleInputChange(e, index, null, 'asuntosTratados'),
                                                    className: 'form-control',
                                                    placeholder: '',
                                                    rows: 4
                                                }
                                            )
                                        ),
                                        formData.asuntosTratados.length > 1 && React.createElement(
                                            'button',
                                            {
                                                onClick: () => removeAsunto(index),
                                                className: 'btn btn-link text-danger mt-2'
                                            },
                                            'Eliminar Asunto'
                                        )
                                    )
                                )),
                                React.createElement(
                                    'button',
                                    {
                                        onClick: addAsunto,
                                        className: 'btn btn-primary mt-2'
                                    },
                                    'Agregar Asunto'
                                )
                            ),

                            // Sección de Acuerdos
                            React.createElement(
                                'div',
                                { className: 'mb-4' },
                                React.createElement(
                                    'h2',
                                    { className: 'h4 mb-3 text-center' },
                                    'Acuerdos'
                                ),
                                formData.acuerdos.map((acuerdo, index) => React.createElement(
                                    'div',
                                    { key: index, className: 'card mb-3' },
                                    React.createElement(
                                        'div',
                                        { className: 'card-body' },
                                        [
                                            {
                                                label: 'Descripción',
                                                name: 'descripcion',
                                                type: 'textarea',
                                                value: acuerdo.descripcion,
                                                placeholder: '',
                                                rows: 4
                                            },
                                            {
                                                label: 'Responsable',
                                                name: 'responsable',
                                                type: 'input',
                                                value: acuerdo.responsable,
                                                placeholder: 'Seleccione o escriba un nombre...',
                                                datalistId: `responsable-${index}`
                                            },
                                            {
                                                label: 'Fecha Aproximada',
                                                name: 'fechaAprox',
                                                type: 'input',
                                                value: acuerdo.fechaAprox,
                                                placeholder: 'Seleccione fecha y hora...',
                                                id: `fechaAprox-${index}`
                                            }
                                        ].map((field, i) => React.createElement(
                                            'div',
                                            { key: i, className: 'mb-3' },
                                            React.createElement(
                                                'label',
                                                { className: 'form-label' },
                                                field.label
                                            ),
                                            React.createElement(
                                                field.type === 'input' ? 'input' : 'textarea',
                                                {
                                                    name: field.name,
                                                    id: field.id,
                                                    value: field.value,
                                                    onChange: e => handleInputChange(e, index, field.name, 'acuerdos'),
                                                    className: 'form-control',
                                                    placeholder: field.placeholder,
                                                    rows: field.rows,
                                                    list: field.datalistId
                                                }
                                            ),
                                            field.datalistId && React.createElement(
                                                'datalist',
                                                { id: field.datalistId },
                                                predefinedUsers.map((user, j) => React.createElement(
                                                    'option',
                                                    { key: j, value: user }
                                                ))
                                            )
                                        )),
                                        React.createElement(
                                            'button',
                                            {
                                                onClick: () => removeAcuerdo(index),
                                                className: 'btn btn-link text-danger mt-2'
                                            },
                                            'Eliminar Acuerdo'
                                        )
                                    )
                                )),
                                React.createElement(
                                    'button',
                                    {
                                        onClick: addAcuerdo,
                                        className: 'btn btn-primary mt-2'
                                    },
                                    'Agregar Acuerdo'
                                )
                            ),

                            // Advertencia de duplicados
                            duplicateWarning && React.createElement(
                                'div',
                                { className: 'alert alert-warning d-flex align-items-center mb-3' },
                                React.createElement(
                                    'i',
                                    { className: 'fas fa-exclamation-triangle me-2' }
                                ),
                                React.createElement(
                                    'div',
                                    null,
                                    React.createElement(
                                        'strong',
                                        null,
                                        '⚠️ Posible Duplicado Detectado'
                                    ),
                                    React.createElement('br'),
                                    'Ya existe una minuta con la misma fecha, lugar y convocante. Verifique los datos antes de guardar.'
                                )
                            ),

                            // Botones de acción
                            React.createElement(
                                'div',
                                { className: 'd-flex gap-2 flex-wrap' },
                                React.createElement(
                                    'button',
                                    {
                                        onClick: saveMinuta,
                                        className: `btn ${isEditing ? 'btn-warning' : 'btn-success'}`,
                                        disabled: isLoading
                                    },
                                    isEditing ? '✏️ Actualizar Minuta' : '💾 Guardar en Base de Datos'
                                ),
                                isEditing && React.createElement(
                                    'button',
                                    {
                                        onClick: cancelEdit,
                                        className: 'btn btn-secondary',
                                        disabled: isLoading
                                    },
                                    '❌ Cancelar Edición'
                                ),
                                React.createElement(
                                    'button',
                                    {
                                        onClick: () => generatePDF(),
                                        className: 'btn btn-info'
                                    },
                                    'Exportar a PDF'
                                ),
                                React.createElement(
                                    'button',
                                    {
                                        onClick: () => generateWord(),
                                        className: 'btn btn-primary'
                                    },
                                    'Exportar a Word'
                                )
                            )
                        )
                    )
                ),

                // Pestaña Consultar Minutas
                activeTab === 'consultar' && React.createElement(
                    'div',
                    null,
                    React.createElement(SearchSection),
                    viewingMinuta && React.createElement(MinutaViewer, { minuta: viewingMinuta }),
                    !viewingMinuta && React.createElement(MinutasList)
                ),

                // Pestaña Repositorio de Acuerdos
                activeTab === 'acuerdos' && React.createElement(
                    'div',
                    null,
                    React.createElement(AcuerdosRepository)
                )
            );
        };

        const root = ReactDOM.createRoot(document.getElementById('root'));
        root.render(React.createElement(MinutaApp));
    </script>
</body>
</html>