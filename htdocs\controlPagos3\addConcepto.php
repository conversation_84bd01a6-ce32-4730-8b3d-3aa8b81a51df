<?php include_once('php/session.php'); ?>
<!DOCTYPE html>
<html>

<head>
    <meta charset="UTF-8">
    <?php include_once("includes/head.html");
    include_once("includes/headList.html");
    ?>

    <script src="js/addConcepto.js"></script>

</head>

<body class="bg-light">
    <div class="container">
        <br>
        <div id="header"></div>
        <?php include_once("includes/menu.html"); ?>

        <div class="container">
            <br>
            <div id="header"></div>
            <?php include_once("includes/menu.html"); ?>
            <div class="mt-4 row justify-content-center">
                <div class="col-lg-10">
                    <div class="card border-success mb-3" style="max-width: 100%;">
                        <div class="card-header bg-info p-2 text-dark bg-opacity-10">
                            <h3>Conceptos</h3>
                        </div>
                        <div class="card-body">
                            <input type="hidden" name="admin" id="admin" value="8709011993">
                            <table id="listaConcepto" class="mt-4 table table-striped" style="width: 100%;">
                                <thead>
                                    <tr class="table-dark">
                                        <th>Concepto</th>
                                        <th>Monto(s) actual(es)</th>
                                    </tr>
                                </thead>
                                <tbody>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <div class="mt-4 row justify-content-center">
                <div class="col-lg-10">
                    <div class="card border-primary mb-3" style="max-width: 100%;">
                        <div class="card-header bg-success text-dark bg-opacity-50">
                            <h3>Nuevo Concepto</h3>
                        </div>
                        <div class="card-body">
                            <form method="POST" action="php/saveConcepto.php" autocomplete="off" id="operadorForm">
                                <div class="row justify-content-center">
                                    <div class="col-lg-6">
                                        <label for="nombre">
                                            <h5>Nombre del concepto:</h5>
                                        </label>
                                        <input name="concepto" id="concepto" maxlength="100" class="form-control" required>
                                    </div>
                                </div>
                                <br>
                                <div id="renglones">
                                    <div class="row justify-content-center">
                                        <div class="col-lg-4">
                                            <label for="importe1">
                                                <h5>Importe:</h5>
                                            </label>
                                            <div class="input-group mb-3">
                                                <span class="input-group-text">$</span>
                                                <input type="number" name="importe[]" id="importe1" class="form-control" aria-label="Amount (to the nearest dollar)">
                                                <span class="input-group-text">.00</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <br>
                                <div class="row justify-content-center">
                                    <div class="col-lg-3">
                                        <div class="d-grid gap-2">
                                            <button type="submit" class="btn btn-info"><span class="ui-icon ui-icon-disk"></span>Guardar</button>
                                        </div>
                                    </div>
                                </div>
                            </form>
                            <br>
                            <div class="row justify-content-center">
                                <div class="col-md-2">
                                    <div class="d-grid gap-2">
                                        <button name="addIporte" id="addIporte" class="btn btn-sm btn-outline-success">Agregar<br><span class="ui-icon ui-icon-plusthick"></span></button>
                                    </div>
                                </div>
                                <div class="col-md-2 offset-md-2">
                                    <div class="d-grid gap-2">
                                        <button name="removeImporte" id="removeImporte" class="btn btn-sm btn-outline-danger">Quitar<br><span class="ui-icon ui-icon-minusthick"></span></button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div id="footer"></div>
        </div>
</body>

</html>