<?php
include_once("conexion.php");
include_once("configDB.php");
include_once("classProcesaVariable.php");
include_once("classEconomico.php");

if (isset($_POST['idEconomico'])) {

    $idEconomico = new procesaVariable($_POST['idEconomico']);
    $marca = new procesaVariable($_POST['marca']);
    $modelo = new procesaVariable($_POST['modelo']);
    $placa = new procesaVariable($_POST['placa']);
    $anio = new procesaVariable($_POST['anio']);
    $propietarioAuto = new procesaVariable($_POST['propietarioAuto']);


    $taxiConect = new foo_mysqli("localhost", USER, PASS, DBNAME);
    $taxiConect->query("SET NAMES utf8");

    $auto = new economico(
        "",
        $marca->valor,
        $modelo->valor,
        $placa->valor,
        $anio->valor,
        $idEconomico->valor,
        "",
        'A',
        $propietarioAuto->valor,
        ""
    );

    if ($auto->updateEconomico($taxiConect)) {
        $var = 1;
    } else {
        $var = '0';
    }

    $taxiConect->close();
} else {
    $var = '0';
}

header("Location: ../editEconomico.php?res=$var");
