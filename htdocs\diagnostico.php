<?php
/**
 * Archivo de diagnóstico para verificar la configuración del sistema
 */

echo "<h1>Diagnóstico del Sistema de Minutas</h1>";

// Verificar PHP
echo "<h2>1. Información de PHP</h2>";
echo "Versión de PHP: " . phpversion() . "<br>";
echo "Extensiones cargadas: " . implode(", ", get_loaded_extensions()) . "<br><br>";

// Verificar PDO
echo "<h2>2. Verificación de PDO</h2>";
if (extension_loaded('pdo')) {
    echo "✅ PDO está disponible<br>";
    if (extension_loaded('pdo_mysql')) {
        echo "✅ PDO MySQL está disponible<br>";
    } else {
        echo "❌ PDO MySQL NO está disponible<br>";
    }
} else {
    echo "❌ PDO NO está disponible<br>";
}

// Verificar archivos del sistema
echo "<h2>3. Verificación de Archivos</h2>";
$files = [
    'config/database.php',
    'models/Minuta.php',
    'api/minutas.php',
    'database/create_tables.sql'
];

foreach ($files as $file) {
    if (file_exists($file)) {
        echo "✅ $file existe<br>";
    } else {
        echo "❌ $file NO existe<br>";
    }
}

// Verificar conexión a base de datos
echo "<h2>4. Verificación de Base de Datos</h2>";
try {
    require_once 'config/database.php';
    $database = new Database();
    $db = $database->getConnection();
    
    if ($db) {
        echo "✅ Conexión a base de datos exitosa<br>";
        
        // Verificar tablas
        $tables = ['minutas', 'minuta_asistentes', 'minuta_asuntos', 'minuta_acuerdos'];
        foreach ($tables as $table) {
            $stmt = $db->prepare("SHOW TABLES LIKE ?");
            $stmt->execute([$table]);
            if ($stmt->rowCount() > 0) {
                echo "✅ Tabla '$table' existe<br>";
            } else {
                echo "❌ Tabla '$table' NO existe<br>";
            }
        }
        
        // Probar generación de ID
        echo "<br><strong>Prueba de generación de ID:</strong><br>";
        $newId = Database::generateMinutaId();
        echo "Nuevo ID generado: $newId<br>";
        
    } else {
        echo "❌ No se pudo conectar a la base de datos<br>";
    }
} catch (Exception $e) {
    echo "❌ Error de base de datos: " . $e->getMessage() . "<br>";
}

// Verificar permisos de escritura
echo "<h2>5. Verificación de Permisos</h2>";
$dirs = ['.', 'config', 'models', 'api'];
foreach ($dirs as $dir) {
    if (is_writable($dir)) {
        echo "✅ Directorio '$dir' es escribible<br>";
    } else {
        echo "⚠️ Directorio '$dir' NO es escribible<br>";
    }
}

// Probar API
echo "<h2>6. Prueba de API</h2>";
echo "<button onclick='testAPI()'>Probar API</button><br>";
echo "<div id='apiResult'></div>";

?>

<script>
async function testAPI() {
    const resultDiv = document.getElementById('apiResult');
    resultDiv.innerHTML = 'Probando API...';
    
    try {
        // Probar generación de ID
        const response = await fetch('api/minutas.php?action=generate_id');
        const data = await response.json();
        
        if (data.success) {
            resultDiv.innerHTML = `✅ API funciona correctamente. ID generado: ${data.minuta_id}`;
        } else {
            resultDiv.innerHTML = `❌ Error en API: ${data.error || 'Error desconocido'}`;
        }
    } catch (error) {
        resultDiv.innerHTML = `❌ Error de conexión: ${error.message}`;
    }
}
</script>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h1 { color: #333; }
h2 { color: #666; border-bottom: 1px solid #ccc; }
button { padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
button:hover { background: #0056b3; }
</style>
