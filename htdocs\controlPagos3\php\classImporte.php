<?php
class importe
{

    public $monto;
    public $concepto;
    public $estado;
    public $identificador;

    function __construct($con, $std = null, $mnt = null)
    {
        $this->concepto = $con;
        if (isset($std)) {
            $this->estado = $std;
        }
        if (isset($mnt)) {
            $this->monto = $mnt;
        }
    }

    public function buscaImporte($conexion, $aux)
    {
        $qry = "SELECT idImporte, monto FROM importe 
                WHERE idConcepto = '$this->concepto' 
                AND estatus = '$this->estado';";
        $rqry = $conexion->query($qry);

        $i = 0;

        if ($rqry->num_rows != 0) {
            while ($row = $rqry->fetch_array(MYSQLI_ASSOC)) {
                array_push($aux, $row);
                $i++;
            }
            $aux[0] = $i;
        } else {
            $aux = array(0);
        }

        return $aux;
    }

    public function ultimoImporteDesactivado($conexion, $aux)
    {
        $q = "SELECT i.idImporte, i.monto, f.fechaDesactivado
        FROM importe i
        INNER JOIN fechaultimadesactivacion f ON i.idConcepto = f.idConcepto
        WHERE i.idConcepto = '$this->concepto'
        AND i.estatus = '$this->estado';";
        $rqry = $conexion->query($q);

        $importeViejo = array();
        if ($rqry->num_rows != 0) {
            while ($row = $rqry->fetch_array(MYSQLI_ASSOC)) {
                array_push($importeViejo, $row);
            }
            $aux['antiguo'] = $importeViejo;
        }

        return $aux;
    }

    public function nuevoImporte($conexion, $transac)
    {
        $insertImp = "INSERT INTO importe(idConcepto, monto, estatus) 
            VALUES ('$this->concepto',
            '$this->monto','$this->estado')";
        $exeInsertImp = $conexion->query($insertImp);

        if (!$exeInsertImp) {
            $transac = 0;
        }
        return $transac;
    }

    public function bajaImporte($conexion, $t)
    {
        $q = "UPDATE importe SET estatus='I' WHERE idConcepto='$this->concepto'";
        $eq = $conexion->query($q);

        if (!$eq) {
            $t = 0;
        }
        return $t;
    }

    public function importeConvenio($conexion)
    {
        // $q = "SELECT MAX(idImporteConvenio) AS idImporte, monto
        //           FROM importeconvenio GROUP BY idImporteConvenio";
        $q = "SELECT idImporte, monto
            FROM importe
            WHERE idImporte = '$this->identificador'";
        $eq = $conexion->query($q);
        $row = $eq->fetch_array(MYSQLI_ASSOC);

        return $row;
    }

    public function importeXConcepto($conexion)
    {
        $q = "SELECT GROUP_CONCAT(idImporte) AS importes
        FROM importe i 
        WHERE i.idConcepto = '$this->concepto'";
        $rqry = $conexion->query($q);
        if ($rqry->num_rows != 0) {
            $row = $rqry->fetch_array(MYSQLI_ASSOC);
            $data = $row['importes'];
        } else {
            $data = false;
        }
        return $data;
    }

    public function nombreConcepto($conexion)
    {
        $q = "SELECT c.descripcion 
        FROM concepto c
        WHERE c.idConcepto = '$this->concepto'";
        $rqry = $conexion->query($q);
        if ($rqry->num_rows != 0) {
            $row = $rqry->fetch_array(MYSQLI_ASSOC);
            $data = $row['descripcion'];
        } else {
            $data = false;
        }
        return $data;
    }

    public function conceptos($conexion)
    {
        $aux = array();
        $q = "SELECT idConcepto, descripcion
        FROM concepto";
        $rqry = $conexion->query($q);
        if ($rqry->num_rows != 0) {
            while ($row = $rqry->fetch_array(MYSQLI_ASSOC)) {
                array_push($aux, $row);
            }
        } else {
            $aux = false;
        }
        return $aux;
    }
}