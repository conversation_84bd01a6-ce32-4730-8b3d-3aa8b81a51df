<?php
include_once("../conexion.php");
include_once("../configDB.php");

$taxiConect = new foo_mysqli("localhost", USER, PASS, DBNAME);
$taxiConect->query("SET NAMES utf8");

foreach ($_REQUEST as $campo => $valor) {
    $variable = $campo;
}

$dato = $_REQUEST[$variable];
switch ($variable) {
    case 'contable':
        $qry = "SELECT idOperador FROM operador WHERE contable = '$dato' AND estado = 'A'";
        break;
    case 'referenciado':
        $qry = "SELECT idOperador FROM operador WHERE referenciado = '$dato' AND estado = 'A'";
        break;
    case 'folio':
        $qry = "SELECT idOperador FROM licencia WHERE idLicencia = '$dato'";
        break;
    case 'correo':
        $dato = strtolower($dato);
        $qry = "SELECT idOperador FROM operador WHERE correo = '$dato' AND estado = 'A'";
        break;
    case 'correoUser':
        $dato = strtolower($dato);
        $qry = "SELECT u.idUsuario FROM usuario u WHERE u.correo = '$dato'";
        break;
    case 'userName':
        $qry = "SELECT idUsuario FROM `login` WHERE userName = '$dato'";
        break;
    case 'correoOwner':
        $qry = "SELECT idOwner FROM `owner` WHERE correo = '$dato'";
        break;
    case 'placa':
        $qry = "SELECT idEconomico FROM economico WHERE placa = '$dato' AND estado = 'A'";
        break;
    case 'economico':
        $qry = "SELECT idEconomico FROM economico WHERE economico = '$dato' AND estado = 'A'";
        break;
    case 'contable0':
        // $qry = "SELECT oe.idEconomico
        // FROM operador o 
        // INNER JOIN operadoreconomico oe ON o.idOperador = oe.idOperador
        // WHERE o.contable = '$dato'
        // AND oe.estado = 'A'";
        $qry = "SELECT oe.idEconomico
        FROM operadoreconomico oe
        WHERE oe.idOperador = '$dato'
        AND oe.estado = 'A'";
        break;
    case 'economico0':
        // $qry = "SELECT oe.idOperador
        // FROM economico e
        // INNER JOIN operadoreconomico oe ON e.idEconomico = oe.idEconomico
        // WHERE e.economico = '$dato'
        // AND oe.estado = 'A'";
        $qry = "SELECT oe.idOperador
        FROM operadoreconomico oe
        WHERE oe.idEconomico = '$dato'
        AND oe.estado = 'A'";
        break;
    default:
        $qry = "SELECT idOperador FROM operador WHERE idOperador = 'x'";
}

$rqry = $taxiConect->query($qry);

if ($rqry->num_rows != 0) {
    echo 'false';
} else {
    echo 'true';
}

$taxiConect->close();
