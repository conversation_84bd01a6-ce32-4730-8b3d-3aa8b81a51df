<?php
include_once("conexion.php");
include_once("configDB.php");
include_once("classProcesaVariable.php");
include_once("classAbono.php");
include_once("classImporte.php");
include_once("classReporte.php");
include_once("classOperador.php");
include_once("session.php");

if (isset($_POST['beginDate']) and isset($_POST['endDate'])) {

    $beginDate = new procesaVariable($_POST['beginDate']);
    $endDate = new procesaVariable($_POST['endDate']);
    // $soloAbonos = new procesaVariable($_POST['soloAbonos']);

    $taxiConect = new foo_mysqli("localhost", USER, PASS, DBNAME);
    $taxiConect->query("SET NAMES utf8");

    $conceptos = new importe('');
    $allConcept = $conceptos->conceptos($taxiConect);

    // $q = "select	group_concat(distinct a.idRecibo SEPARATOR ' | ') as 'Recibo(s)',
    $q = "select	a.idRecibo as 'Recibo(s)',
    o.contable, o.idOperador, o.nombreCompleto as Operador, r.idcuentaBancaria as CuentaBancaria,
    e.economico, a.semana, a.anio, 
    group_concat(distinct r.fechaPago SEPARATOR ' | ') AS 'fechas Pago recibos',
    r.movimiento as 'Movimiento bancario'";
    foreach ($allConcept as $cArray) {
        $idConcepto = $cArray['idConcepto'];
        $coneptoNombre = $cArray['descripcion'];
        $importeAux = new importe($idConcepto);
        $importes = $importeAux->importeXConcepto($taxiConect);
        // $coneptoNombre = $importeAux->nombreConcepto($taxiConect);
        $q .= ", CONCAT(SUM(IF(a.idImporte IN($importes), i.monto, 0)),
            CASE TRIM(GROUP_CONCAT(IF(a.idImporte IN($importes), a.tipoAbono, '') SEPARATOR ' '))
                WHEN 'A' THEN ''
                WHEN 'C' THEN '*'
                WHEN 'V' THEN '**'
                WHEN '' THEN '0'                    
                ELSE '***'
            END
            ) AS '$coneptoNombre', 
            GROUP_CONCAT(IF(a.idImporte IN($importes), DATE_FORMAT(r.fechaPago, '%d/%m/%Y %H:%i'), '') SEPARATOR ',') AS 'fechaPago-$coneptoNombre' ";
    }
    $q .= ", group_concat(distinct r.observaciones SEPARATOR ' | ') AS Observaciones
    from abono a
    inner join operadorview o on a.idOperador = o.idOperador 
    inner join recibo r on a.idRecibo = r.idRecibo
    inner join operadoreconomico oe on o.idOperador = oe.idOperador and oe.fechaRegsitro = (
    select max(fechaRegsitro) from control.operadoreconomico oe2 where oe2.idOperador = oe.idOperador)
    inner join economico e on oe.idEconomico = e.idEconomico 
    inner join  importe i on a.idImporte = i.idImporte 
    ";

    if (isset($_POST['soloAbonos']) and $_POST['soloAbonos'] == 'S') {
        $q .= "WHERE DATE_FORMAT(r.fechaPago, '%Y-%m-%d') BETWEEN '$beginDate->valor' AND '$endDate->valor'
            AND a.tipoAbono = 'A'";
    } else {
        $q .= "WHERE DATE_FORMAT(r.fechaRegistro, '%Y-%m-%d') BETWEEN '$beginDate->valor' AND '$endDate->valor'";
    }

    $q .= " group by a.idRecibo, o.idOperador, semana, anio
    ORDER BY a.idRecibo, a.anio, a.semana";

    // echo "<pre>$q</pre>";
    $rq = $taxiConect->query($q) or die($taxiConect->error . "<br><pre>$q");

    $myCsv = new Reporte("reporteGral", $rq);
    $myCsv->putHeaders(false);
    $myCsv->putData(false);

    $rq->free();

    $taxiConect->close();
} else {
    $link = '0';
}