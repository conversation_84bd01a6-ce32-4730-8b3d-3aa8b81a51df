(function ($) {
  $.fn.extend({
    miCheckBox: function (archivo, errorLocaliza, tabla, DB) {
      return this.each(function () {
        var aux = this;

        $.ajax({
          type: "POST",
          url: archivo,
          data: { tabla: tabla, dbName: DB },
        })
          .done(function (r) {
            resp = $.parseJSON(r);
            for (var x in resp) {
              $(aux).append(
                '<label for="' + tabla + x + '">' + resp[x] + "</label>"
              );
              $(aux).append(
                '<input type="checkbox" name="' +
                  tabla +
                  '" id="' +
                  tabla +
                  x +
                  '" value="' +
                  x +
                  '"><br>'
              );
            }
            $("input[type=checkbox]").checkboxradio();
            $(aux).append('<label for="' + tabla + '" class="error"></label>');
          })
          .fail(function () {
            alertify.alert(
              "Warning!",
              "Error al cargar el formulario (" +
                errorLocaliza +
                "), se recomienda cargar nuevamente la página"
            );
          });
      });
    },
  });

  ////////////////////////////////////////////////////////////////////////////////////////////////////////////

  $.fn.extend({
    enumera: function () {
      return this.each(function () {
        list = $(this).children("tbody").children("tr");
        ctr = 1;
        for (var x in list) {
          $("#" + list[x].id)
            .children("th")
            .empty()
            .html(ctr);
          ctr++;
        }
        return 0;
      });
    },
  });

  ////////////////////////////////////////////////////////////////////////////////////////////////////////////

  $.fn.extend({
    cambiaMayus: function () {
      return this.each(function () {
        $(this).change(function () {
          aux = $(this).val().toUpperCase();
          $(this).val(aux);
        });
      });
    },
  });

  ///////////////////////////////////////////////////////////////////////////////////////////////////////////

  $.fn.extend({
    miCatalogo: function (tabla, errorLocaliza) {
      return this.each(function () {
        var aux = this;

        catArrar = { tabla: tabla };

        $.ajax({
          type: "POST",
          url: "php/ajax/catalogos.php",
          data: catArrar,
        })
          .done(function (r) {
            resp = $.parseJSON(r);

            if (resp[0] != "error") {
              for (var x in resp) {
                $(aux).append(
                  "<option value=" + resp[x][0] + ">" + resp[x][1] + "</option>"
                );
              }
            }
          })
          .fail(function () {
            alertify.alert(
              "Warning!",
              "Error al cargar el formulario (" +
                errorLocaliza +
                "), se recomienda cargar nuevamente la página"
            );
          });
      });
    },
  });
})(jQuery);
