<style type="text/css">
    .headFoot {
        width: 100%;
        margin: 5px auto;
    }

    .headFoot h6 {
        font-size: 10px;
    }

    .bDashRight {
        border-right: 2px dashed black;
    }

    .bDashTop {
        border-top: 2px dashed black;
    }

    .bDashLeft {
        border-left: 2px dashed black;
    }

    td.col1 {
        text-align: right;
    }

    td.centrar {
        text-align: center;
    }

    .centrado {
        text-align: center;
    }

    .firma {
        font-size: 16px;
    }

    h1 {
        font-size: 50px;
    }

    .cuerpo {
        width: 100%;
        height: 100%;
    }

    .cuerpo td {
        font-size: 18px;
    }

    .convenio {
        width: 100%;
    }

    .convenio th {
        font-size: 18px;
        text-align: center;
    }

    .convenio td {
        font-size: 16px;
        padding-top: 10px;
    }

    .derecha {
        text-align: right;
    }

    p {
        text-align: justify;
    }

    .parrafo {
        font-size: 18px;
    }

    @supports(object-fit: cover) {
        .box img {
            height: 100%;
            object-fit: cover;
            object-position: center center;
        }
    }
</style>
<?php
include_once("conexion.php");
include_once("configDB.php");
include_once("funciones.php");
include_once("classCargo.php");
include_once("classAbono.php");

$taxiConect = new foo_mysqli("localhost", USER, PASS, DBNAME);
$taxiConect->query("SET NAMES utf8");

$myConvenio = new cargo($tipoCargo, 0, 3, $convenio, 0, 0);
$datos = $myConvenio->dataConvenio($taxiConect);
if ($datos) {
    $montoNum = $datos['monto'];
    $formatoES = new NumberFormatter("es", NumberFormatter::SPELLOUT);
    $montoLetra = $formatoES->format($montoNum);
    if (!is_null($datos['importe'])) {
        $montoSemanal = $datos['importe'];
        $semanas = ceil($montoNum / $montoSemanal);
        $nombre = $datos['nombreCompleto'];
        $folio = $datos['Folio'];

        $abonos = new abono();
        $abonos->recibo = $convenio;
        $convenioData = $abonos->abonoConvenio($taxiConect);
    } else {
        $montoNum = "________";
        $montoSemanal = "________";
        $semanas = "_________";
    }
} else {
    $datos = $myConvenio->dataPrestamo($taxiConect);
    if ($datos) {
        $montoNum = $datos['monto'];
        $formatoES = new NumberFormatter("es", NumberFormatter::SPELLOUT);
        $montoLetra = $formatoES->format($montoNum);
        $nombre = $datos['nombreCompleto'];
        $folio = $datos['Folio'];
        $montoSemanal = "________";
        $semanas = "_________";
        @$convenioData->num_rows = 0;
    } else {
        $montoNum = "________";
        $montoSemanal = "________";
        $semanas = "_________";
    }
}


$hoy = "CDMX, " . date('d') . " de " . mes(date('n')) . " de " . date('Y');
?>
<page format="LETTER" backtop="30mm" backbottom="20mm" backleft="5mm" backright="5mm">
    <page_header>
        <table class="headFoot">
            <col style="width: 50%;">
            <col style="width: 50%;">
            <tr>
                <td class="centrar"><img src="../images/sitio 152.png" height="90" width="90"></td>
                <td>
                    <h1>Sitio 152</h1>
                </td>
            </tr>
        </table>
    </page_header>
    <page_footer>
        <table class="headFoot">
            <col style="width: 50%;">
            <col style="width: 50%;">
            <tr>
                <td>&nbsp;</td>
                <td>pagina [[page_cu]]/[[page_nb]]</td>
            </tr>
        </table>
    </page_footer>

    <table class="cuerpo">
        <col style="width: 50%;">
        <col style="width: 50%;">
        <tr>
            <td>&nbsp;</td>
            <td class="col1"><strong>Folio: </strong><? echo $folio ?></td>
        </tr>
    </table>
    <p>
    <h2 class="centrado">PAGARÉ</h2>
    </p>
    <p class="parrafo">Por este pagaré me obligo incondicionalmente a pagar a la orden de la <strong>"Unión de Propietarios y Operadores de Servitaxis Similares y Conexos del D.F. A.C."</strong>,
        en el domicilio que se me requiera, la cantidad de <strong>$ <? echo $montoNum ?> (<? echo $montoLetra ?> Pesos 00/100)</strong> que me obligo a cubrir en <? echo $semanas ?>
        pagos semanales, vencidos y sucesivos, cada uno por la cantidad de <strong>$ <? echo $montoSemanal ?></strong> en moneda nacional</p>
    <p class="parrafo">
        Este pagaré sólo perderá su ejecutividad cuando el deudor cumpla con todas y cada una de sus obligaciones contraídas.
    </p>
    <br><br><br><br>
    <p>
    <h6 class="centrado firma"><?php echo $hoy ?></h6>
    </p>
    <br><br><br>
    <p>
    <h6 class="centrado firma" style="text-decoration: overline;"><?php echo "&nbsp;&nbsp;&nbsp;&nbsp;$nombre&nbsp;&nbsp;&nbsp;&nbsp;" ?></h6>
    </p>
</page>

<?php
if ($convenioData->num_rows != 0) {
?>
    <page pageset="old">
        <br>
        <table class="convenio">
            <col style="width: 4%;">
            <col style="width: 20%;">
            <col style="width: 15%;">
            <col style="width: 25%;">
            <col style="width: 10%;">
            <col style="width: 13%;">
            <col style="width: 12%;">
            <thead>
                <tr>
                    <th>#</th>
                    <th>Concepto</th>
                    <th>Importe Unitario</th>
                    <th>Semanas</th>
                    <th>Año</th>
                    <th>Adeudo</th>
                    <th>Semanas a pagar</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td colspan="7">&nbsp;</td>
                </tr>
                <?php
                $i = 0;
                $suma = 0;
                $semanasSum = 0;
                while ($row = $convenioData->fetch_array(MYSQLI_ASSOC)) {
                    $i++;
                    $suma = $suma + $row['Adeudo'];
                    $semPag = ceil($row['Adeudo'] / $montoSemanal);
                    $semanasSum = $semanasSum + $semPag;
                    $varPrint = "<tr>
            <th>$i</th>
            <td>" . $row['Concepto'] . "</td>
            <td class='centrado'>$ " . $row['importeUnitario'] . "</td>
            <td>" . $row['Semanas'] . "</td>
            <td class='centrado'>" . $row['anio'] . "</td>
            <td class='derecha'>$ " . $row['Adeudo'] . "</td>
            <td class='centrado'>$semPag</td>
        </tr>";
                    echo $varPrint;
                }
                ?>
                <tr>
                    <td colspan="7">&nbsp;</td>
                </tr>
                <tr>
                    <td colspan="5" class="derecha">TOTAL:</td>
                    <td class="derecha">$ <? echo number_format($suma, 2); ?></td>
                    <td class="centrado"><? echo $semanasSum; ?></td>
                </tr>
            </tbody>
        </table>
    </page>
<?php
}
$taxiConect->close();
?>