<?php
class cargo
{

    public $operador;
    public $usuario;
    public $importe;
    public $importePagar;
    public $tipoCargo;
    public $chequeFolio;
    public $tipoUser;
    public $fechaCheque;

    function __construct($tipo, $monto, $id, $chequeFolio, $user, $typeUser)
    {
        $this->tipoCargo = $tipo;
        $this->importe = $monto;
        $this->operador = $id;
        $this->chequeFolio = $chequeFolio;
        $this->usuario = $user;
        $this->tipoUser = $typeUser;
    }

    public function saveCargo($conexion)
    {
        $q = "INSERT INTO cargo(tipoCargo, folio, idOperador, idUsuario, tipoUsuario, monto) 
            VALUES ('$this->tipoCargo',
            '$this->chequeFolio',
            '$this->operador',
            '$this->usuario',
            '$this->tipoUser',
            '$this->importe')";
        $insert = $conexion->query($q);

        return $insert;
    }

    public function updatePresatamo($conexion)
    {
        $q = "UPDATE cargo 
        SET fechaCheque='$this->fechaCheque'
        WHERE tipoCargo='$this->tipoCargo'
        AND folio='$this->chequeFolio'";
        $insert = $conexion->query($q);

        return $insert;
    }

    public function saveComConvenio($conexion)
    {
        $update = "UPDATE cargo SET idImporte='$this->importePagar'
        WHERE tipoCargo='$this->tipoCargo' AND folio='$this->chequeFolio'";
        $execUdp = $conexion->query($update);

        return $execUdp;
    }

    public function dataConvenio($conexion)
    {
        $q = "SELECT CONCAT(c.tipoCargo,'-',c.folio) AS Folio, o.nombreCompleto, c.monto, i.monto AS importe
        FROM cargo c
        INNER JOIN operadorview o ON c.idOperador = o.idOperador
        INNER JOIN importe i ON c.idImporte = i.idImporte
        WHERE c.tipoCargo = '$this->tipoCargo'
        AND c.folio = '$this->chequeFolio'";
        $eq = $conexion->query($q);

        if ($eq->num_rows != 0) {
            $row = $eq->fetch_array(MYSQLI_ASSOC);
        } else {
            $row = false;
        }

        return $row;
    }

    public function dataPrestamo($conexion)
    {
        $q = "SELECT CONCAT(c.tipoCargo,'-',c.folio) AS Folio, o.nombreCompleto, c.monto
        FROM cargo c
        INNER JOIN operadorview o ON c.idOperador = o.idOperador
        WHERE c.tipoCargo = '$this->tipoCargo'
        AND c.folio = '$this->chequeFolio'";
        $eq = $conexion->query($q);

        if ($eq->num_rows != 0) {
            $row = $eq->fetch_array(MYSQLI_ASSOC);
        } else {
            $row = false;
        }

        return $row;
    }

    public function suma($conexion, $fecha)
    {
        $q = "SELECT SUM(c.monto) AS monto
            FROM cargo c 
            WHERE c.fechaRegistro < '$fecha'
            AND c.tipoCargo = '$this->tipoCargo'
            AND c.idOperador = '$this->operador';";
        $eq = $conexion->query($q);
        if ($eq->num_rows != 0) {
            $row = $eq->fetch_array(MYSQLI_ASSOC);
            $monto = $row['monto'];
        } else {
            $monto = null;
        }

        if (is_null($monto)) {
            $monto = 0;
        }

        return $monto;
    }
}
