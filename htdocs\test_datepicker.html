<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Date Picker</title>
    <link href="https://cdn.jsdelivr.net/npm/flatpickr@4.6.13/dist/flatpickr.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/flatpickr@4.6.13/dist/flatpickr.min.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container my-4">
        <h2>Test Date Picker</h2>
        
        <div class="mb-3">
            <label for="testDate" class="form-label">Fecha y Ho<PERSON> de Prueba</label>
            <input type="text" id="testDate" class="form-control" placeholder="Seleccione fecha y hora...">
        </div>
        
        <div class="mb-3">
            <label for="testDate2" class="form-label">Segunda Fecha</label>
            <input type="text" id="testDate2" class="form-control" placeholder="Seleccione fecha y hora...">
        </div>
        
        <button onclick="showValues()" class="btn btn-primary">Mostrar Valores</button>
        
        <div id="output" class="mt-3"></div>
    </div>

    <script>
        // Inicializar date pickers
        const picker1 = flatpickr('#testDate', {
            enableTime: true,
            dateFormat: 'd/m/Y H:i',
            onChange: function(selectedDates, dateStr, instance) {
                console.log('Fecha 1 cambiada:', dateStr);
                document.getElementById('output').innerHTML = 'Fecha 1: ' + dateStr;
            }
        });

        const picker2 = flatpickr('#testDate2', {
            enableTime: true,
            dateFormat: 'd/m/Y H:i',
            onChange: function(selectedDates, dateStr, instance) {
                console.log('Fecha 2 cambiada:', dateStr);
            }
        });

        function showValues() {
            const date1 = document.getElementById('testDate').value;
            const date2 = document.getElementById('testDate2').value;
            
            document.getElementById('output').innerHTML = `
                <div class="alert alert-info">
                    <strong>Valores:</strong><br>
                    Fecha 1: ${date1}<br>
                    Fecha 2: ${date2}
                </div>
            `;
        }
    </script>
</body>
</html>
