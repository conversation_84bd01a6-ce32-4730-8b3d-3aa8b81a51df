$(document).ready(function () {
  if ($.get("res")) {
    Rsp = $.get("res");
    switch (Rsp) {
      case "0":
        rmsg = "Error en el proceso";
        break;
      case "1":
        rmsg = "Acción realizada correctamente";
        break;
      case "2":
        rmsg =
          "No se pudo activar, modifique el contable en la parte de edición.";
        break;
      default:
        rmsg = "Error al guardar en la base de datos.";
    }
    alertify
      .alert()
      .setting({
        title: "Servitaxis Sitio 152",
        closable: false,
        message: rmsg,
        onok: function () {
          let myUrl = window.location.origin;
          myUrl = `${myUrl}/selectConvenio.php`;
          $(location).attr("href", myUrl);
        },
      })
      .show();
  }

  $("#cuotaConvenio").miCatalogo("importeconvenio", "Catalogo importe");
  $("#formCuota").validate();
});
