$(document).ready(function () {
  $("#buscadorOp").focus();
  if ($.get("resp")) {
    Rsp = $.get("resp");
    switch (Rsp) {
      case "0":
        rmsg = "Error al guardar los datos";
        break;
      case "1":
        rmsg = "Datos guardados correctamente";
        break;
    }
    alertify.alert("Servitaxis Sitio 152", rmsg);
  }

  $("#fechaDeposito").datepicker({
    dateFormat: "yy-mm-dd",
    maxDate: "0",
    showWeek: true,
    beforeShowDay: function (date) {
      return [!date.getDay() == 0];
    },
  });
  $("#divFechaDeposito").hide();
  if ($.get("deposito") && $.get("deposito") == 1) {
    $("#divFechaDeposito").show();
    $("#depositoFlag").val(1);
  }

  $("#concepto").miCatalogo("concepto", "Catalogo concepto");
  $("#cuenta").miCatalogo("vistaCuenta", "Catalogo Cuentas Bancarias");
  $("#buscadorOp").cambiaMayus();
  $(
    "#divAgregaConcepto, #semanaDiv, #anioDiv, #agregaDiv, #importeDiv, #reciboConceptos"
  ).hide();

  $("#concepto").change(function (e) {
    e.preventDefault();
    idconcepto = $(this).val();
    operador = $("#idOperador").val();
    $.ajax({
      type: "post",
      url: "php/ajax/buscaImporte.php",
      data: { concepto: idconcepto, idOperado: operador },
    })
      .done(function (r) {
        R = $.parseJSON(r);
        $("#importeDiv").show();
        if (R[0] != 0) {
          semana = R.semana;
          anio = R.anio;
          semanaSig = R.semanaSig;
          anioSemana = R.anioSemana;
          delete R.semana;
          delete R.anio;
          delete R.semanaSig;
          delete R.anioSemana;
          delete R[0];

          mySelect = '<option value="">Elegir importe ...</option>';

          for (var key in R) {
            mySelect =
              mySelect +
              '<option value="' +
              R[key].idImporte +
              '">' +
              R[key].monto +
              "</option>";
          }
          $("#importe").empty().append(mySelect);

          if (semana == null) {
            msgUltima = "No hay datos para este concepto";
          } else {
            msgUltima = "Ultima semana pagada: " + semana + " del año: " + anio;
          }
          $("#ultimaSemana").empty().html(msgUltima);

          $("#semanaDiv,#anioDiv,#agregaDiv").show();
          $("#semana").val(semanaSig);
          $("#anio").val(anioSemana);
        } else {
          alertify.alert(
            "Servitaxis Sitio 152",
            "El concepto no tiene importes activos agregue uno antes de continuar"
          );
        }
      })
      .fail(function () {
        alertify.alert("Warning!", "Error al cargar el formulario.");
      });
  });

  $("#reciboConceptos").validate();

  total = 0;
  $("#conceptoAgrega").validate({
    submitHandler: function (f) {
      semana = $("#semana").val();
      concepto = $("#concepto").val();
      importe = $("#importe").val();
      semana = $("#semana").val();
      anio = $("#anio").val();
      operador = $("#idOperador").val();
      conceptoText = $("#concepto option:selected").text();
      importeText = $("#importe option:selected").text();

      if ($("#flexCondonacion").prop("checked")) {
        condona = "C";
        condonaImage = "images/checked.png";
      } else {
        condona = "A";
        condonaImage = "images/unchecked.png";
        total = total + parseFloat(importeText);
      }
      $("#operadorID").val(operador);

      numId = $("#listaConceptos").children("tbody").children("tr").length;

      $("#reciboConceptos").show();
      $("#listaConceptos").append(
        '<tr id="fila' +
          numId +
          concepto +
          '">' +
          "<th></th>" +
          '<td><input type="hidden" name="conceptos[]" value="' +
          concepto +
          '">' +
          conceptoText +
          "</td>" +
          '<td><input type="hidden" name="importes[]" value="' +
          importe +
          '">$ ' +
          importeText +
          "</td>" +
          '<td><input type="hidden" name="semanas[]" value="' +
          semana +
          '">' +
          semana +
          "</td>" +
          '<td><input type="hidden" name="anios[]" value="' +
          anio +
          '">' +
          anio +
          "</td>" +
          '<td><input type="hidden" name="condona[]" id="condona' +
          numId +
          concepto +
          semana +
          '" value="' +
          condona +
          '"><img class="elimina" src="' +
          condonaImage +
          '" width="25" height="25">' +
          "</td>" +
          '<td><a href="#"><img class="elimina" src="images/delete.png" width="25" height="25"></a></td>' +
          "</tr>"
      );
      $("#listaConceptos").enumera();
      $("#totalRecibo")
        .empty()
        .html("$ " + total);

      $("#flexCondonacion").prop("checked", false);
      $("#semanaDiv,#anioDiv,#agregaDiv,#importeDiv").hide();
      $("#concepto").val("");
      $("#ultimaSemana").empty();

      $(".elimina").on("click", function (e) {
        e.preventDefault();
        id = $(this).parents("tr").attr("id");
        $(this).parents("tr").remove();

        filasList = $("#listaConceptos").children("tbody").children("tr");
        total = 0;
        for (var x in filasList) {
          aux = $("#" + filasList[x].id).children("td");
          aux1 = $("#" + filasList[x].id)
            .children("td")
            .children();
          if (aux.length == 0) {
            importe = 0;
          } else {
            importe = datoColumna(1, aux);
          }
          if ($(aux1[4]).val() == "N") {
            total = total + importe;
          }
        }

        $("#totalRecibo")
          .empty()
          .html("$ " + total);

        $("#listaConceptos").enumera();

        if ($("#listaConceptos tbody").children("tr").length == 0)
          $("#reciboConceptos").hide();
      });
    },
  });

  $("#busqueda").validate({
    submitHandler: function (form) {
      datoOP = $("#buscadorOp").val();
      $.ajax({
        type: "post",
        url: "php/ajax/buscaOperador.php",
        data: { datoBusca: datoOP },
      })
        .done(function (r) {
          R = $.parseJSON(r);
          if (R[0] != 0) {
            semanaActual = R.semanaSig;
            anioSemana = R.anioSemana;
            const { inicio, fin } = R.semInicioFin;
            delete R.semanaSig;
            delete R.anioSemana;
            delete R.semInicioFin;
            datos = R[0];
            $("#divAgregaConcepto").show();
            $("#operadorName").empty().html(datos.nombreCompleto);
            $("#contableNum").empty().html(datos.contable);
            $("#idOperador").val(datos.idOperador);
            $("#semanaActual")
              .empty()
              .html(`Semana ${semanaActual} del ${inicio} al ${fin}`);
          } else {
            $("#operadorName").empty();
            $("#contableNum").empty();
            $("#idOperador").val("");
            $("#semanaActual").empty();
            $("#divAgregaConcepto").hide();
            alertify.alert("Warning!", "Operador no encontrado");
          }
        })
        .fail(function () {
          alertify.alert("Warning!", "Error al cargar el formulario");
        });
    },
  });
});
