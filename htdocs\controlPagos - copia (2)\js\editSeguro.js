$(document).ready(function () {
  if ($.get("res")) {
    Rsp = $.get("res");
    switch (Rsp) {
      case "0":
        rmsg = "Error en el proceso";
        break;
      case "1":
        rmsg = "Seguro actualizado correctamente";
        break;
    }
    alertify
      .alert()
      .setting({
        title: "Servitaxis Sitio 152",
        closable: false,
        message: rmsg,
        onok: function () {
          let myUrl = window.location.origin;
          myUrl = `${myUrl}/editSeguro.php`;
          $(location).attr("href", myUrl);
        },
      })
      .show();
  }

  $("#vigenciaBegin")
    .datepicker({
      dateFormat: "yy-mm-dd",
      defaultDate: "-6m",
      changeMonth: true,
      changeYear: true,
      numberOfMonths: 1,
    })
    .change(function () {
      $("#vigenciaEnd").datepicker("option", "minDate", $(this).val());
    });
  $("#vigenciaEnd")
    .datepicker({
      dateFormat: "yy-mm-dd",
      defaultDate: "+3m",
      changeMonth: true,
      changeYear: true,
      numberOfMonths: 1,
    })
    .change(function () {
      $("#vigenciaBegin").datepicker("option", "maxDate", $(this).val());
    });

  $("#divBtnBaja,#dataAuto").hide();
  $("#formSeguro").hide();

  $("#busqueda").validate({
    submitHandler: function (form) {
      datoOP = $("#buscaEconomico").val();
      $.ajax({
        type: "post",
        url: "php/ajax/buscaEconomico.php",
        data: { datoBusca: datoOP, seguro: 1 },
      })
        .done(function (r) {
          R = $.parseJSON(r);
          console.log(R);
          if (R[0] != 0) {
            const {
              anio,
              apellidoMaterno,
              apellidoPaterno,
              economico,
              idEconomico,
              marca,
              modelo,
              nombre,
              placa,
            } = R;
            const {
              aseguradora,
              idSeguro,
              poliza,
              vigenciaFin,
              vigenciaInicio,
            } = R["suSeguro"];
            $("#dataAuto").empty().append(`<div class="col-lg-8">
            <h5>Propietario: ${nombre} ${apellidoPaterno} ${apellidoMaterno}</h5>
            <h5>NIV: ${idEconomico}</h5><input type="hidden" name="idEconomico" id="idEconomico" value="${idEconomico}">
            <h5>Marca: ${marca}</h5>
            <h5>Modelo: ${modelo}</h5>
            <h5>Placa: ${placa}</h5>
            <h5>Año: ${anio}</h5>
            <h5>Economico: ${economico}</h5>
        </div>`);
            $("#divBtnBaja,#dataAuto").show();
            $("#formSeguro").show();

            $("#aseguradora").val(aseguradora);
            $("#poliza").val(poliza);
            $("#vigenciaBegin").val(vigenciaInicio);
            $("#vigenciaEnd").val(vigenciaFin);
            $("#idSeguro").val(idSeguro);
          } else {
            $("#aseguradora").val('');
            $("#poliza").val('');
            $("#vigenciaBegin").val('');
            $("#vigenciaEnd").val('');
            $("#idSeguro").val('');

            $("#dataAuto").empty();
            $("#divBtnBaja,#dataAuto").hide();
            $("#formSeguro").hide();
            alertify.alert("Servitaxis Sitio 152", "Economico no encontrado");
          }
        })
        .fail(function () {
          alertify.alert("Warning!", "Error al cargar el formulario");
        });
    },
  });
});
