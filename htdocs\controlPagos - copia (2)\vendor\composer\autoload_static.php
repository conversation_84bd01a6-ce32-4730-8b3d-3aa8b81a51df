<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticInitc7c3a80fc71ede24dc10ffcb430520e6
{
    public static $prefixLengthsPsr4 = array (
        'P' => 
        array (
            'PHP<PERSON>ailer\\PHPMailer\\' => 20,
        ),
    );

    public static $prefixDirsPsr4 = array (
        'PHPMailer\\PHPMailer\\' => 
        array (
            0 => __DIR__ . '/..' . '/phpmailer/phpmailer/src',
        ),
    );

    public static $classMap = array (
        'Composer\\InstalledVersions' => __DIR__ . '/..' . '/composer/InstalledVersions.php',
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->prefixLengthsPsr4 = ComposerStaticInitc7c3a80fc71ede24dc10ffcb430520e6::$prefixLengthsPsr4;
            $loader->prefixDirsPsr4 = ComposerStaticInitc7c3a80fc71ede24dc10ffcb430520e6::$prefixDirsPsr4;
            $loader->classMap = ComposerStaticInitc7c3a80fc71ede24dc10ffcb430520e6::$classMap;

        }, null, ClassLoader::class);
    }
}
