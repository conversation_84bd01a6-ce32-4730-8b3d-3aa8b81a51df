<?php
class operador
{

    public $nombre;
    public $paterno;
    public $materno;
    public $contable;
    public $referenciado;
    public $celular;
    public $telefono;
    public $domicilio;
    public $estado;
    public $identificador;

    function __construct($std)
    {
        $this->estado = $std;
    }

    public function dataOperador($conexion)
    {
        $q = "SELECT idOperador, nombre, apellidoPaterno, apellidoMaterno, contable, referenciado, celular, 
            telefono, domicilio, correo, estado 
        FROM operador 
        WHERE estado = '$this->estado' 
        AND contable = '$this->contable'";
        $eq = $conexion->query($q);
        if (!$eq) {
            $res = array(0);
        } else {
            if ($eq->num_rows != 0) {
                $res = $eq->fetch_array(MYSQLI_ASSOC);
            } else {
                $res = array(0);
            }
        }
        return $res;
    }

    public function dataOperadorContableNombre($conexion)
    {
        $q = "SELECT idOperador, nombre, apellidoPaterno, apellidoMaterno, contable, referenciado, celular, 
                telefono, domicilio, correo, estado 
                FROM operador 
                WHERE estado = '$this->estado' 
                AND contable = '$this->contable'
                OR CONCAT(nombre, ' ', apellidoPaterno, ' ', apellidoMaterno) LIKE '$this->nombre'";
        $eq = $conexion->query($q);
        if (!$eq) {
            $res = array(0);
        } else {
            if ($eq->num_rows != 0) {
                $res = $eq->fetch_array(MYSQLI_ASSOC);
            } else {
                $res = array(0);
            }
        }
        return $res;
    }

    public function dataXIdentificador($conexion)
    {
        $q = "SELECT idOperador, nombre, apellidoPaterno, apellidoMaterno, contable, referenciado, celular, 
            telefono, domicilio, correo, estado 
        FROM operador 
        WHERE estado = '$this->estado' 
        AND idOperador = '$this->identificador'";
        $eq = $conexion->query($q);
        if (!$eq) {
            $res = array(0);
        } else {
            if ($eq->num_rows != 0) {
                $res = $eq->fetch_array(MYSQLI_ASSOC);
            } else {
                $res = array(0);
            }
        }
        return $res;
    }

    public function operadorList($conexion)
    {
        $q = "SELECT	o.idOperador, CONCAT(o.nombre,' ',o.apellidoPaterno,' ',o.apellidoMaterno) AS nombreCompleto, o.contable, 
		o.referenciado, o.celular, o.telefono, o.domicilio, o.correo, o.estado,
		IF(l.vigenciaFin IS NULL,'Vencida',l.vigenciaFin) AS vigenciaLicencia
FROM operador o
LEFT JOIN licenciavigente l ON o.idOperador = l.idOperador ;";
        $eq = $conexion->query($q);

        return $eq;
    }

    public function activaDesactiva($conexion)
    {
        $q = "UPDATE operador SET estado='$this->estado'
            WHERE idOperador='$this->identificador'";

        $eq = $conexion->query($q);

        return $eq;
    }

    public function validaContable($conexion)
    {
        $q = "SELECT idOperador FROM operador
        WHERE contable = '$this->contable'
        AND estado = 'A';";

        $eq = $conexion->query($q);

        if ($eq->num_rows != 0) {
            $x = false;
        } else {
            $x = true;
        }

        return $x;
    }

    public function economico($conexion)
    {
        $q = "SELECT e.economico 
        FROM operadoreconomico o 
        INNER JOIN economico e ON o.idEconomico = e.idEconomico
        WHERE o.idOperador = '$this->identificador'
        AND o.estado = 'A';";
        $eq = $conexion->query($q);
        if ($eq->num_rows != 0) {
            $row = $eq->fetch_array(MYSQLI_ASSOC);
            $x = $row['economico'];
        } else {
            $x = null;
        }

        return $x;
    }
}
