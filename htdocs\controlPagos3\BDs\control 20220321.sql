-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 4.7.9
-- https://www.phpmyadmin.net/
--
-- Servidor: localhost
-- Tiempo de generación: 21-03-2022 a las 20:54:21
-- Versión del servidor: 8.0.27
-- Versión de PHP: 5.6.40-57+ubuntu21.04.1+deb.sury.org+1

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
SET AUTOCOMMIT = 0;
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Base de datos: `control`
--

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `abono`
--

CREATE TABLE `abono` (
  `idAbono` int NOT NULL,
  `idUsuario` int NOT NULL,
  `tipoUsuario` enum('A','C','J') NOT NULL,
  `idOperador` int NOT NULL,
  `idImporte` int NOT NULL,
  `idRecibo` char(10) NOT NULL,
  `semana` int NOT NULL,
  `anio` char(4) NOT NULL,
  `tipoAbono` enum('A','C','V') DEFAULT NULL COMMENT 'abono,condonacion,convenio'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `cargo`
--

CREATE TABLE `cargo` (
  `tipoCargo` enum('C','P') NOT NULL,
  `folio` char(10) NOT NULL COMMENT 'noCheque o folioConvenio',
  `fechaCheque` date DEFAULT NULL,
  `idOperador` int NOT NULL,
  `idUsuario` int NOT NULL,
  `tipoUsuario` enum('A','C','J') NOT NULL,
  `idImporteConvenio` int DEFAULT NULL,
  `monto` decimal(8,2) NOT NULL,
  `fechaRegistro` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;

-- --------------------------------------------------------

--
-- Estructura Stand-in para la vista `carroactivo`
-- (Véase abajo para la vista actual)
--
CREATE TABLE `carroactivo` (
`idEconomico` char(17)
,`Descripcion` varchar(80)
);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `concepto`
--

CREATE TABLE `concepto` (
  `idConcepto` int NOT NULL,
  `descripcion` varchar(100) NOT NULL,
  `fechaRegistro` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=COMPACT;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `cuentaBancaria`
--

CREATE TABLE `cuentaBancaria` (
  `idCuentaBancaria` char(11) NOT NULL,
  `banco` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `estado` enum('A','I') NOT NULL,
  `fechaRegistro` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `economico`
--

CREATE TABLE `economico` (
  `idEconomico` char(17) NOT NULL,
  `idOwner` int NOT NULL,
  `economico` int NOT NULL,
  `marca` varchar(30) NOT NULL,
  `modelo` varchar(30) NOT NULL,
  `placa` char(6) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `anio` char(4) DEFAULT NULL,
  `propietarioAuto` varchar(100) NOT NULL,
  `estado` enum('A','I') NOT NULL DEFAULT 'A',
  `fechaRegistro` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;

-- --------------------------------------------------------

--
-- Estructura Stand-in para la vista `economicopropietario`
-- (Véase abajo para la vista actual)
--
CREATE TABLE `economicopropietario` (
`idEconomico` char(17)
,`Propietario` varchar(92)
,`economico` int
,`marca` varchar(30)
,`modelo` varchar(30)
,`placa` char(6)
,`anio` char(4)
,`Status` varchar(8)
,`propietarioAuto` varchar(100)
);

-- --------------------------------------------------------

--
-- Estructura Stand-in para la vista `fechaultimadesactivacion`
-- (Véase abajo para la vista actual)
--
CREATE TABLE `fechaultimadesactivacion` (
`idConcepto` int
,`fechaDesactivado` timestamp
);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `importe`
--

CREATE TABLE `importe` (
  `idImporte` int NOT NULL,
  `idConcepto` int NOT NULL,
  `monto` decimal(8,2) NOT NULL,
  `fechaRegistro` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `estatus` enum('A','I') NOT NULL,
  `fechaUpdate` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `importeconvenio`
--

CREATE TABLE `importeconvenio` (
  `idImporteConvenio` int NOT NULL,
  `monto` decimal(6,2) NOT NULL,
  `fechaRegistro` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `fechaBaja` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `licencia`
--

CREATE TABLE `licencia` (
  `idLicencia` char(9) NOT NULL,
  `idOperador` int NOT NULL,
  `vigenciaInicio` date DEFAULT NULL,
  `vigenciaFin` date NOT NULL,
  `fechaRegistro` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;

-- --------------------------------------------------------

--
-- Estructura Stand-in para la vista `licenciacaducada`
-- (Véase abajo para la vista actual)
--
CREATE TABLE `licenciacaducada` (
`idLicencia` char(9)
,`idOperador` int
,`vigenciaInicio` date
,`vigenciaFin` date
);

-- --------------------------------------------------------

--
-- Estructura Stand-in para la vista `licenciavigente`
-- (Véase abajo para la vista actual)
--
CREATE TABLE `licenciavigente` (
`idLicencia` char(9)
,`idOperador` int
,`vigenciaInicio` date
,`vigenciaFin` date
);

-- --------------------------------------------------------

--
-- Estructura Stand-in para la vista `licenciaxcaducar`
-- (Véase abajo para la vista actual)
--
CREATE TABLE `licenciaxcaducar` (
`idLicencia` char(9)
,`idOperador` int
,`vigenciaInicio` date
,`vigenciaFin` date
);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `login`
--

CREATE TABLE `login` (
  `idUsuario` int NOT NULL,
  `tipoUsuario` enum('A','C','J') NOT NULL COMMENT 'admin, caputrista, caja',
  `userName` varchar(16) NOT NULL,
  `passwd` char(60) NOT NULL,
  `estado` enum('A','I') NOT NULL,
  `fechaRegistro` timestamp NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `operador`
--

CREATE TABLE `operador` (
  `idOperador` int NOT NULL,
  `nombre` varchar(30) NOT NULL,
  `apellidoPaterno` varchar(30) NOT NULL,
  `apellidoMaterno` varchar(30) NOT NULL,
  `contable` char(3) NOT NULL,
  `referenciado` char(7) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `celular` char(10) NOT NULL,
  `telefono` char(10) DEFAULT NULL,
  `domicilio` varchar(100) DEFAULT NULL,
  `correo` varchar(80) NOT NULL,
  `estado` enum('A','I') NOT NULL DEFAULT 'A',
  `fechaRegistro` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;

-- --------------------------------------------------------

--
-- Estructura Stand-in para la vista `operadorcontable`
-- (Véase abajo para la vista actual)
--
CREATE TABLE `operadorcontable` (
`idOperador` int
,`Contable` varchar(98)
);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `operadoreconomico`
--

CREATE TABLE `operadoreconomico` (
  `idOperador` int NOT NULL,
  `idEconomico` char(17) NOT NULL,
  `estado` enum('A','I') NOT NULL,
  `fechaRegsitro` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;

-- --------------------------------------------------------

--
-- Estructura Stand-in para la vista `operadorview`
-- (Véase abajo para la vista actual)
--
CREATE TABLE `operadorview` (
`idOperador` int
,`nombreCompleto` varchar(92)
,`contable` char(3)
,`referenciado` char(7)
,`correo` varchar(80)
);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `owner`
--

CREATE TABLE `owner` (
  `idOwner` int NOT NULL,
  `nombre` varchar(30) NOT NULL,
  `apellidoPaterno` varchar(30) NOT NULL,
  `apellidoMaterno` varchar(30) NOT NULL,
  `telefono` char(10) NOT NULL,
  `correo` varchar(80) NOT NULL,
  `tipo` enum('S','A','D','T') NOT NULL,
  `estado` enum('A','I') NOT NULL DEFAULT 'A',
  `fechaRegistro` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=COMPACT;

-- --------------------------------------------------------

--
-- Estructura Stand-in para la vista `ownerlist`
-- (Véase abajo para la vista actual)
--
CREATE TABLE `ownerlist` (
`idOwner` int
,`nombreCompleto` varchar(92)
);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `recibo`
--

CREATE TABLE `recibo` (
  `idRecibo` char(10) NOT NULL,
  `estado` enum('A','P') NOT NULL DEFAULT 'A' COMMENT 'adeudo, pagado',
  `efectivo` decimal(8,2) NOT NULL DEFAULT '0.00',
  `cheque` decimal(8,2) NOT NULL DEFAULT '0.00',
  `convenio` decimal(8,2) NOT NULL DEFAULT '0.00',
  `numeroCheque` varchar(10) DEFAULT NULL,
  `fechaRegistro` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `fechaPago` timestamp NULL DEFAULT NULL,
  `fechaDeposito` date DEFAULT NULL,
  `movimiento` char(9) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `idUsuario` int DEFAULT NULL COMMENT 'usuario recibió Pago',
  `tipoUsuario` enum('A','C','J') DEFAULT NULL,
  `idCuentaBancaria` char(11) DEFAULT NULL,
  `observaciones` varchar(200) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `seguro`
--

CREATE TABLE `seguro` (
  `idSeguro` int NOT NULL,
  `idEconomico` char(17) NOT NULL,
  `aseguradora` varchar(30) NOT NULL,
  `poliza` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `vigenciaInicio` date NOT NULL,
  `vigenciaFin` date NOT NULL,
  `fechaRegistro` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=COMPACT;

-- --------------------------------------------------------

--
-- Estructura Stand-in para la vista `segurocaduco`
-- (Véase abajo para la vista actual)
--
CREATE TABLE `segurocaduco` (
`idSeguro` int
,`idEconomico` char(17)
,`aseguradora` varchar(30)
,`poliza` varchar(20)
,`vigenciaInicio` date
,`vigenciaFin` date
);

-- --------------------------------------------------------

--
-- Estructura Stand-in para la vista `segurovigente`
-- (Véase abajo para la vista actual)
--
CREATE TABLE `segurovigente` (
`idSeguro` int
,`idEconomico` char(17)
,`aseguradora` varchar(30)
,`poliza` varchar(20)
,`vigenciaInicio` date
,`vigenciaFin` date
);

-- --------------------------------------------------------

--
-- Estructura Stand-in para la vista `seguroxcaducar`
-- (Véase abajo para la vista actual)
--
CREATE TABLE `seguroxcaducar` (
`idSeguro` int
,`idEconomico` char(17)
,`aseguradora` varchar(30)
,`poliza` varchar(20)
,`vigenciaInicio` date
,`vigenciaFin` date
);

-- --------------------------------------------------------

--
-- Estructura Stand-in para la vista `totalcargooperador`
-- (Véase abajo para la vista actual)
--
CREATE TABLE `totalcargooperador` (
`idOperador` int
,`adeudoPrestamo` decimal(30,2)
,`adeudoConvenio` decimal(30,2)
,`adeudoTotal` decimal(30,2)
);

-- --------------------------------------------------------

--
-- Estructura Stand-in para la vista `totalrecibo`
-- (Véase abajo para la vista actual)
--
CREATE TABLE `totalrecibo` (
`idRecibo` char(10)
,`Total` decimal(30,2)
,`Pago` decimal(9,2)
,`Cambio` decimal(31,2)
);

-- --------------------------------------------------------

--
-- Estructura Stand-in para la vista `userlist`
-- (Véase abajo para la vista actual)
--
CREATE TABLE `userlist` (
`idUsuario` int
,`nombreUsuario` varchar(92)
);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `usuario`
--

CREATE TABLE `usuario` (
  `idUsuario` int NOT NULL,
  `nombre` varchar(30) NOT NULL,
  `apellidoPaterno` varchar(30) NOT NULL,
  `apellidoMaterno` varchar(30) NOT NULL,
  `correo` varchar(80) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `telefono` char(10) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `fechaRegistro` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=COMPACT;

-- --------------------------------------------------------

--
-- Estructura Stand-in para la vista `vistaCuenta`
-- (Véase abajo para la vista actual)
--
CREATE TABLE `vistaCuenta` (
`idCuentaBancaria` char(11)
,`Cuenta` varchar(32)
);

-- --------------------------------------------------------

--
-- Estructura Stand-in para la vista `yearabono`
-- (Véase abajo para la vista actual)
--
CREATE TABLE `yearabono` (
`anio` char(4)
,`year` char(4)
);

-- --------------------------------------------------------

--
-- Estructura para la vista `carroactivo`
--
DROP TABLE IF EXISTS `carroactivo`;

CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `carroactivo`  AS  select `e`.`idEconomico` AS `idEconomico`,concat_ws(' ',`e`.`economico`,`e`.`marca`,`e`.`modelo`,`e`.`placa`) AS `Descripcion` from `economico` `e` where (`e`.`estado` = 'A') order by `e`.`economico` ;

-- --------------------------------------------------------

--
-- Estructura para la vista `economicopropietario`
--
DROP TABLE IF EXISTS `economicopropietario`;

CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `economicopropietario`  AS  select `e`.`idEconomico` AS `idEconomico`,concat(`o`.`nombre`,' ',`o`.`apellidoPaterno`,' ',`o`.`apellidoMaterno`) AS `Propietario`,`e`.`economico` AS `economico`,`e`.`marca` AS `marca`,`e`.`modelo` AS `modelo`,`e`.`placa` AS `placa`,`e`.`anio` AS `anio`,if((`e`.`estado` = 'A'),'Activo','Inactivo') AS `Status`,`e`.`propietarioAuto` AS `propietarioAuto` from (`economico` `e` join `owner` `o` on((`e`.`idOwner` = `o`.`idOwner`))) ;

-- --------------------------------------------------------

--
-- Estructura para la vista `fechaultimadesactivacion`
--
DROP TABLE IF EXISTS `fechaultimadesactivacion`;

CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `fechaultimadesactivacion`  AS  select `i`.`idConcepto` AS `idConcepto`,max(`i`.`fechaUpdate`) AS `fechaDesactivado` from `importe` `i` where (`i`.`estatus` = 'I') group by `i`.`idConcepto` ;

-- --------------------------------------------------------

--
-- Estructura para la vista `licenciacaducada`
--
DROP TABLE IF EXISTS `licenciacaducada`;

CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `licenciacaducada`  AS  select `l`.`idLicencia` AS `idLicencia`,`l`.`idOperador` AS `idOperador`,`l`.`vigenciaInicio` AS `vigenciaInicio`,`l`.`vigenciaFin` AS `vigenciaFin` from `licencia` `l` where ((to_days(`l`.`vigenciaFin`) - to_days(now())) < 0) ;

-- --------------------------------------------------------

--
-- Estructura para la vista `licenciavigente`
--
DROP TABLE IF EXISTS `licenciavigente`;

CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `licenciavigente`  AS  select `l`.`idLicencia` AS `idLicencia`,`l`.`idOperador` AS `idOperador`,`l`.`vigenciaInicio` AS `vigenciaInicio`,`l`.`vigenciaFin` AS `vigenciaFin` from `licencia` `l` where ((to_days(`l`.`vigenciaFin`) - to_days(now())) > 0) ;

-- --------------------------------------------------------

--
-- Estructura para la vista `licenciaxcaducar`
--
DROP TABLE IF EXISTS `licenciaxcaducar`;

CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `licenciaxcaducar`  AS  select `l`.`idLicencia` AS `idLicencia`,`l`.`idOperador` AS `idOperador`,`l`.`vigenciaInicio` AS `vigenciaInicio`,`l`.`vigenciaFin` AS `vigenciaFin` from `licencia` `l` where ((to_days(`l`.`vigenciaFin`) - to_days(now())) between 0 and 15) ;

-- --------------------------------------------------------

--
-- Estructura para la vista `operadorcontable`
--
DROP TABLE IF EXISTS `operadorcontable`;

CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `operadorcontable`  AS  select `o`.`idOperador` AS `idOperador`,concat(`o`.`contable`,' - ',`o`.`nombre`,' ',`o`.`apellidoPaterno`,' ',`o`.`apellidoMaterno`) AS `Contable` from `operador` `o` where (`o`.`estado` = 'A') order by `o`.`contable` ;

-- --------------------------------------------------------

--
-- Estructura para la vista `operadorview`
--
DROP TABLE IF EXISTS `operadorview`;

CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `operadorview`  AS  select `operador`.`idOperador` AS `idOperador`,concat(`operador`.`nombre`,' ',`operador`.`apellidoPaterno`,' ',`operador`.`apellidoMaterno`) AS `nombreCompleto`,`operador`.`contable` AS `contable`,`operador`.`referenciado` AS `referenciado`,`operador`.`correo` AS `correo` from `operador` ;

-- --------------------------------------------------------

--
-- Estructura para la vista `ownerlist`
--
DROP TABLE IF EXISTS `ownerlist`;

CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `ownerlist`  AS  select `owner`.`idOwner` AS `idOwner`,concat(`owner`.`apellidoPaterno`,' ',`owner`.`apellidoMaterno`,' ',`owner`.`nombre`) AS `nombreCompleto` from `owner` where (`owner`.`estado` = 'A') order by `owner`.`apellidoPaterno` ;

-- --------------------------------------------------------

--
-- Estructura para la vista `segurocaduco`
--
DROP TABLE IF EXISTS `segurocaduco`;

CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `segurocaduco`  AS  select `s`.`idSeguro` AS `idSeguro`,`s`.`idEconomico` AS `idEconomico`,`s`.`aseguradora` AS `aseguradora`,`s`.`poliza` AS `poliza`,`s`.`vigenciaInicio` AS `vigenciaInicio`,`s`.`vigenciaFin` AS `vigenciaFin` from `seguro` `s` where ((to_days(`s`.`vigenciaFin`) - to_days(now())) < 0) ;

-- --------------------------------------------------------

--
-- Estructura para la vista `segurovigente`
--
DROP TABLE IF EXISTS `segurovigente`;

CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `segurovigente`  AS  select `s`.`idSeguro` AS `idSeguro`,`s`.`idEconomico` AS `idEconomico`,`s`.`aseguradora` AS `aseguradora`,`s`.`poliza` AS `poliza`,`s`.`vigenciaInicio` AS `vigenciaInicio`,`s`.`vigenciaFin` AS `vigenciaFin` from `seguro` `s` where ((to_days(`s`.`vigenciaFin`) - to_days(now())) > 0) ;

-- --------------------------------------------------------

--
-- Estructura para la vista `seguroxcaducar`
--
DROP TABLE IF EXISTS `seguroxcaducar`;

CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `seguroxcaducar`  AS  select `s`.`idSeguro` AS `idSeguro`,`s`.`idEconomico` AS `idEconomico`,`s`.`aseguradora` AS `aseguradora`,`s`.`poliza` AS `poliza`,`s`.`vigenciaInicio` AS `vigenciaInicio`,`s`.`vigenciaFin` AS `vigenciaFin` from `seguro` `s` where ((to_days(`s`.`vigenciaFin`) - to_days(now())) between 0 and 15) ;

-- --------------------------------------------------------

--
-- Estructura para la vista `totalcargooperador`
--
DROP TABLE IF EXISTS `totalcargooperador`;

CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `totalcargooperador`  AS  select `c`.`idOperador` AS `idOperador`,sum(if((`c`.`tipoCargo` = 'P'),`c`.`monto`,0)) AS `adeudoPrestamo`,sum(if((`c`.`tipoCargo` = 'C'),`c`.`monto`,0)) AS `adeudoConvenio`,sum(`c`.`monto`) AS `adeudoTotal` from `cargo` `c` group by `c`.`idOperador` ;

-- --------------------------------------------------------

--
-- Estructura para la vista `totalrecibo`
--
DROP TABLE IF EXISTS `totalrecibo`;

CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `totalrecibo`  AS  select `r`.`idRecibo` AS `idRecibo`,sum(`i`.`monto`) AS `Total`,(`r`.`efectivo` + `r`.`cheque`) AS `Pago`,((`r`.`efectivo` + `r`.`cheque`) - sum(`i`.`monto`)) AS `Cambio` from ((`abono` `a` join `recibo` `r` on((`a`.`idRecibo` = `r`.`idRecibo`))) join `importe` `i` on((`a`.`idImporte` = `i`.`idImporte`))) where (`a`.`tipoAbono` = 'A') group by `r`.`idRecibo` ;

-- --------------------------------------------------------

--
-- Estructura para la vista `userlist`
--
DROP TABLE IF EXISTS `userlist`;

CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `userlist`  AS  select `usuario`.`idUsuario` AS `idUsuario`,concat(`usuario`.`nombre`,' ',`usuario`.`apellidoPaterno`,' ',`usuario`.`apellidoMaterno`) AS `nombreUsuario` from `usuario` ;

-- --------------------------------------------------------

--
-- Estructura para la vista `vistaCuenta`
--
DROP TABLE IF EXISTS `vistaCuenta`;

CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `vistaCuenta`  AS  select `c`.`idCuentaBancaria` AS `idCuentaBancaria`,concat(`c`.`banco`,'-',`c`.`idCuentaBancaria`) AS `Cuenta` from `cuentaBancaria` `c` ;

-- --------------------------------------------------------

--
-- Estructura para la vista `yearabono`
--
DROP TABLE IF EXISTS `yearabono`;

CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `yearabono`  AS  select `abono`.`anio` AS `anio`,`abono`.`anio` AS `year` from `abono` group by `abono`.`anio` order by `abono`.`anio` desc ;

--
-- Índices para tablas volcadas
--

--
-- Indices de la tabla `abono`
--
ALTER TABLE `abono`
  ADD PRIMARY KEY (`idAbono`),
  ADD KEY `fkAbonoRecibo` (`idRecibo`),
  ADD KEY `fkAbonoImporte` (`idImporte`),
  ADD KEY `fkAbonoOperador` (`idOperador`),
  ADD KEY `fkAbonoLogin` (`idUsuario`,`tipoUsuario`);

--
-- Indices de la tabla `cargo`
--
ALTER TABLE `cargo`
  ADD PRIMARY KEY (`tipoCargo`,`folio`),
  ADD KEY `fkCargoImporteConvenio` (`idImporteConvenio`),
  ADD KEY `fkCargoLogin` (`idUsuario`,`tipoUsuario`),
  ADD KEY `fkCargoOperador` (`idOperador`);

--
-- Indices de la tabla `concepto`
--
ALTER TABLE `concepto`
  ADD PRIMARY KEY (`idConcepto`);

--
-- Indices de la tabla `cuentaBancaria`
--
ALTER TABLE `cuentaBancaria`
  ADD PRIMARY KEY (`idCuentaBancaria`);

--
-- Indices de la tabla `economico`
--
ALTER TABLE `economico`
  ADD PRIMARY KEY (`idEconomico`),
  ADD KEY `fkEconomicoOwner` (`idOwner`);

--
-- Indices de la tabla `importe`
--
ALTER TABLE `importe`
  ADD PRIMARY KEY (`idImporte`),
  ADD KEY `fkImporteConcepto` (`idConcepto`);

--
-- Indices de la tabla `importeconvenio`
--
ALTER TABLE `importeconvenio`
  ADD PRIMARY KEY (`idImporteConvenio`);

--
-- Indices de la tabla `licencia`
--
ALTER TABLE `licencia`
  ADD PRIMARY KEY (`idLicencia`),
  ADD KEY `fkLicenciaOperador` (`idOperador`);

--
-- Indices de la tabla `login`
--
ALTER TABLE `login`
  ADD PRIMARY KEY (`idUsuario`,`tipoUsuario`);

--
-- Indices de la tabla `operador`
--
ALTER TABLE `operador`
  ADD PRIMARY KEY (`idOperador`);

--
-- Indices de la tabla `operadoreconomico`
--
ALTER TABLE `operadoreconomico`
  ADD PRIMARY KEY (`idOperador`,`idEconomico`),
  ADD KEY `fkOperadorEconomicoEconomico` (`idEconomico`);

--
-- Indices de la tabla `owner`
--
ALTER TABLE `owner`
  ADD PRIMARY KEY (`idOwner`);

--
-- Indices de la tabla `recibo`
--
ALTER TABLE `recibo`
  ADD PRIMARY KEY (`idRecibo`),
  ADD KEY `fkReciboCuentaBancaria` (`idCuentaBancaria`),
  ADD KEY `fkReciboLogin` (`idUsuario`,`tipoUsuario`);

--
-- Indices de la tabla `seguro`
--
ALTER TABLE `seguro`
  ADD PRIMARY KEY (`idSeguro`),
  ADD KEY `fkSeguroEconomico` (`idEconomico`);

--
-- Indices de la tabla `usuario`
--
ALTER TABLE `usuario`
  ADD PRIMARY KEY (`idUsuario`);

--
-- AUTO_INCREMENT de las tablas volcadas
--

--
-- AUTO_INCREMENT de la tabla `abono`
--
ALTER TABLE `abono`
  MODIFY `idAbono` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=95;

--
-- AUTO_INCREMENT de la tabla `concepto`
--
ALTER TABLE `concepto`
  MODIFY `idConcepto` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- AUTO_INCREMENT de la tabla `importe`
--
ALTER TABLE `importe`
  MODIFY `idImporte` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=17;

--
-- AUTO_INCREMENT de la tabla `importeconvenio`
--
ALTER TABLE `importeconvenio`
  MODIFY `idImporteConvenio` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT de la tabla `operador`
--
ALTER TABLE `operador`
  MODIFY `idOperador` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;

--
-- AUTO_INCREMENT de la tabla `owner`
--
ALTER TABLE `owner`
  MODIFY `idOwner` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT de la tabla `seguro`
--
ALTER TABLE `seguro`
  MODIFY `idSeguro` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;

--
-- AUTO_INCREMENT de la tabla `usuario`
--
ALTER TABLE `usuario`
  MODIFY `idUsuario` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- Restricciones para tablas volcadas
--

--
-- Filtros para la tabla `abono`
--
ALTER TABLE `abono`
  ADD CONSTRAINT `fkAbonoImporte` FOREIGN KEY (`idImporte`) REFERENCES `importe` (`idImporte`) ON UPDATE CASCADE,
  ADD CONSTRAINT `fkAbonoLogin` FOREIGN KEY (`idUsuario`,`tipoUsuario`) REFERENCES `login` (`idUsuario`, `tipoUsuario`) ON DELETE RESTRICT ON UPDATE CASCADE,
  ADD CONSTRAINT `fkAbonoOperador` FOREIGN KEY (`idOperador`) REFERENCES `operador` (`idOperador`) ON UPDATE CASCADE,
  ADD CONSTRAINT `fkAbonoRecibo` FOREIGN KEY (`idRecibo`) REFERENCES `recibo` (`idRecibo`) ON UPDATE CASCADE;

--
-- Filtros para la tabla `cargo`
--
ALTER TABLE `cargo`
  ADD CONSTRAINT `fkCargoImporteConvenio` FOREIGN KEY (`idImporteConvenio`) REFERENCES `importeconvenio` (`idImporteConvenio`) ON UPDATE CASCADE,
  ADD CONSTRAINT `fkCargoLogin` FOREIGN KEY (`idUsuario`,`tipoUsuario`) REFERENCES `login` (`idUsuario`, `tipoUsuario`) ON DELETE RESTRICT ON UPDATE CASCADE,
  ADD CONSTRAINT `fkCargoOperador` FOREIGN KEY (`idOperador`) REFERENCES `operador` (`idOperador`) ON DELETE RESTRICT ON UPDATE CASCADE;

--
-- Filtros para la tabla `economico`
--
ALTER TABLE `economico`
  ADD CONSTRAINT `fkEconomicoOwner` FOREIGN KEY (`idOwner`) REFERENCES `owner` (`idOwner`) ON UPDATE CASCADE;

--
-- Filtros para la tabla `importe`
--
ALTER TABLE `importe`
  ADD CONSTRAINT `fkImporteConcepto` FOREIGN KEY (`idConcepto`) REFERENCES `concepto` (`idConcepto`) ON UPDATE CASCADE;

--
-- Filtros para la tabla `licencia`
--
ALTER TABLE `licencia`
  ADD CONSTRAINT `fkLicenciaOperador` FOREIGN KEY (`idOperador`) REFERENCES `operador` (`idOperador`) ON UPDATE CASCADE;

--
-- Filtros para la tabla `login`
--
ALTER TABLE `login`
  ADD CONSTRAINT `fkLoginUsuario` FOREIGN KEY (`idUsuario`) REFERENCES `usuario` (`idUsuario`) ON DELETE RESTRICT ON UPDATE CASCADE;

--
-- Filtros para la tabla `operadoreconomico`
--
ALTER TABLE `operadoreconomico`
  ADD CONSTRAINT `fkOperadorEconomicoEconomico` FOREIGN KEY (`idEconomico`) REFERENCES `economico` (`idEconomico`) ON DELETE RESTRICT ON UPDATE CASCADE,
  ADD CONSTRAINT `fkOperadorEconomicoOperador` FOREIGN KEY (`idOperador`) REFERENCES `operador` (`idOperador`) ON DELETE RESTRICT ON UPDATE CASCADE;

--
-- Filtros para la tabla `recibo`
--
ALTER TABLE `recibo`
  ADD CONSTRAINT `fkReciboCuentaBancaria` FOREIGN KEY (`idCuentaBancaria`) REFERENCES `cuentaBancaria` (`idCuentaBancaria`) ON DELETE RESTRICT ON UPDATE CASCADE,
  ADD CONSTRAINT `fkReciboLogin` FOREIGN KEY (`idUsuario`,`tipoUsuario`) REFERENCES `login` (`idUsuario`, `tipoUsuario`) ON DELETE RESTRICT ON UPDATE CASCADE;

--
-- Filtros para la tabla `seguro`
--
ALTER TABLE `seguro`
  ADD CONSTRAINT `fkSeguroEconomico` FOREIGN KEY (`idEconomico`) REFERENCES `economico` (`idEconomico`) ON DELETE RESTRICT ON UPDATE CASCADE;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
