<?php
class licencia
{
    public $expedicion;
    public $vigencia;
    public $identificador;
    public $operador;

    function __construct($fol, $exp, $vig, $op)
    {
        $this->expedicion = $exp;
        $this->vigencia = $vig;
        $this->identificador = $fol;
        $this->operador = $op;
    }

    public function saveLicencia($conexion)
    {
        $q = "INSERT INTO licencia(idLicencia, idOperador, vigenciaInicio, vigenciaFin) 
                VALUES ('$this->identificador',            
                '$this->operador',
                '$this->expedicion',
                '$this->vigencia')";
        $eq = $conexion->query($q);

        return $eq;
    }

    public function validaLicencia($conexion)
    {
        $q = "SELECT idOperador FROM licencia WHERE idLicencia = '$this->identificador'";
    }

    public function vigente($conexion)
    {
        $q = "SELECT idLicencia, vigenciaInicio, vigenciaFin 
        FROM licenciavigente 
        WHERE idOperador = '$this->operador'";
        $eq = $conexion->query($q);

        if ($eq->num_rows != 0) {
            $row = $eq->fetch_array(MYSQLI_ASSOC);
        } else {
            $row = array(0);
        }

        return $row;
    }

    public function update($conexion,$licencia)
    {
        $q = "UPDATE licencia 
            SET idLicencia='$this->identificador',
            vigenciaInicio='$this->expedicion',
            vigenciaFin='$this->vigencia'
            WHERE idOperador='$this->operador'
            AND idLicencia='$licencia'";
        $eq = $conexion->query($q);

        return $eq;
    }
}
