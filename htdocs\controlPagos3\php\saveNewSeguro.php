<?php
include_once("conexion.php");
include_once("configDB.php");
include_once("classProcesaVariable.php");
include_once("classSeguro.php");

if (isset($_POST['poliza'])) {

    $aseguradora = new procesaVariable($_POST['aseguradora']);
    $poliza = new procesaVariable($_POST['poliza']);
    $vigenciaBegin = new procesaVariable($_POST['vigenciaBegin']);
    $vigenciaEnd = new procesaVariable($_POST['vigenciaEnd']);
    $idEconomico = new procesaVariable($_POST['idEconomico']);

    $taxiConect = new foo_mysqli("localhost", USER, PASS, DBNAME);
    $taxiConect->query("SET NAMES utf8");

    $var = $taxiConect->iniciaTransaccion();
    
    $seguroAuto =  new seguro($idEconomico->valor, $aseguradora->valor, $poliza->valor, $vigenciaBegin->valor, $vigenciaEnd->valor);
    if ($seguroAuto->save($taxiConect)) {
        $var = 1;
    } else {
        $var = 0;
    }

    echo $taxiConect->error;
    $link = $taxiConect->finTransaccion($var, '1', '0');

    $taxiConect->resetTabla($var, 'idSeguro', 'seguro');

    $taxiConect->close();
} else {
    $var = '0';
}

header("Location: ../nuevoSeguro.php?res=$var");
