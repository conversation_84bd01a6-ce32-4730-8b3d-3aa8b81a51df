<?php
include_once("conexion.php");
include_once("configDB.php");
include_once("classAbono.php");
include_once("funciones.php");
include_once("classCorreo.php");

if (isset($recibo)) {
    $taxiConect = new foo_mysqli("localhost", USER, PASS, DBNAME);
    $taxiConect->query("SET NAMES utf8");

    $abonos = new abono();
    $abonos->recibo = $recibo;
    $aux = $abonos->getAbonoRecibo($taxiConect);
    $operador = $abonos->operadorRecibo($taxiConect);

    $captirista = $abonos->capturaCajaRecibo($taxiConect, 'abono');
    $cajera = $abonos->capturaCajaRecibo($taxiConect, 'recibo');

    $asunto = "Recibo $recibo";
    $body1 = dataTD($aux, '#FFFFFF', '#D9D9D9');
    $email = new correo($operador['correo'], $asunto);
    $email->cuerpoRecibo($body1['tabla'], $body1['total'], $operador, $recibo, $captirista, $cajera);

    $email->enviar();

    $taxiConect->close();
}
