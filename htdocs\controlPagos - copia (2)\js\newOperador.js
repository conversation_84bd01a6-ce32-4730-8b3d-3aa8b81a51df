$(document).ready(function () {
  $("input").cambiaMayus();

  if ($.get("resp")) {
    resp = $.get("resp");
    switch (resp) {
      case "0":
        msg = "Erro<PERSON> al guardar, intente nuevamente.";
        break;
      case "1":
        msg = "Operador guardado satisfacctoriamente";
        break;
    }
    alertify.alert("Servitaxis Sitio 152", msg);
  }

  $("#fechaExpedicion")
    .datepicker({
      dateFormat: "yy-mm-dd",
      changeMonth: true,
      changeYear: true,
      numberOfMonths: 1,
    })
    .change(function () {
      $("#fechaVigencia").datepicker("option", "minDate", $(this).val());
    });
  $("#fechaVigencia")
    .datepicker({
      dateFormat: "yy-mm-dd",
      defaultDate: "+3y",
      changeMonth: true,
      changeYear: true,
      numberOfMonths: 1,
    })
    .change(function () {
      $("#fechaExpedicion").datepicker("option", "maxDate", $(this).val());
    });

  $("#operadorForm").validate({
    rules: {
      contable: {
        remote: "php/ajax/validaVariable.php",
      },
      correo: {
        remote: "php/ajax/validaVariable.php",
      },
      referenciado: {
        remote: "php/ajax/validaVariable.php",
      },
      folio: {
        remote: "php/ajax/validaVariable.php",
      },
      fechaVigencia: {
        fechaMayor: true,
      },
    },
    messages: {
      contable: {
        remote: "El contable ya es utilizado por un operador activo.",
      },
      correo: {
        remote: "El correo ya es utilizado por un operador activo.",
      },
      referenciado: {
        remote: "El referenciado ya es utilizado por un operador activo.",
      },
      folio: {
        remote: "La licencia ya se encuetra registrada.",
      },
    },
  });
});
