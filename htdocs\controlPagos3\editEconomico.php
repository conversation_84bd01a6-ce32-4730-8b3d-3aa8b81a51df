<?php include_once('php/session.php'); ?>
<!DOCTYPE html>
<html>

<head>
    <meta charset="UTF-8">
    <?php include_once("includes/head.html"); ?>
    <script src="js/editEconomico.js"></script>

</head>

<body class="bg-light">
    <div class="container">
        <br>
        <div id="header"></div>
        <?php include_once("includes/menu.html"); ?>
        <div class="mt-4 row justify-content-center">
            <div class="col-lg-10">
                <div class="card border-success mb-3" style="max-width: 100%;">
                    <div class="card-header bg-info p-2 text-dark bg-opacity-10">
                        <h3>Editar de Economico</h3>
                    </div>
                    <div class="card-body">
                        <input type="hidden" name="admin" value="8709011992">
                        <div class="row justify-content-center text-center">
                            <h4>Escribe un número de economico</h4>
                        </div>
                        <div class="mt-4 row justify-content-center">
                            <div class="col-lg-8">
                                <form id="busqueda" autocomplete="off">
                                    <div class="row justify-content-center">
                                        <div class="col-lg-6">
                                            <div class="input-group mb-3">
                                                <input type="number" class="form-control" id="buscaEconomico" name="buscaEconomico" required placeholder="Buscador de Economico" aria-label="Buscador de Operador" aria-describedby="button-addon2">
                                                <button class="btn btn-outline-primary">Buscar</button>
                                            </div>
                                            <label id="buscaEconomico" class="error" for="buscaEconomico"></label>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                        <hr>
                        <form method="POST" action="php/editaEconomico.php" id="formEdicion" autocomplete="off">
                            <div class="row mt-4 justify-content-center" id="dataAuto">

                            </div>
                            <div class="row mt-4 justify-content-center">
                                <div class="col-lg-3">
                                    <label for="marca">
                                        <h5>Marca:</h5>
                                    </label>
                                    <input class="form-control" maxlength="30" name="marca" id="marca" placeholder="Ej: Nissan" required>
                                </div>
                                <div class="col-lg-3">
                                    <label for="modelo">
                                        <h5>Modelo:</h5>
                                    </label>
                                    <input class="form-control" maxlength="30" name="modelo" id="modelo" placeholder="Ej: Versa" required>
                                </div>
                            </div>
                            <div class="row mt-4 justify-content-center">
                                <div class="col-lg-4">
                                    <label for="propietarioAuto">
                                        <h5>Propietario del auto:</h5>
                                    </label>
                                    <input class="form-control" maxlength="100" name="propietarioAuto" id="propietarioAuto" required>
                                </div>
                                <div class="col-lg-2">
                                    <label for="placa">
                                        <h5>Placa:</h5>
                                    </label>
                                    <input class="form-control" minlength="6" maxlength="6" name="placa" id="placa" placeholder="Ej: A1234C" required>
                                </div>
                                <div class="col-lg-2">
                                    <label for="anio">
                                        <h5>Año:</h5>
                                    </label>
                                    <input type="number" class="form-control" min="2000" maxlength="4" minlength="4" name="anio" id="anio" required>
                                </div>
                            </div>
                            <div class="row mt-4 justify-content-center" id="divBtnBaja">
                                <div class="col-lg-4">
                                    <div class="d-grid gap-2">
                                        <button type="submit" class="btn btn-lg btn-warning">Guardar</button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        <div id="footer"></div>
    </div>
</body>

</html>