<?php include_once('php/session.php'); ?>
<!DOCTYPE html>
<html>

<head>
    <meta charset="UTF-8">
    <?php include_once("includes/head.html"); ?>
    <script src="js/conceptoImporte.js"></script>

</head>

<body class="bg-light">
    <div class="container">
        <br>
        <div id="header"></div>
        <?php include_once("includes/menu.html"); ?>

        <br>
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="card border-primary mb-3" style="max-width: 100%;">
                    <div class="card-header">
                        <h3>Edición de Importe(s)</h3>
                    </div>
                    <div class="card-body">
                        <div class="row justify-content-center">
                            <div class="col-lg-6 text-center">
                                <div class="alert alert-info" role="alert">
                                    Seleccione el concepto y agregue los montos requeridos, estos sustituiran a los anteriores.
                                </div>
                            </div>
                        </div>
                        <form method="POST" action="php/updateImportes.php" autocomplete="off" id="operadorForm">
                            <div class="row justify-content-center">
                                <div class="col-lg-6">
                                    <div class="form-group">
                                        <label for="concepto">
                                            <h5>Conceptos</h5>
                                        </label>
                                        <select class="form-control" name="concepto" id="concepto">
                                            <option value="">Elegir concepto ...</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <br>
                            <div id="renglones">
                                <div class="row justify-content-center">
                                    <div class="col-lg-4">
                                        <label for="importe1">
                                            <h5>Importe:</h5>
                                        </label>
                                        <div class="input-group mb-3">
                                            <span class="input-group-text">$</span>
                                            <input type="number" name="importe[]" id="importe1" class="form-control" min="0">
                                            <span class="input-group-text">.00</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <br>
                            <div class="row justify-content-center">
                                <div class="col-lg-3">
                                    <div class="d-grid gap-2">
                                        <button type="submit" class="btn btn-lg btn-success"><span class="ui-icon ui-icon-disk"></span>Guardar</button>
                                    </div>
                                </div>
                            </div>
                        </form>
                        <br>
                        <div class="row justify-content-center">
                            <div class="col-md-2">
                                <div class="d-grid gap-2">
                                    <button name="addIporte" id="addIporte" class="btn btn-sm btn-outline-success">Agregar<br><span class="ui-icon ui-icon-plusthick"></span></button>
                                </div>
                            </div>
                            <div class="col-md-2 offset-md-2">
                                <div class="d-grid gap-2">
                                    <button name="removeImporte" id="removeImporte" class="btn btn-sm btn-outline-danger">Quitar<br><span class="ui-icon ui-icon-minusthick"></span></button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div id="footer"></div>
    </div>
</body>

</html>