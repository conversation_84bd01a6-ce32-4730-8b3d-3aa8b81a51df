<?php
include_once("conexion.php");
include_once("configDB.php");
include_once("classProcesaVariable.php");
include_once("classImporte.php");

if (isset($_POST['concepto'])) {
    $concepto = new procesaVariable($_POST['concepto']);
    $importe = new procesaVariable($_POST['importe']);

    $taxiConect = new foo_mysqli("localhost", USER, PASS, DBNAME);
    $taxiConect->query("SET NAMES utf8");

    $transac = $taxiConect->iniciaTransaccion();

    $insert = "INSERT INTO concepto(idConcepto, descripcion) 
                VALUES (NULL,
                '$concepto->valor')";
    $exeInsert = $taxiConect->query($insert);

    if ($exeInsert) {
        $idConcepto = $taxiConect->insert_id;
        $importes = $importe->valor;
        foreach ($importes as $c => $m) {
            $insertImp = "INSERT INTO importe(idImporte, idConcepto, monto, estatus) 
            VALUES (NULL,'$idConcepto',
            '$m','A')";
            $exeInsertImp = $taxiConect->query($insertImp);

            if (!$exeInsertImp) {
                $transac = 0;
            }
        }
    } else {
        $transac = 0;
    }

    $link = $taxiConect->finTransaccion($transac, '$next', '$back');

    $taxiConect->close();
} else {
    $transac = 0;
}

header("Location: ../addConcepto.php?resp=$transac");
