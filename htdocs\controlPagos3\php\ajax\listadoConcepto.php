<?php
include_once("../conexion.php");
include_once("../configDB.php");

if (isset($_POST['myVal']) and $_POST['myVal'] == 8709011993) {

    $taxiConect = new foo_mysqli("localhost", USER, PASS, DBNAME);
    $taxiConect->query("SET NAMES utf8");

    $q = "SELECT c.descripcion AS Concepto, 
    TRIM(REPLACE(GROUP_CONCAT(IF(i.estatus = 'A' AND i.monto is NOT null,CONCAT('$ ',i.monto),'-') SEPARATOR ', '),'-,','')) AS Montos
FROM concepto c
LEFT JOIN importe i  ON c.idConcepto = i.idConcepto
GROUP BY c.idConcepto
ORDER BY c.idConcepto";
    $eq = $taxiConect->query($q);

    $aux = array();
    if ($eq->num_rows != 0) {
        while ($row = $eq->fetch_array(MYSQLI_ASSOC)) {
            array_push($aux, $row);
        }
    } else {
        $aux = array(0);
    }

    $taxiConect->close();
} else {
    $aux = array(0);
}

echo json_encode($aux);
