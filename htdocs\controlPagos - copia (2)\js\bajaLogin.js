$(document).ready(function () {
  if ($.get("res")) {
    Rsp = $.get("res");
    switch (Rsp) {
      case "0":
        rmsg = "Error en el proceso";
        break;
      case "1":
        rmsg = "Baja realizada correctamente";
        break;
    }
    alertify.alert("Servitaxis Sitio 152", rmsg);
  }
  $("#formBaja").validate();

  $("#divBtnBaja,#divUser").hide();

  $("#empleado")
    .miCatalogo("userlist", "Error en  catalogo de empleados")
    .change(function (e) {
      e.preventDefault();
      const user = $(this).val();
      $.ajax({
        type: "post",
        url: "php/ajax/empleadoUserList.php",
        data: { idUser: user },
      })
        .done(function (r) {
          R = $.parseJSON(r);
          if (R[0] != 0) {
            $("#usuario").empty();
            let userAppend = `<option value="">Selecione una opción ...</option>`;
            R.forEach(function (param) {
              let { tipoUsuario, userName } = param;
              userAppend = `${userAppend}<option value="${tipoUsuario}">${userName}</option>`;
            });
            $("#usuario").append(userAppend);
            $("#divBtnBaja,#divUser").show();
          } else {
            alertify.alert(
              "Servitaxis Sitio 152",
              "El empleado no tiene usuarios activos"
            );
            $("#empleado").val("")
            $("#divBtnBaja,#divUser").hide();
          }
        })
        .fail(function () {
            alertify.alert(
                "Servitaxis Sitio 152",
                "Error al cargar Usuarios"
              );
        });
    });
});
