-- =====================================================
-- AGREGAR CAMPO ESTADO A TABLA DE ACUERDOS
-- Base de datos: minutas_db
-- =====================================================

USE minutas_db;

-- Agregar campo estado a la tabla minuta_acuerdos
ALTER TABLE minuta_acuerdos
ADD COLUMN estado ENUM('vigente', 'vencido', 'completado') DEFAULT 'vigente'
COMMENT 'Estado del acuerdo: vigente, vencido o completado';

-- Crear índice para el campo estado
ALTER TABLE minuta_acuerdos ADD INDEX idx_estado (estado);

-- Actualizar acuerdos existentes a estado vigente
UPDATE minuta_acuerdos SET estado = 'vigente' WHERE estado IS NULL;

-- Mostrar estructura actualizada
DESCRIBE minuta_acuerdos;
SELECT 'Campo estado agregado exitosamente a minuta_acuerdos' as mensaje;
