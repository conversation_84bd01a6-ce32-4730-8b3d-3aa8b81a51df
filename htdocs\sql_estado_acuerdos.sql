-- =====================================================
-- AGREGAR CAMPO ESTADO A TABLA DE ACUERDOS
-- Base de datos: minutas_db
-- =====================================================

USE minutas_db;

-- Agregar campo estado a la tabla minuta_acuerdos
ALTER TABLE minuta_acuerdos 
ADD COLUMN estado ENUM('pendiente', 'completado') DEFAULT 'pendiente' 
COMMENT 'Estado del acuerdo: pendiente o completado';

-- Crear índice para el campo estado
ALTER TABLE minuta_acuerdos ADD INDEX idx_estado (estado);

-- Actualizar acuerdos existentes a estado pendiente
UPDATE minuta_acuerdos SET estado = 'pendiente' WHERE estado IS NULL;

-- Mostrar estructura actualizada
DESCRIBE minuta_acuerdos;
SELECT 'Campo estado agregado exitosamente a minuta_acuerdos' as mensaje;
