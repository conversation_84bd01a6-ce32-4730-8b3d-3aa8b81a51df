<?php
class foo_mysqli extends mysqli
{
	public function __construct($host, $usuario, $contraseña, $bd)
	{
		parent::__construct($host, $usuario, $contraseña, $bd);

		if (mysqli_connect_error()) {
			die('Error de Conexión (' . mysqli_connect_errno() . ') ' . mysqli_connect_error());
		}
	}

	public function iniciaTransaccion()
	{
		$this->query("SET NAMES utf8");
		$this->autocommit(FALSE);

		$aux = 1; //si cambia a 0 se hara rollback

		return $aux;
	}

	public function finTransaccion($aux, $next, $back)
	{
		if ($aux === 1) {
			$this->commit();
			echo "guardo";
			$link = $next;
		} else {
			$this->rollback();
			echo "error";
			$link = $back;
		}

		return ($link);
	}

	public function resetTabla($aux, $idTabla, $tabla)
	{
		if ($aux == 0) {
			$qMaxID = "SELECT MAX($idTabla) AS maxID FROM $tabla";
			$eQMaxID = $this->query($qMaxID);
			$resQMaxID = $eQMaxID->fetch_array(MYSQLI_ASSOC);
			$idMaximo = $resQMaxID['maxID'];
			$idMaximo = $idMaximo + 1;

			$qAutoIncrement = "ALTER TABLE $tabla AUTO_INCREMENT=$idMaximo";
			$this->query($qAutoIncrement);
		} else {
			echo "Ok";
		}
	}
}
