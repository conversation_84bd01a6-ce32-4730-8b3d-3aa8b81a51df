-- =====================================================
-- TABLA DE AUTOGUARDADO PARA SISTEMA DE MINUTAS
-- Base de datos: minutas_db
-- =====================================================

USE minutas_db;

-- Agregar campo es_autoguardado a la tabla minutas existente
ALTER TABLE minutas
ADD COLUMN es_autoguardado TINYINT(1) DEFAULT 0 COMMENT 'Indica si la minuta fue autoguardada (1) o guardada manualmente (0)';

-- Crear índice para el campo es_autoguardado
ALTER TABLE minutas ADD INDEX idx_es_autoguardado (es_autoguardado);

-- Procedimiento para limpiar minutas autoguardadas antiguas (más de 24 horas)
DELIMITER //
CREATE PROCEDURE LimpiarMinutasAutoguardadasAntiguas()
BEGIN
    DELETE FROM minutas
    WHERE es_autoguardado = 1
    AND fecha_actualizacion < DATE_SUB(NOW(), INTERVAL 24 HOUR);

    SELECT ROW_COUNT() as registros_eliminados;
END //
DELIMITER ;

-- Mostrar estructura actualizada
DESCRIBE minutas;
SELECT 'Campo es_autoguardado agregado exitosamente' as mensaje;
