$(document).ready(function () {
  for (i = 1; i < 54; i++) {
    $("#semana").append(`<option value="${i}">Semana ${i}</option>`);
  }
  $("#anio").miCatalogo("yearabono", "Error en  catalogo de años");
  $("#concepto").miCatalogo("concepto", "Error en  catalogo de concepto");
  $("#formSemanal").validate({
    submitHandler: function (form) {
      const idConceto = $("#concepto").val();
      const conceptoText = $("#concepto option:selected").text();
      $("#concepto option:selected").remove();
      $("#concepto").val("");
      $("#headerList").children().last().remove();
      $("#headerList").append(`<th>${conceptoText}</th>`);
      $("#headerList").append(`<th>Total</th>`);
      $("#conceptosList").append(
        `<input type="hidden" name="concepto[]" value="${idConceto}">`
      );
      $("#genReporte").show();
    },
  });
  $("#genReporte").hide();
  $("#genReporte").validate();
});
