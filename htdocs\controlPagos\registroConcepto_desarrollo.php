<?php include_once('php/session.php'); ?>
<!DOCTYPE html>
<html>

<head>
    <meta charset="UTF-8">

    <?php include_once("includes/head.html"); ?>

    <script src="js/registroConcepto.js"></script>

</head>

<body class="bg-light">
    <div class="container">
        <br>
        <div id="header"></div>

        <br>
        <?php include_once("includes/menu.html"); ?>

        <br>
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="card border-primary mb-3" style="max-width: 100%;">
                    <div class="card-header bg-transparent border-primary">
                        <h5 class="card-title">Registro de conceptos</h5>
                        <form id="busqueda" autocomplete="off">
                            <div class="row justify-content-center">
                                <div class="col-lg-6">
                                    <div class="input-group mb-3">
                                        <input type="text" class="form-control" id="buscadorOp" name="buscadorOp" required placeholder="Buscador de Operador" aria-label="Buscador de Operador" aria-describedby="button-addon2">
                                        <button class="btn btn-outline-primary">Buscar</button>
                                    </div>
                                    <label id="buscadorOp-error" class="error" for="buscadorOp"></label>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="card-body">
                        <form id="conceptoAgrega">
                            <div id="divAgregaConcepto">
                                <div class="row justify-content-between">
                                    <div class="col-lg-5">
                                        <h5>Operador</h5>
                                        <h4 id="operadorName"></h4>
                                        <input type="hidden" name="idOperador" id="idOperador">
                                    </div>
                                    <div class="col-lg-5 text-end">
                                        <h5>Contable</h5>
                                        <h4 id="contableNum"></h4>
                                    </div>
                                </div>
                                <div class="row justify-content-center">
                                    <div class="col-lg-5 text-center">
                                        <h5 id="semanaActual"></h5>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-lg-3">
                                        <div class="form-group">
                                            <label for="concepto">
                                                <h5>Conceptos</h5>
                                            </label>
                                            <select class="form-control" name="concepto" id="concepto">
                                                <option value="">Elegir concepto ...</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-lg-3" id="importeDiv">
                                        <label for="importe">
                                            <h5>Importe</h5>
                                        </label>
                                        <div class="input-group mb-3">
                                            <span class="input-group-text">$</span>
                                            <input type="number" step="0.01" min="0" class="form-control" name="importe" id="importe" placeholder="Ingrese el importe" required>
                                        </div>
                                    </div>
                                    <div class="col-lg-2" id="semanaDiv">
                                        <label for="semana">
                                            <h5>Semana</span></h5>
                                        </label>
                                        <input type="number" min="1" max="53" class="form-control" name="semana" id="semana" required>
                                    </div>
                                    <div class="col-lg-2" id="anioDiv">
                                        <label for="anio">
                                            <h5>Año</h5>
                                        </label>
                                        <input type="number" class="form-control" name="anio" id="anio" required>
                                    </div>
                                    <div class="col-lg-2" id="agregaDiv">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="flexCondonacion" value="S">
                                            <label class="form-check-label" for="flexCondonacion">Condonación</label>
                                        </div>
                                        <div class="mt-auto p-2 bd-highlight">
                                            <div class="d-grid gap-2">
                                                <button class="btn btn-primary" name="agregar" id="agregar">Agregar</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row justify-content-center">
                                    <div class="col-lg-8 text-center">
                                        <h5 id="ultimaSemana"></h5>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="card-footer bg-transparent border-primary">
                        <form method="POST" action="php/saveRecibo.php" id="reciboConceptos" autocomplete="off">
                            <input type="hidden" id="operadorID" name="operadorID">
                            <table class="table table-sm" id="listaConceptos">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>Concepto</th>
                                        <th>Importe</th>
                                        <th>Semana</th>
                                        <th>Año</th>
                                        <th>Condonado</th>
                                    </tr>
                                </thead>
                                <tbody>

                                </tbody>
                            </table>
                            <div class="row justify-content-center">
                                <div class="col-lg-4">
                                    <div class="form-floating">
                                        <textarea class="form-control" rows="5" style="resize: none; height: 100px" maxlength="200" placeholder="Escriba las observaciones requeridas" name="observaciones" id="observaciones"></textarea>
                                        <label for="observaciones">Observaciones</label>
                                    </div>
                                </div>
                            </div>
                            <div class="mt-4 row justify-content-center">
                                <div class="col-lg-2 text-end">
                                    <h4>Total:</h4>
                                </div>
                                <div class="col-lg-2">
                                    <h4 id="totalRecibo"></h4>
                                </div>
                            </div>
                            <input type="hidden" name="depositoFlag" id="depositoFlag" value="0">
                            <div class="row justify-content-center" id="divFechaDeposito">
                                <div class="col-lg-3">
                                    <label for="fechaDeposito">
                                        <h5>Fecha de Deposito</h5>
                                    </label>
                                    <input class="form-control" name="fechaDeposito" id="fechaDeposito" required>
                                </div>
                                <div class="col-lg-3">
                                    <label for="movimientoNo">
                                        <h5>Número de movimiento</h5>
                                    </label>
                                    <input type="number" class="form-control" maxlength="7" name="movimientoNo" id="movimientoNo" required>
                                </div>
                                <div class="col-lg-3">
                                    <label for="cuenta">
                                        <h5>Cuenta de deposito:</h5>
                                    </label>
                                    <select name="cuenta" id="cuenta" class="form-control" required>
                                        <option value="">Selecione una opción...</option>
                                    </select>
                                </div>
                            </div>
                            <div class="row justify-content-center">
                                <div class="col-lg-4">
                                    <div class="mt-auto p-2 bd-highlight">
                                        <div class="d-grid gap-2">
                                            <button class="btn btn-lg btn-success">Guardar</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        <div id="footer"></div>
    </div>
</body>

</html>