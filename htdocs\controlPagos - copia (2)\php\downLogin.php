<?php
include_once("conexion.php");
include_once("configDB.php");
include_once("session.php");
include_once("classUsuario.php");

$back = $_SERVER['HTTP_REFERER'];

if (isset($_POST['admin'])) {
    $usuario = $_POST['empleado'];
    $tipoUser = $_POST['usuario'];

    $taxiConect = new foo_mysqli("localhost", USER, PASS, DBNAME);
    $taxiConect->query("SET NAMES utf8");

    $baja = new usuario("", "", "", "", "", "", $tipoUser, "I", "");
    $baja->identificador = $usuario;
    if($baja->bajaLogin($taxiConect)){
        $aux = 1;
    }
    else{
        $aux = 0;
    }

    $taxiConect->close();
}
else{
    $aux = 0;
}

header("Location: $back?res=$aux");
