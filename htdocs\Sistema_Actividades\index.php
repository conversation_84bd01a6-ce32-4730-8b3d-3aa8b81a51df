<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sistema de Actividades</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <!-- Flatpickr para fechas -->
    <link href="https://cdn.jsdelivr.net/npm/flatpickr@4.6.13/dist/flatpickr.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/flatpickr@4.6.13/dist/flatpickr.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/flatpickr@4.6.13/dist/l10n/es.js"></script>

    <!-- React -->
    <script crossorigin src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script crossorigin src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>

    <!-- Librerías para exportación -->
    <script src="https://unpkg.com/xlsx@0.18.5/dist/xlsx.full.min.js"></script>
    <script src="https://unpkg.com/jspdf@2.5.1/dist/jspdf.umd.min.js"></script>
    <script src="https://unpkg.com/jspdf-autotable@3.5.31/dist/jspdf.plugin.autotable.min.js"></script>

    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --info-color: #17a2b8;
            --light-color: #f8f9fa;
            --dark-color: #343a40;
        }

        body {
            background-color: #f5f6fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .navbar-brand {
            font-weight: bold;
            color: white !important;
        }

        .nav-tabs .nav-link {
            border: none;
            border-radius: 8px 8px 0 0;
            margin-right: 5px;
            background-color: #e9ecef;
            color: #495057;
            font-weight: 500;
        }

        .nav-tabs .nav-link.active {
            background-color: var(--primary-color);
            color: white;
            border: none;
        }

        .card {
            border: none;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .card-header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            border-radius: 12px 12px 0 0 !important;
            border: none;
            font-weight: 600;
        }

        .form-control, .form-select {
            border-radius: 8px;
            border: 1px solid #dee2e6;
            padding: 10px 15px;
        }

        .form-control:focus, .form-select:focus {
            border-color: var(--secondary-color);
            box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
        }

        .btn {
            border-radius: 8px;
            padding: 10px 20px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }

        .table-responsive {
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow-x: auto;
            background: white;
        }

        .table {
            margin-bottom: 0;
            min-width: 1200px;
        }

        .table th {
            background-color: var(--dark-color);
            color: white;
            border: none;
            font-weight: 600;
            padding: 15px 10px;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .table td {
            padding: 12px 10px;
            vertical-align: middle;
            border-bottom: 1px solid #dee2e6;
        }

        .table tbody tr:hover {
            background-color: #f8f9fa;
        }

        .badge {
            font-size: 0.75rem;
            padding: 6px 12px;
            border-radius: 20px;
        }

        .badge-prioridad-alta {
            background-color: var(--danger-color);
            color: white;
        }

        .badge-prioridad-media {
            background-color: var(--warning-color);
            color: white;
        }

        .badge-prioridad-baja {
            background-color: var(--info-color);
            color: white;
        }

        .badge-progreso-vigente {
            background-color: var(--success-color);
            color: white;
        }

        .badge-progreso-progreso {
            background-color: var(--secondary-color);
            color: white;
        }

        .badge-progreso-vencido {
            background-color: var(--danger-color);
            color: white;
        }

        .badge-progreso-completado {
            background-color: var(--dark-color);
            color: white;
        }

        .alert {
            border-radius: 8px;
            border: none;
        }

        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid var(--secondary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .text-truncate {
            max-width: 200px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .export-buttons {
            background: var(--light-color);
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #dee2e6;
            margin-bottom: 20px;
        }

        .filters-container {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .stats-number {
            font-size: 2rem;
            font-weight: bold;
        }

        .col-numero { width: 60px; min-width: 60px; }
        .col-id { width: 140px; min-width: 120px; }
        .col-tarea { width: 300px; min-width: 250px; }
        .col-tema { width: 150px; min-width: 120px; }
        .col-prioridad { width: 100px; min-width: 80px; }
        .col-fecha { width: 160px; min-width: 140px; }
        .col-responsable { width: 180px; min-width: 150px; }
        .col-progreso { width: 120px; min-width: 100px; }
        .col-comentarios { width: 200px; min-width: 150px; }
        .col-acciones { width: 100px; min-width: 80px; }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg" style="background-color: var(--primary-color);">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="fas fa-tasks me-2"></i>
                Sistema de Actividades
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text text-white">
                    <i class="fas fa-calendar-alt me-1"></i>
                    <span id="current-date"></span>
                </span>
            </div>
        </div>
    </nav>

    <!-- Contenedor principal -->
    <div class="container-fluid mt-4">
        <div id="app"></div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>

    <script type="text/babel">
        const { useState, useEffect } = React;

        // Usuarios predefinidos (importados del sistema de minutas)
        const predefinedUsers = [
            //Dirección
            { nombre: 'Agustín Díaz Lastra', area: 'Dirección General', extensionCorreo: '<EMAIL>' },
            { nombre: 'Juana Elizabeth Castro González', area: 'Dirección General', extensionCorreo: '<EMAIL>' },
            { nombre: 'Elizabeth Luna Reyna', area: 'Dirección General', extensionCorreo: '<EMAIL>' },
            { nombre: 'Lidia Michelett López Roque', area: 'Dirección General', extensionCorreo: '<EMAIL>' },
            { nombre: 'Tomas Alberto Alamina Aguilar', area: 'Dirección General', extensionCorreo: '<EMAIL>' },
            { nombre: 'Fernanda Vazquez Posada', area: 'Dirección General', extensionCorreo: '@ruv.org.mx' },
            { nombre: 'Juan Carlos Leal Barcenas', area: 'Dirección General', extensionCorreo: '@ruv.org.mx' },
            { nombre: 'Reyna Catalina Nevarez Rascon', area: 'Contraloría', extensionCorreo: '<EMAIL>' },
            { nombre: 'Mario Rafael Reyna Mandujano', area: 'Dirección General', extensionCorreo: '<EMAIL>' },
            { nombre: 'Juan Manuel Rodriguez Gomez', area: 'Contraloría', extensionCorreo: '<EMAIL>' },
            { nombre: 'Brenda Maniau Mancila', area: 'Enlace Especializado de Coordinación y Comunicación', extensionCorreo: '<EMAIL>' },

            //Desarrollo
            { nombre: 'Maria Elena López', area: 'Gerente de Desarrollo', extensionCorreo: '<EMAIL>' },
            { nombre: 'Norberto Rojas Nava', area: 'Consultor de Aplicaciones Móviles y Portales', extensionCorreo: '<EMAIL>' },
            { nombre: 'Adrian Alberto Casas Lopez', area: 'Consultor de Desarrollo y Mantenimiento de Sistemas', extensionCorreo: '<EMAIL>' },
            { nombre: 'Edgar Oscar Gómez Lara', area: 'Consultor y Planificador de Recursos Empresariales', extensionCorreo: '<EMAIL>' },
            { nombre: 'Mayeli Ayerim González Hernández', area: 'Consultor de Calidad', extensionCorreo: '<EMAIL>' },
            { nombre: 'Azael Antonio Sandoval', area: 'Ejecutivo Desarrollador de Sistemas', extensionCorreo: '<EMAIL>' },
            { nombre: 'Raúl Hernández Torres', area: 'Ejecutivo de gestión ERP', extensionCorreo: '<EMAIL>' }
        ];

        // Prioridades disponibles
        const prioridades = ['Alta', 'Media', 'Baja'];

        // Estados de progreso disponibles
        const estadosProgreso = ['Vigente', 'En Progreso', 'Vencido', 'Completado'];

        // Funciones de utilidad
        const formatDate = (dateString) => {
            if (!dateString) return '';
            const date = new Date(dateString);
            return date.toLocaleDateString('es-ES', {
                day: '2-digit',
                month: '2-digit',
                year: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });
        };

        const formatDateForInput = (dateString) => {
            if (!dateString) return '';
            const date = new Date(dateString);
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            const hours = String(date.getHours()).padStart(2, '0');
            const minutes = String(date.getMinutes()).padStart(2, '0');
            return `${day}/${month}/${year} ${hours}:${minutes}`;
        };

        const getCurrentDateTime = () => {
            const now = new Date();
            const year = now.getFullYear();
            const month = String(now.getMonth() + 1).padStart(2, '0');
            const day = String(now.getDate()).padStart(2, '0');
            const hours = String(now.getHours()).padStart(2, '0');
            const minutes = String(now.getMinutes()).padStart(2, '0');
            return `${day}/${month}/${year} ${hours}:${minutes}`;
        };

        // Funciones de API
        const apiCall = async (endpoint, options = {}) => {
            try {
                const response = await fetch(`api/actividades.php${endpoint}`, {
                    headers: {
                        'Content-Type': 'application/json',
                        ...options.headers
                    },
                    ...options
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();
                return data;
            } catch (error) {
                console.error('API Error:', error);
                throw error;
            }
        };

        const crearActividad = async (actividadData) => {
            return await apiCall('', {
                method: 'POST',
                body: JSON.stringify(actividadData)
            });
        };

        const obtenerActividades = async (filtros = {}) => {
            const params = new URLSearchParams({
                action: 'list',
                ...filtros
            });
            return await apiCall(`?${params}`);
        };

        const obtenerActividad = async (id) => {
            return await apiCall(`?id=${id}`);
        };

        const actualizarActividad = async (actividadData) => {
            return await apiCall('', {
                method: 'PUT',
                body: JSON.stringify(actividadData)
            });
        };

        const obtenerTemas = async () => {
            return await apiCall('?action=temas');
        };

        const generarNuevoId = async () => {
            return await apiCall('?action=generate_id');
        };

        // Componente principal de la aplicación
        const SistemaActividades = () => {
            // Estados principales
            const [activeTab, setActiveTab] = useState('nueva');
            const [isLoading, setIsLoading] = useState(false);
            const [message, setMessage] = useState({ type: '', text: '' });
            const [temas, setTemas] = useState([]);
            const [temasDetallados, setTemasDetallados] = useState([]);
            const [actividades, setActividades] = useState([]);
            const [editingActividad, setEditingActividad] = useState(null);
            const [isEditing, setIsEditing] = useState(false);

            // Estados para gestión de temas
            const [nuevoTema, setNuevoTema] = useState({
                nombre: '',
                descripcion: '',
                color: '#3498db',
                icono: 'fas fa-tag'
            });

            // Estados del formulario
            const [formData, setFormData] = useState({
                actividad_id: '',
                tarea: '',
                tema: '',
                prioridad: 'Media',
                fechaHoraCompromiso: '',
                responsable: '',
                progreso: 'Vigente',
                comentarios: ''
            });

            // Estados de filtros
            const [filtros, setFiltros] = useState({
                tema: '',
                prioridad: '',
                responsable: '',
                progreso: ''
            });

            // Cargar datos iniciales
            useEffect(() => {
                cargarTemas();
                cargarActividades();
                actualizarFechaActual();
            }, []);

            // Actualizar fecha actual cada minuto
            useEffect(() => {
                const interval = setInterval(actualizarFechaActual, 60000);
                return () => clearInterval(interval);
            }, []);

            const actualizarFechaActual = () => {
                const now = new Date();
                const fechaFormateada = now.toLocaleDateString('es-ES', {
                    weekday: 'long',
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                });
                const elemento = document.getElementById('current-date');
                if (elemento) {
                    elemento.textContent = fechaFormateada;
                }
            };

            const cargarTemas = async () => {
                try {
                    const response = await obtenerTemas();
                    if (response.success) {
                        setTemas(response.data);
                    }
                } catch (error) {
                    console.error('Error cargando temas:', error);
                }
            };

            const cargarTemasDetallados = async () => {
                try {
                    const response = await apiCall('?action=temas_detallados');
                    if (response.success) {
                        setTemasDetallados(response.data);
                    }
                } catch (error) {
                    console.error('Error cargando temas detallados:', error);
                }
            };

            const crearNuevoTema = async () => {
                if (!nuevoTema.nombre.trim()) {
                    setMessage({ type: 'error', text: 'El nombre del tema es requerido' });
                    return;
                }

                try {
                    setIsLoading(true);
                    const params = new URLSearchParams({
                        action: 'crear_tema',
                        nombre: nuevoTema.nombre,
                        descripcion: nuevoTema.descripcion,
                        color: nuevoTema.color,
                        icono: nuevoTema.icono
                    });

                    const response = await apiCall(`?${params}`);
                    if (response.success) {
                        setMessage({ type: 'success', text: 'Tema creado exitosamente' });
                        setNuevoTema({ nombre: '', descripcion: '', color: '#3498db', icono: 'fas fa-tag' });
                        cargarTemas();
                        cargarTemasDetallados();
                    } else {
                        setMessage({ type: 'error', text: response.error || 'Error al crear el tema' });
                    }
                } catch (error) {
                    setMessage({ type: 'error', text: 'Error de conexión' });
                } finally {
                    setIsLoading(false);
                }
            };

            const cargarActividades = async () => {
                try {
                    setIsLoading(true);
                    const response = await obtenerActividades(filtros);
                    if (response.success) {
                        setActividades(response.data);
                    }
                } catch (error) {
                    console.error('Error cargando actividades:', error);
                    setMessage({ type: 'error', text: 'Error al cargar las actividades' });
                } finally {
                    setIsLoading(false);
                }
            };

            // Recargar actividades cuando cambien los filtros
            useEffect(() => {
                if (activeTab === 'consultar') {
                    cargarActividades();
                } else if (activeTab === 'temas') {
                    cargarTemasDetallados();
                }
            }, [filtros, activeTab]);

            const resetForm = () => {
                setFormData({
                    actividad_id: '',
                    tarea: '',
                    tema: '',
                    prioridad: 'Media',
                    fechaHoraCompromiso: '',
                    responsable: '',
                    progreso: 'Vigente',
                    comentarios: ''
                });
                setIsEditing(false);
                setEditingActividad(null);
            };

            const handleInputChange = (e) => {
                const { name, value } = e.target;
                setFormData(prev => ({
                    ...prev,
                    [name]: value
                }));
            };

            const handleTextareaChange = (name) => (e) => {
                const value = e.target.value;
                setFormData(prev => ({
                    ...prev,
                    [name]: value
                }));
            };

            // Inicializar Flatpickr para fecha y hora
            useEffect(() => {
                const fechaInput = document.querySelector('input[name="fechaHoraCompromiso"]');
                if (fechaInput && !fechaInput._flatpickr) {
                    flatpickr(fechaInput, {
                        enableTime: true,
                        dateFormat: "d/m/Y H:i",
                        time_24hr: true,
                        locale: "es",
                        minDate: "today",
                        defaultHour: 9,
                        defaultMinute: 0,
                        onChange: function(selectedDates, dateStr) {
                            setFormData(prev => ({
                                ...prev,
                                fechaHoraCompromiso: dateStr
                            }));
                        }
                    });
                }
            }, [activeTab]);

            // Componente de navegación por pestañas
            const TabNavigation = () => {
                return React.createElement(
                    'ul',
                    { className: 'nav nav-tabs mb-4' },
                    [
                        { id: 'nueva', label: isEditing ? `✏️ Editando: ${editingActividad?.actividad_id}` : '📝 Nueva Actividad' },
                        { id: 'consultar', label: '📋 Consultar Actividades' },
                        { id: 'temas', label: '🏷️ Gestionar Temas' }
                    ].map(tab => React.createElement(
                        'li',
                        { key: tab.id, className: 'nav-item' },
                        React.createElement(
                            'button',
                            {
                                className: `nav-link ${activeTab === tab.id ? 'active' : ''}`,
                                onClick: () => {
                                    setActiveTab(tab.id);
                                    setMessage({ type: '', text: '' });
                                    if (tab.id === 'nueva' && !isEditing) {
                                        resetForm();
                                    }
                                }
                            },
                            tab.label
                        )
                    ))
                );
            };

            // Componente de mensajes
            const MessageAlert = () => {
                if (!message.text) return null;

                return React.createElement(
                    'div',
                    {
                        className: `alert ${message.type === 'success' ? 'alert-success' : 'alert-danger'} alert-dismissible fade show`,
                        role: 'alert'
                    },
                    message.text,
                    React.createElement(
                        'button',
                        {
                            type: 'button',
                            className: 'btn-close',
                            onClick: () => setMessage({ type: '', text: '' })
                        }
                    )
                );
            };

            // Componente de carga
            const LoadingSpinner = () => {
                if (!isLoading) return null;

                return React.createElement(
                    'div',
                    { className: 'text-center py-4' },
                    React.createElement(
                        'div',
                        { className: 'loading-spinner mx-auto' }
                    ),
                    React.createElement(
                        'p',
                        { className: 'mt-2 text-muted' },
                        'Cargando...'
                    )
                );
            };

            // Componente del formulario de nueva actividad
            const FormularioActividad = () => {
                const handleSubmit = async (e) => {
                    e.preventDefault();

                    // Validaciones básicas
                    if (!formData.tarea.trim()) {
                        setMessage({ type: 'error', text: 'La tarea es requerida' });
                        return;
                    }

                    if (!formData.tema) {
                        setMessage({ type: 'error', text: 'El tema es requerido' });
                        return;
                    }

                    if (!formData.fechaHoraCompromiso) {
                        setMessage({ type: 'error', text: 'La fecha y hora de compromiso es requerida' });
                        return;
                    }

                    if (!formData.responsable) {
                        setMessage({ type: 'error', text: 'El responsable es requerido' });
                        return;
                    }

                    try {
                        setIsLoading(true);

                        if (isEditing) {
                            // Actualizar actividad existente
                            const response = await actualizarActividad(formData);
                            if (response.success) {
                                setMessage({ type: 'success', text: `Actividad ${formData.actividad_id} actualizada exitosamente` });
                                cargarActividades();
                                resetForm();
                                setActiveTab('consultar');
                            } else {
                                setMessage({ type: 'error', text: response.error || 'Error al actualizar la actividad' });
                            }
                        } else {
                            // Crear nueva actividad
                            const response = await crearActividad(formData);
                            if (response.success) {
                                setMessage({ type: 'success', text: `Actividad ${response.actividad_id} creada exitosamente` });
                                resetForm();
                            } else {
                                setMessage({ type: 'error', text: response.error || 'Error al crear la actividad' });
                            }
                        }
                    } catch (error) {
                        setMessage({ type: 'error', text: 'Error de conexión. Intente nuevamente.' });
                    } finally {
                        setIsLoading(false);
                    }
                };

                return React.createElement(
                    'div',
                    { className: 'card' },
                    React.createElement(
                        'div',
                        { className: 'card-header' },
                        React.createElement(
                            'h5',
                            { className: 'mb-0' },
                            isEditing ? `✏️ Editar Actividad: ${formData.actividad_id}` : '📝 Nueva Actividad'
                        )
                    ),
                    React.createElement(
                        'div',
                        { className: 'card-body' },
                        React.createElement(
                            'form',
                            { onSubmit: handleSubmit },
                            // Primera fila: Tarea
                            React.createElement(
                                'div',
                                { className: 'mb-3' },
                                React.createElement(
                                    'label',
                                    { className: 'form-label fw-bold' },
                                    '📝 Tarea *'
                                ),
                                React.createElement(
                                    'textarea',
                                    {
                                        className: 'form-control',
                                        name: 'tarea',
                                        value: formData.tarea,
                                        onChange: handleTextareaChange('tarea'),
                                        placeholder: 'Describe la tarea a realizar...',
                                        rows: 3,
                                        required: true
                                    }
                                )
                            ),
                            // Segunda fila: Tema y Prioridad
                            React.createElement(
                                'div',
                                { className: 'row mb-3' },
                                React.createElement(
                                    'div',
                                    { className: 'col-md-6' },
                                    React.createElement(
                                        'label',
                                        { className: 'form-label fw-bold' },
                                        '🏷️ Tema *'
                                    ),
                                    React.createElement(
                                        'select',
                                        {
                                            className: 'form-select',
                                            name: 'tema',
                                            value: formData.tema,
                                            onChange: handleInputChange,
                                            required: true
                                        },
                                        React.createElement('option', { value: '' }, 'Seleccione un tema...'),
                                        temas.map(tema => React.createElement(
                                            'option',
                                            { key: tema, value: tema },
                                            tema
                                        ))
                                    )
                                ),
                                React.createElement(
                                    'div',
                                    { className: 'col-md-6' },
                                    React.createElement(
                                        'label',
                                        { className: 'form-label fw-bold' },
                                        '⚡ Prioridad *'
                                    ),
                                    React.createElement(
                                        'select',
                                        {
                                            className: 'form-select',
                                            name: 'prioridad',
                                            value: formData.prioridad,
                                            onChange: handleInputChange,
                                            required: true
                                        },
                                        prioridades.map(prioridad => React.createElement(
                                            'option',
                                            { key: prioridad, value: prioridad },
                                            prioridad
                                        ))
                                    )
                                )
                            ),
                            // Tercera fila: Fecha y Responsable
                            React.createElement(
                                'div',
                                { className: 'row mb-3' },
                                React.createElement(
                                    'div',
                                    { className: 'col-md-6' },
                                    React.createElement(
                                        'label',
                                        { className: 'form-label fw-bold' },
                                        '📅 Fecha y Hora Compromiso *'
                                    ),
                                    React.createElement(
                                        'input',
                                        {
                                            type: 'text',
                                            className: 'form-control',
                                            name: 'fechaHoraCompromiso',
                                            value: formData.fechaHoraCompromiso,
                                            onChange: handleInputChange,
                                            placeholder: 'dd/mm/yyyy hh:mm',
                                            required: true
                                        }
                                    )
                                ),
                                React.createElement(
                                    'div',
                                    { className: 'col-md-6' },
                                    React.createElement(
                                        'label',
                                        { className: 'form-label fw-bold' },
                                        '👤 Responsable *'
                                    ),
                                    React.createElement(
                                        'select',
                                        {
                                            className: 'form-select',
                                            name: 'responsable',
                                            value: formData.responsable,
                                            onChange: handleInputChange,
                                            required: true
                                        },
                                        React.createElement('option', { value: '' }, 'Seleccione un responsable...'),
                                        predefinedUsers.map(user => React.createElement(
                                            'option',
                                            { key: user.nombre, value: user.nombre },
                                            `${user.nombre} - ${user.area}`
                                        ))
                                    )
                                )
                            ),
                            // Cuarta fila: Progreso (solo en edición)
                            isEditing && React.createElement(
                                'div',
                                { className: 'mb-3' },
                                React.createElement(
                                    'label',
                                    { className: 'form-label fw-bold' },
                                    '📊 Progreso'
                                ),
                                React.createElement(
                                    'select',
                                    {
                                        className: 'form-select',
                                        name: 'progreso',
                                        value: formData.progreso,
                                        onChange: handleInputChange
                                    },
                                    estadosProgreso.map(estado => React.createElement(
                                        'option',
                                        { key: estado, value: estado },
                                        estado
                                    ))
                                )
                            ),
                            // Quinta fila: Comentarios
                            React.createElement(
                                'div',
                                { className: 'mb-3' },
                                React.createElement(
                                    'label',
                                    { className: 'form-label fw-bold' },
                                    '💬 Comentarios'
                                ),
                                React.createElement(
                                    'textarea',
                                    {
                                        className: 'form-control',
                                        name: 'comentarios',
                                        value: formData.comentarios,
                                        onChange: handleTextareaChange('comentarios'),
                                        placeholder: 'Comentarios adicionales...',
                                        rows: 3
                                    }
                                )
                            ),
                            // Botones
                            React.createElement(
                                'div',
                                { className: 'd-flex gap-2 justify-content-end' },
                                React.createElement(
                                    'button',
                                    {
                                        type: 'button',
                                        className: 'btn btn-secondary',
                                        onClick: resetForm,
                                        disabled: isLoading
                                    },
                                    '🔄 Limpiar'
                                ),
                                React.createElement(
                                    'button',
                                    {
                                        type: 'submit',
                                        className: `btn ${isEditing ? 'btn-warning' : 'btn-primary'}`,
                                        disabled: isLoading
                                    },
                                    isLoading ?
                                        React.createElement('span', { className: 'loading-spinner me-2' }) :
                                        (isEditing ? '✏️' : '💾'),
                                    ' ',
                                    isEditing ? 'Actualizar Actividad' : 'Guardar Actividad'
                                )
                            )
                        )
                    )
                );
            };

            // Componente de consulta de actividades
            const ConsultaActividades = () => {
                const editarActividad = (actividad) => {
                    setFormData({
                        actividad_id: actividad.actividad_id,
                        tarea: actividad.tarea,
                        tema: actividad.tema,
                        prioridad: actividad.prioridad,
                        fechaHoraCompromiso: formatDateForInput(actividad.fecha_hora_compromiso),
                        responsable: actividad.responsable,
                        progreso: actividad.progreso,
                        comentarios: actividad.comentarios || ''
                    });
                    setEditingActividad(actividad);
                    setIsEditing(true);
                    setActiveTab('nueva');
                };

                const getBadgeClass = (tipo, valor) => {
                    if (tipo === 'prioridad') {
                        switch (valor) {
                            case 'Alta': return 'badge-prioridad-alta';
                            case 'Media': return 'badge-prioridad-media';
                            case 'Baja': return 'badge-prioridad-baja';
                            default: return 'badge-secondary';
                        }
                    } else if (tipo === 'progreso') {
                        switch (valor) {
                            case 'Vigente': return 'badge-progreso-vigente';
                            case 'En Progreso': return 'badge-progreso-progreso';
                            case 'Vencido': return 'badge-progreso-vencido';
                            case 'Completado': return 'badge-progreso-completado';
                            default: return 'badge-secondary';
                        }
                    }
                };

                return React.createElement(
                    'div',
                    null,
                    // Filtros
                    React.createElement(
                        'div',
                        { className: 'filters-container' },
                        React.createElement(
                            'h5',
                            { className: 'mb-3' },
                            '🔍 Filtros de Búsqueda'
                        ),
                        React.createElement(
                            'div',
                            { className: 'row g-3' },
                            React.createElement(
                                'div',
                                { className: 'col-md-3' },
                                React.createElement(
                                    'select',
                                    {
                                        className: 'form-select',
                                        value: filtros.tema,
                                        onChange: (e) => setFiltros({...filtros, tema: e.target.value})
                                    },
                                    React.createElement('option', { value: '' }, 'Todos los temas'),
                                    temas.map(tema => React.createElement('option', { key: tema, value: tema }, tema))
                                )
                            ),
                            React.createElement(
                                'div',
                                { className: 'col-md-3' },
                                React.createElement(
                                    'select',
                                    {
                                        className: 'form-select',
                                        value: filtros.prioridad,
                                        onChange: (e) => setFiltros({...filtros, prioridad: e.target.value})
                                    },
                                    React.createElement('option', { value: '' }, 'Todas las prioridades'),
                                    prioridades.map(prioridad => React.createElement('option', { key: prioridad, value: prioridad }, prioridad))
                                )
                            ),
                            React.createElement(
                                'div',
                                { className: 'col-md-3' },
                                React.createElement(
                                    'select',
                                    {
                                        className: 'form-select',
                                        value: filtros.responsable,
                                        onChange: (e) => setFiltros({...filtros, responsable: e.target.value})
                                    },
                                    React.createElement('option', { value: '' }, 'Todos los responsables'),
                                    predefinedUsers.map(user => React.createElement('option', { key: user.nombre, value: user.nombre }, user.nombre))
                                )
                            ),
                            React.createElement(
                                'div',
                                { className: 'col-md-3' },
                                React.createElement(
                                    'select',
                                    {
                                        className: 'form-select',
                                        value: filtros.progreso,
                                        onChange: (e) => setFiltros({...filtros, progreso: e.target.value})
                                    },
                                    React.createElement('option', { value: '' }, 'Todos los estados'),
                                    estadosProgreso.map(estado => React.createElement('option', { key: estado, value: estado }, estado))
                                )
                            )
                        ),
                        React.createElement(
                            'div',
                            { className: 'mt-3' },
                            React.createElement(
                                'button',
                                {
                                    className: 'btn btn-outline-secondary btn-sm',
                                    onClick: () => setFiltros({ tema: '', prioridad: '', responsable: '', progreso: '' })
                                },
                                '🗑️ Limpiar Filtros'
                            )
                        )
                    ),
                    // Tabla de actividades
                    actividades.length === 0 ? React.createElement(
                        'div',
                        { className: 'text-center py-5' },
                        React.createElement(
                            'h5',
                            { className: 'text-muted' },
                            '📋 No hay actividades registradas'
                        ),
                        React.createElement(
                            'p',
                            { className: 'text-muted' },
                            'Crea tu primera actividad en la pestaña "Nueva Actividad"'
                        )
                    ) : React.createElement(
                        'div',
                        { className: 'table-responsive' },
                        React.createElement(
                            'table',
                            { className: 'table table-striped table-hover' },
                            React.createElement(
                                'thead',
                                null,
                                React.createElement(
                                    'tr',
                                    null,
                                    React.createElement('th', { className: 'col-numero' }, '#'),
                                    React.createElement('th', { className: 'col-id' }, 'ID'),
                                    React.createElement('th', { className: 'col-tarea' }, 'Tarea'),
                                    React.createElement('th', { className: 'col-tema' }, 'Tema'),
                                    React.createElement('th', { className: 'col-prioridad' }, 'Prioridad'),
                                    React.createElement('th', { className: 'col-fecha' }, 'Fecha Compromiso'),
                                    React.createElement('th', { className: 'col-responsable' }, 'Responsable'),
                                    React.createElement('th', { className: 'col-progreso' }, 'Progreso'),
                                    React.createElement('th', { className: 'col-comentarios' }, 'Comentarios'),
                                    React.createElement('th', { className: 'col-acciones' }, 'Acciones')
                                )
                            ),
                            React.createElement(
                                'tbody',
                                null,
                                actividades.map((actividad, index) => React.createElement(
                                    'tr',
                                    { key: actividad.actividad_id },
                                    React.createElement('td', null, index + 1),
                                    React.createElement('td', null, actividad.actividad_id),
                                    React.createElement('td', { className: 'text-truncate', title: actividad.tarea }, actividad.tarea),
                                    React.createElement('td', null, actividad.tema),
                                    React.createElement(
                                        'td',
                                        null,
                                        React.createElement(
                                            'span',
                                            { className: `badge ${getBadgeClass('prioridad', actividad.prioridad)}` },
                                            actividad.prioridad
                                        )
                                    ),
                                    React.createElement('td', null, formatDate(actividad.fecha_hora_compromiso)),
                                    React.createElement('td', null, actividad.responsable),
                                    React.createElement(
                                        'td',
                                        null,
                                        React.createElement(
                                            'span',
                                            { className: `badge ${getBadgeClass('progreso', actividad.estado_calculado || actividad.progreso)}` },
                                            actividad.estado_calculado || actividad.progreso
                                        )
                                    ),
                                    React.createElement('td', { className: 'text-truncate', title: actividad.comentarios }, actividad.comentarios || '-'),
                                    React.createElement(
                                        'td',
                                        null,
                                        React.createElement(
                                            'button',
                                            {
                                                className: 'btn btn-warning btn-sm',
                                                onClick: () => editarActividad(actividad),
                                                title: 'Editar actividad'
                                            },
                                            '✏️'
                                        )
                                    )
                                ))
                            )
                        )
                    )
                );
            };

            // Componente de gestión de temas
            const GestionTemas = () => {
                const iconosDisponibles = [
                    'fas fa-tag', 'fas fa-code', 'fas fa-users', 'fas fa-file-alt',
                    'fas fa-graduation-cap', 'fas fa-tools', 'fas fa-chart-line',
                    'fas fa-bug', 'fas fa-wrench', 'fas fa-search', 'fas fa-briefcase',
                    'fas fa-bullhorn', 'fas fa-ellipsis-h', 'fas fa-star', 'fas fa-heart',
                    'fas fa-cog', 'fas fa-home', 'fas fa-user', 'fas fa-envelope'
                ];

                return React.createElement(
                    'div',
                    null,
                    // Formulario para nuevo tema
                    React.createElement(
                        'div',
                        { className: 'card mb-4' },
                        React.createElement(
                            'div',
                            { className: 'card-header' },
                            React.createElement('h5', { className: 'mb-0' }, '➕ Crear Nuevo Tema')
                        ),
                        React.createElement(
                            'div',
                            { className: 'card-body' },
                            React.createElement(
                                'div',
                                { className: 'row g-3' },
                                React.createElement(
                                    'div',
                                    { className: 'col-md-4' },
                                    React.createElement(
                                        'label',
                                        { className: 'form-label fw-bold' },
                                        'Nombre del Tema *'
                                    ),
                                    React.createElement(
                                        'input',
                                        {
                                            type: 'text',
                                            className: 'form-control',
                                            value: nuevoTema.nombre,
                                            onChange: (e) => setNuevoTema({...nuevoTema, nombre: e.target.value}),
                                            placeholder: 'Ej: Marketing Digital'
                                        }
                                    )
                                ),
                                React.createElement(
                                    'div',
                                    { className: 'col-md-3' },
                                    React.createElement(
                                        'label',
                                        { className: 'form-label fw-bold' },
                                        'Color'
                                    ),
                                    React.createElement(
                                        'input',
                                        {
                                            type: 'color',
                                            className: 'form-control form-control-color',
                                            value: nuevoTema.color,
                                            onChange: (e) => setNuevoTema({...nuevoTema, color: e.target.value})
                                        }
                                    )
                                ),
                                React.createElement(
                                    'div',
                                    { className: 'col-md-3' },
                                    React.createElement(
                                        'label',
                                        { className: 'form-label fw-bold' },
                                        'Icono'
                                    ),
                                    React.createElement(
                                        'select',
                                        {
                                            className: 'form-select',
                                            value: nuevoTema.icono,
                                            onChange: (e) => setNuevoTema({...nuevoTema, icono: e.target.value})
                                        },
                                        iconosDisponibles.map(icono => React.createElement(
                                            'option',
                                            { key: icono, value: icono },
                                            icono
                                        ))
                                    )
                                ),
                                React.createElement(
                                    'div',
                                    { className: 'col-md-2 d-flex align-items-end' },
                                    React.createElement(
                                        'button',
                                        {
                                            className: 'btn btn-primary w-100',
                                            onClick: crearNuevoTema,
                                            disabled: isLoading
                                        },
                                        '➕ Crear'
                                    )
                                )
                            ),
                            React.createElement(
                                'div',
                                { className: 'mt-3' },
                                React.createElement(
                                    'label',
                                    { className: 'form-label fw-bold' },
                                    'Descripción'
                                ),
                                React.createElement(
                                    'textarea',
                                    {
                                        className: 'form-control',
                                        value: nuevoTema.descripcion,
                                        onChange: (e) => setNuevoTema({...nuevoTema, descripcion: e.target.value}),
                                        placeholder: 'Descripción del tema...',
                                        rows: 2
                                    }
                                )
                            )
                        )
                    ),
                    // Lista de temas existentes
                    React.createElement(
                        'div',
                        { className: 'card' },
                        React.createElement(
                            'div',
                            { className: 'card-header' },
                            React.createElement('h5', { className: 'mb-0' }, '🏷️ Temas Existentes')
                        ),
                        React.createElement(
                            'div',
                            { className: 'card-body' },
                            temasDetallados.length === 0 ? React.createElement(
                                'p',
                                { className: 'text-muted text-center' },
                                'Cargando temas...'
                            ) : React.createElement(
                                'div',
                                { className: 'row g-3' },
                                temasDetallados.map(tema => React.createElement(
                                    'div',
                                    { key: tema.id, className: 'col-md-6 col-lg-4' },
                                    React.createElement(
                                        'div',
                                        {
                                            className: 'card h-100',
                                            style: { borderLeft: `4px solid ${tema.color}` }
                                        },
                                        React.createElement(
                                            'div',
                                            { className: 'card-body' },
                                            React.createElement(
                                                'h6',
                                                { className: 'card-title d-flex align-items-center' },
                                                React.createElement('i', { className: `${tema.icono} me-2`, style: { color: tema.color } }),
                                                tema.nombre
                                            ),
                                            tema.descripcion && React.createElement(
                                                'p',
                                                { className: 'card-text small text-muted' },
                                                tema.descripcion
                                            ),
                                            React.createElement(
                                                'div',
                                                { className: 'small text-muted' },
                                                React.createElement('strong', null, 'Actividades: '), tema.total_actividades || 0,
                                                React.createElement('br'),
                                                React.createElement('strong', null, 'Vigentes: '), tema.actividades_vigentes || 0
                                            )
                                        )
                                    )
                                ))
                            )
                        )
                    )
                );
            };

            return React.createElement(
                'div',
                null,
                React.createElement(TabNavigation),
                React.createElement(MessageAlert),
                React.createElement(LoadingSpinner),
                activeTab === 'nueva' ? React.createElement(FormularioActividad) :
                activeTab === 'consultar' ? React.createElement(ConsultaActividades) :
                React.createElement(GestionTemas)
            );
        };

        // Renderizar la aplicación
        const container = document.getElementById('app');
        const root = ReactDOM.createRoot(container);
        root.render(React.createElement(SistemaActividades));

    </script>
</body>
</html>
