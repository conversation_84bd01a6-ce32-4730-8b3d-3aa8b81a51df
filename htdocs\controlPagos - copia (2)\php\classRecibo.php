<?php
class recibo
{

    public $identificador;
    public $formaPago;
    public $estado;
    public $cheque;
    public $fechaPago;
    public $anioMes;

    function __construct()
    {
        $this->estado = 'A';
        $this->anioMes = date('Ym');
    }

    public function guardaRecibo($conexion)
    {
        $this->generaID($conexion);
        $insert = "INSERT INTO recibo(idRecibo, estado) 
        VALUES ('$this->identificador','$this->estado')";
        $rInsert =  $conexion->query($insert);

        return $rInsert;
    }

    public function generaID($conexion)
    {
        $q = "SELECT idRecibo FROM recibo WHERE idRecibo LIKE '$this->anioMes%' ORDER BY idRecibo DESC LIMIT 1;";
        $rq = $conexion->query($q);

        if ($rq->num_rows != 0) {
            $res = $rq->fetch_array(MYSQLI_ASSOC);
            $this->identificador = $res['idRecibo'] + 1;
        } else {
            $this->identificador = $this->anioMes . "0001";
        }
    }

    public function buscaRecibo($conexion, $dato)
    {
        $q = "SELECT r.idRecibo, o.nombreCompleto
            FROM recibo r
            INNER JOIN abono a ON r.idRecibo = a.idRecibo
            INNER JOIN operadorview o ON a.idOperador = o.idOperador
            WHERE r.estado = 'A'
            AND (o.contable = '$dato'
            OR o.nombreCompleto LIKE '$dato')
            GROUP BY r.idRecibo, o.nombreCompleto;";
        $rq = $conexion->query($q);
        if ($rq->num_rows != 0) {
            $res = array();
            while ($row = $rq->fetch_array(MYSQLI_ASSOC)) {
                array_push($res, $row);
            }
        } else {
            $res = array(0);
        }
        return $res;
    }

    public function reciboSinDeposito($conexion, $dato)
    {
        $q = "SELECT r.idRecibo FROM recibo r
            INNER JOIN abono a ON r.idRecibo = a.idRecibo
            INNER JOIN operadorview o ON a.idOperador = o.idOperador
            WHERE r.estado = 'P'
            AND r.fechaDeposito IS NULL
            AND r.movimiento IS NULL
            AND (o.contable = '$dato'
            OR o.nombreCompleto LIKE '$dato')
            GROUP BY r.idRecibo
            HAVING GROUP_CONCAT(a.tipoAbono) NOT LIKE '%V%';";
        $rq = $conexion->query($q);
        if ($rq->num_rows != 0) {
            $res = array();
            while ($row = $rq->fetch_array(MYSQLI_ASSOC)) {
                array_push($res, $row);
            }
        } else {
            $res = array(0);
        }
        return $res;
    }

    public function toTalRecibo($conexion)
    {
        $q = "SELECT Total FROM totalrecibo WHERE idRecibo = '$this->identificador'";
        $eq = $conexion->query($q);
        if ($eq->num_rows != 0) {
            $row = $eq->fetch_array(MYSQLI_ASSOC);
            $total = $row['Total'];
        } else {
            $total = 0;
        }

        return $total;
    }
}
