<?php
include_once("conexion.php");
include_once("configDB.php");
include_once("classProcesaVariable.php");
include_once("classOwner.php");

if (isset($_POST['nombre']) and isset($_POST['correoOwner'])) {

    $nombre = new procesaVariable($_POST['nombre']);
    $aPaterno = new procesaVariable($_POST['aPaterno']);
    $aMaterno = new procesaVariable($_POST['aMaterno']);
    $telefono = new procesaVariable($_POST['telefono']);
    $correo = new procesaVariable($_POST['correoOwner']);
    $tipoPropietario = new procesaVariable($_POST['tipoPropietario']);

    $mailUser = strtolower($correo->valor);

    $taxiConect = new foo_mysqli("localhost", USER, PASS, DBNAME);
    $taxiConect->query("SET NAMES utf8");

    $aux =  $taxiConect->iniciaTransaccion();

    $ownerNew = new owner(
        $nombre->valor,
        $aPaterno->valor,
        $aMaterno->valor,
        $mailUser,
        $telefono->valor,
        $tipoPropietario->valor
    );

    if (!$ownerNew->saveOwner($taxiConect)) {
        $aux = 0;
    }

    $var = $taxiConect->finTransaccion($aux, '1', '0');
    $taxiConect->resetTabla($aux, 'idOwner', 'owner');

    $taxiConect->close();
} else {
    $var = '0';
}

header("Location: ../newOwner.php?resp=$var");
