<?php
include_once("../conexion.php");
include_once("../configDB.php");

$taxiConect = new foo_mysqli("localhost", USER, PASS, DBNAME);
$taxiConect->query("SET NAMES utf8");

if (isset($_POST['idUser'])) {
    $idUser = $_POST['idUser'];
    $qry = "SELECT tipoUsuario, userName FROM `login` WHERE idUsuario = '$idUser' AND estado = 'A'";
    $rqry = $taxiConect->query($qry);

    $aux = array();

    if ($rqry->num_rows != 0) {
        while ($row = $rqry->fetch_array(MYSQLI_ASSOC)) {
            array_push($aux, $row);
        }
    } else {
        $aux = array(0);
    }
}
else{
    $aux = array(0);
}
$taxiConect->close();
echo json_encode($aux);