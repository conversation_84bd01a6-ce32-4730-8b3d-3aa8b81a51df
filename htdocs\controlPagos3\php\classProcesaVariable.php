<?php
class procesaVariable{
	
	public $valor;
	
	function __construct($dato){
		if(isset($dato)){
			$this->valor = $dato;
		}
		else{
			$this->valor = NULL;
		}
	}
	
	public function nulo(){
		$d = (($this->valor == "NULL" || $this->valor == "null" || $this->valor == "" || $this->valor == " ") ? "NULL" : "'".$this->valor."'");
		
		return $d;
	}

	public function procesoBusqueda(){
		$d =str_replace(" ", "%", $this->valor);
		if (!is_numeric($d)) {
			$d = "%$d%";
		}

		return $d;
	}
}
?>