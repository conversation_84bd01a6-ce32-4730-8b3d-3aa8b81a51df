<?php include_once('php/session.php'); ?>
<!DOCTYPE html>
<html>

<head>
    <meta charset="UTF-8">
    <?php include_once("includes/head.html"); ?>
    <script src="js/reporteOperadorFechas.js"></script>

</head>

<body class="bg-light">
    <div class="container">
        <br>
        <div id="header"></div>
        <?php include_once("includes/menu.html"); ?>

        <br>
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="card border-success mb-3" style="max-width: 100%;">
                    <div class="card-header bg-info bg-opacity-25 p-2 text-dark">
                        <h3>Resporte pagos por seleción</h3>
                    </div>
                    <div class="card-body">
                        <form method="POST" id="formSemanal">
                            <div class="row justify-content-center text-center">
                                <h4>Seleccione y agregue el concepto que sera una columna en el reporte</h4>
                            </div>
                            <div class="row mt-4 justify-content-center">
                                <div class="col-lg-4">
                                    <label for="concepto">
                                        <h5>Concepto:</h5>
                                    </label>
                                    <select class="form-control" name="concepto" id="concepto" required>
                                        <option value="">Selecione una opción ...</option>
                                    </select>
                                </div>
                                <div class="col-lg-3">
                                    <div class="d-grid gap-2">
                                        <button name="addConcepto" id="addConcepto" class="btn btn-sm btn-outline-success">&nbsp;<br>Agregar <span class="ui-icon ui-icon-plusthick"></span><br>&nbsp;</button>
                                    </div>
                                </div>
                            </div>
                        </form>
                        <hr>
                        <form action="php/reporteOperadorFechas.php" method="POST" id="genReporte" autocomplete="off">
                            <div class="row justify-content-center text-center" id="conceptosList">
                                <h4>Las siguientes columnas se mostraran en el reporte</h4>
                            </div>
                            <div class="row">
                                <div class="col-lg-12">
                                    <table align="center" class="table table-bordered border-primary">
                                        <thead>
                                            <tr id="headerList">
                                                <th>Semana</th>
                                            </tr>
                                        </thead>
                                    </table>
                                </div>
                            </div>
                            <hr>
                            <div class="row justify-content-center text-center">
                                <h4>Seleccione año y semana del reporte a generar</h4>
                            </div>
                            <div class="row mt-4 justify-content-center">
                                <div class="col-lg-4">
                                    <label for="operador">
                                        <h5>Operador:</h5>
                                    </label>
                                    <select class="form-control" name="operador" id="operador" required>
                                        <option value="">Selecione una opción ...</option>
                                    </select>
                                </div>
                            </div>
                            <div class="row mt-4 justify-content-center">
                                <div class="col-lg-3">
                                    <label for="anio">
                                        <h5>Desde:</h5>
                                    </label>
                                    <input name="beginDate" id="beginDate" required class="form-control" readonly>
                                </div>
                                <div class="col-lg-3">
                                    <label for="semana">
                                        <h5>Hasta:</h5>
                                    </label>
                                    <input name="endDate" id="endDate" required class="form-control" readonly>
                                </div>
                            </div>
                            <div class="row mt-4 justify-content-center">
                                <div class="col-lg-6">
                                    <h5>
                                        <b>NOTA:</b>Este reporte muestra todos los registros guardados (Abonos, Condonaciones, Convenios)
                                        que corresponden al operador selecionado, en caso de requerir unicamente los abonos (Registros
                                        que si pago el operador) habilite selecione el siguiente boton.

                                        En el reporte los registros se identificaran de la siguiente manera:
                                        <ul>
                                            <li>Pago: Número solo</li>
                                            <li>Condonación: Se agrega 1 asterisco (Ej: 500*)</li>
                                            <li>Convenio: Se agregan 2 asterisco (Ej: 500**)</li>
                                            <li>En caso de encontrar un número con 3 asteriscos (Ej: 500***) es porqué esa semana se pago más de una vez.</li>
                                        </ul>
                                    </h5>
                                </div>
                            </div>
                            <div class="row mt-2 justify-content-center">
                                <div class="col-lg-3 text-center">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" name="soloAbonos" id="soloAbonos" value="S">
                                        <label class="form-check-label" for="soloAbonos">
                                            <h5>Solo abonos pagados</h5>
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div class="row justify-content-center mt-4">
                                <div class="col-lg-3">
                                    <input type="hidden" name="tipoReporte" id="tipoReporte" value="1">
                                    <div class="d-grid gap-2">
                                        <button name="btnGenera" id="btnGenera" class="btn btn-lg btn-success">Generar reporte</button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        <div id="footer"></div>
    </div>
</body>

</html>