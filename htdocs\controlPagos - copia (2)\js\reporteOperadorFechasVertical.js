$(document).ready(function () {
  $("#operador").miCatalogo(
    "operadorcontable",
    "Error en  catalogo de operador"
  );
  $("#concepto").miCatalogo("concepto", "Error en  catalogo de concepto");
  $("#genReporte").validate();

  $("#beginDate")
    .datepicker({
      dateFormat: "yy-mm-dd",
      defaultDate: "-2M",
      changeMonth: true,
      changeYear: true,
      numberOfMonths: 2,
    })
    .on("change", function () {
      $("#endDate").datepicker("option", "minDate", $(this).val());
    });
  $("#endDate")
    .datepicker({
      dateFormat: "yy-mm-dd",
      defaultDate: "NOW",
      changeMonth: true,
      changeYear: true,
      numberOfMonths: 2,
    })
    .on("change", function () {
      $("#beginDate").datepicker("option", "maxDate", $(this).val());
    });
});
