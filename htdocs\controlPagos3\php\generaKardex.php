<?php
include_once("conexion.php");
include_once("configDB.php");
include_once("classProcesaVariable.php");
include_once("classAbono.php");
include_once("classCargo.php");
include_once("session.php");
include_once("classReporte.php");
include_once("classOperador.php");

if (isset($_POST['operador']) and isset($_POST['beginDate'])) {

    $operador = new procesaVariable($_POST['operador']);
    $beginDate = new procesaVariable($_POST['beginDate']);
    $endDate = new procesaVariable($_POST['endDate']);

    $taxiConect = new foo_mysqli("localhost", USER, PASS, DBNAME);
    $taxiConect->query("SET NAMES utf8");

    $cargos = new cargo('C', '', '', '', '', '');
    $cargos->operador = $operador->valor;
    $convenios = $cargos->suma($taxiConect, $beginDate->valor);
    $cargos->tipoCargo = 'P';
    $prestamos = $cargos->suma($taxiConect, $beginDate->valor);

    $abonos = new abono();
    $abonos->operador = $operador->valor;
    $pagosConvenio = $abonos->totalXConcepto($taxiConect, '2', $beginDate->valor);
    $pagosPrestamo = $abonos->totalXConcepto($taxiConect, '3', $beginDate->valor);

    $adeudoConvenio = $convenios - $pagosConvenio;
    $adeudoPrestamo = $prestamos - $pagosPrestamo;
    $adeudoTotal = $adeudoConvenio + $adeudoPrestamo;

    $elOperador = new operador('A');
    $elOperador->identificador = $operador->valor;
    $datosOperador = $elOperador->dataXIdentificador($taxiConect);
    $name = $datosOperador['apellidoPaterno'] . " " . $datosOperador['apellidoMaterno'] . " " . $datosOperador['nombre'];
    $economico = $elOperador->economico($taxiConect);

    $header1 = array("CONT " . $datosOperador['contable'], "ECO " . $economico, $name, "", "", "", "", "", "", "");
    $header2 = array(
        "Semana", "", "Inicio de semana (Lunes)", "Fecha de Pago Caseta", "Pago Caseta",
        "Fecha de Pago Convenio", "Pago Convenio", "Saldo Convenio",
        "Fecha de Pago Prestamo", "Pago Prestamo", "Saldo Prestamo",
        "Saldo Total"
    );
    $header3 = array("", "", "", "", "", "", "", "$adeudoConvenio", "", "", "$adeudoPrestamo", "$adeudoTotal");

    $reporte = new Reporte("Kardex", "");
    $reporte->cabeceras = $header1;
    $reporte->otrosHeaders();
    $reporte->cabeceras = $header2;
    $reporte->otrosHeaders();
    $reporte->cabeceras = $header3;
    $reporte->otrosHeaders();

    $timeEnd = strtotime($endDate->valor);
    $endYear = date('Y');
    $endWeek = date('W');
    $i = 0;
    do {
        $linea = array();
        $auxTime = strtotime("$beginDate->valor +$i week");
        $auxWeek = date('W', $auxTime);
        $auxYear = date('Y', $auxTime);
        $auxDay = date('N', $auxTime);
        if ($auxDay == 1) {
            $auxDate = date('Y-m-d', $auxTime);
        } else {
            $time = strtotime("last Monday", $auxTime);
            $auxDate = date('Y-m-d', $time);
        }

        array_push($linea, $auxWeek, "", $auxDate);

        $auxQ = "SELECT	i.idConcepto, r.fechaPago, i.monto 
        FROM abono a 
        INNER JOIN recibo r ON a.idRecibo = r.idRecibo 
        INNER JOIN importe i ON a.idImporte = i.idImporte 
        WHERE a.idOperador = '$operador->valor'
        AND a.semana = '$auxWeek'
        AND a.anio = '$auxYear'
        AND i.idConcepto < 4
        AND r.estado = 'P'
        ORDER BY i.idConcepto ASC;";
        $auxEQ = $taxiConect->query($auxQ);

        if ($auxEQ->num_rows != 0) {
            while ($row = $auxEQ->fetch_array(MYSQLI_ASSOC)) {
                $datePago = date('Y-m-d', strtotime($row['fechaPago']));
                $monto = $row['monto'];
                $idConcepto = $row['idConcepto'];

                switch ($idConcepto) {
                    case 1:
                        array_push($linea, $datePago, $monto);
                        break;
                    case 2:
                        $linea = llenaArray($linea, 5);
                        $adeudoConvenio = $adeudoConvenio - $monto;
                        array_push($linea, $datePago, $monto, $adeudoConvenio);
                        break;
                    case 3:
                        $linea = llenaArray($linea, 8);
                        $adeudoPrestamo = $adeudoPrestamo - $monto;
                        array_push($linea, $datePago, $monto, $adeudoPrestamo);
                        break;
                }
            }
        }
        $linea = llenaArray($linea, 11);
        $linea[7] = $adeudoConvenio;
        $linea[10] = $adeudoPrestamo;
        $adeudoTotal = $adeudoConvenio + $adeudoPrestamo;
        array_push($linea, $adeudoTotal);
        $reporte->datos = $linea;
        $reporte->otraData();

        $i++;
    } while ($auxYear < $endYear or $auxWeek < $endWeek);
    $reporte->closeFile();

    $taxiConect->close();
} else {
    $link = '0';
}

// header("Location: ../registroConcepto.php?resp=$link");

function llenaArray($array, $tlimite)
{
    while (count($array) < $tlimite) {
        array_push($array, "-");
    }

    return $array;
}

function fechaSemana($semana, $year)
{
    $inicio = "$year-01-01";
    $i = 0;
    do {
        $timeRecorre = strtotime("$inicio +$i week");
        $dia = date('N', $timeRecorre);
        if ($dia == 1) {
            $fecha = validaSemana($timeRecorre, $semana);
        } else {

            $timeRecorre = strtotime("last Monday", $timeRecorre);
            $recorreYear = date('Y', $timeRecorre);
            if ($recorreYear == $year) {
                $fecha = validaSemana($timeRecorre, $semana);
            } else {
                $fecha = 0;
            }
        }
        $i++;
    } while ($fecha === 0 and $i < 53);
    return $fecha;
}


function validaSemana($time, $semana)
{
    $recorreSemana = date('W', $time);
    if ($recorreSemana == $semana) {
        $fecha = date('Y-m-d', $time);
    } else {
        $fecha = 0;
    }

    return $fecha;
}
