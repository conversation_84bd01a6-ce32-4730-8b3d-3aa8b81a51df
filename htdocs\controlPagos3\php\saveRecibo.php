<?php
include_once("conexion.php");
include_once("configDB.php");
include_once("classProcesaVariable.php");
include_once("classAbono.php");
include_once("classRecibo.php");
include_once("session.php");

if (isset($_POST['operadorID']) and isset($_POST['conceptos'])) {

    $operadorID = new procesaVariable($_POST['operadorID']);
    $conceptos = new procesaVariable($_POST['conceptos']);
    $importes = new procesaVariable($_POST['importes']);
    $semanas = new procesaVariable($_POST['semanas']);
    $anios = new procesaVariable($_POST['anios']);
    $condona = new procesaVariable($_POST['condona']);
    $observaciones = new procesaVariable($_POST['observaciones']);

    $tipoUsuario = $_SESSION['userType'];
    $idUser = $_SESSION['idUser'];

    $taxiConect = new foo_mysqli("localhost", USER, PASS, DBNAME);
    $taxiConect->query("SET NAMES utf8");

    $auxil = $taxiConect->iniciaTransaccion();

    $myRecibo = new recibo();
    if ($myRecibo->guardaRecibo($taxiConect)) {
        $qObs = "UPDATE recibo SET observaciones = " . $observaciones->nulo() . " WHERE idRecibo='$myRecibo->identificador'";
        $rObs = $taxiConect->query($qObs);
        if (!$rObs) {
            $auxil = 0;
        }

        $n = count($conceptos->valor);
        for ($i = 0; $i < $n; $i++) {
            $importe = $importes->valor;
            $semana = $semanas->valor;
            $anio = $anios->valor;
            $condonado = $condona->valor;
            $abonos = new abono();
            $abonos->asignaDatos($operadorID->valor, $idUser, $tipoUsuario, $semana[$i], $importe[$i], $anio[$i], $condonado[$i]);
            if (!$abonos->guardaAbono($taxiConect, $myRecibo->identificador)) {
                $auxil = 0;
            }
        }
    } else {
        $auxil = 0;
    }

    if ($auxil != 0 and isset($_POST['depositoFlag']) and $_POST['depositoFlag'] == 1) {
        $fechaDesposito = new procesaVariable($_POST['fechaDeposito']);
        $movimientoNo = new procesaVariable($_POST['movimientoNo']);
        $cuenta = new procesaVariable($_POST['cuenta']);
        $montoPagado = $myRecibo->toTalRecibo($taxiConect);

        $q = "UPDATE recibo SET estado='P',
        idUsuario = '$idUser', 
        tipoUsuario = '$tipoUsuario',
        efectivo='$montoPagado',
        fechaPago='$fechaDesposito->valor',
        fechaDeposito='$fechaDesposito->valor',
        movimiento = '$movimientoNo->valor',
        idCuentaBancaria = '$cuenta->valor'
        WHERE idRecibo='$myRecibo->identificador'";
        $eq = $taxiConect->query($q);
        if (!$eq) {
            $auxil = 0;
        }
        $next = "1&deposito=1";
        $back = "0&deposito=1";
    } else {
        $next = "1";
        $back = "0";
    }

    $link = $taxiConect->finTransaccion($auxil, $next, $back);
    $taxiConect->resetTabla($auxil, "idAbono", "abono");
    $taxiConect->close();

    if ($auxil == 1) {
        $recibo = $myRecibo->identificador;
        include_once("enviaRecibo.php");
    }
} else {
    $link = '0';
}

header("Location: ../registroConcepto.php?resp=$link");
