<?php
class abono
{

    public $operador;
    public $usuario;
    public $semana;
    public $importe;
    public $anio;
    public $tipoAbono;
    public $recibo;
    public $tipoUser;

    function __construct()
    {
    }

    public function asignaDatos($opera, $user, $userType = 'C', $semana, $importe, $anio, $tipo = 'A')
    {
        $this->operador = $opera;
        $this->usuario = $user;
        $this->semana = $semana;
        $this->importe = $importe;
        $this->anio = $anio;
        $this->tipoAbono = $tipo;
        $this->tipoUser = $userType;
    }

    public function guardaAbono($conexion, $recibo)
    {
        $insert = "INSERT INTO abono(idAbono, idUsuario, tipoUsuario, idOperador, idImporte, idRecibo, semana, anio, tipoAbono)
        VALUES (NULL,
        '$this->usuario',
        '$this->tipoUser',
        '$this->operador',
        '$this->importe',
        '$recibo',
        '$this->semana',
        '$this->anio',
        '$this->tipoAbono')";
        $rInsert = $conexion->query($insert);

        return $rInsert;
    }

    public function ultimaSemana($conexion)
    {
        $q = "SELECT t.semana, t.anio
        FROM abono t
        INNER JOIN importe i ON t.idImporte = i.idImporte
        INNER JOIN concepto c ON i.idConcepto = c.idConcepto
        WHERE c.idConcepto = '$this->concepto'
        AND t.idOperador = '$this->operador'        
        ORDER BY t.idAbono DESC
        LIMIT 1;";
        $rq = $conexion->query($q);

        if ($rq->num_rows != 0) {
            $res = $rq->fetch_array(MYSQLI_ASSOC);
            $this->semana = $res['semana'];
            $this->anio = $res['anio'];
        }
    }

    public function siguienteSemana()
    {
        if (is_null($this->semana) or $this->semana == 53) {
            $this->semana = date('W');
            $this->anio = date('Y');
        } else {
            $this->semana += 1;
        }
    }

    public function inicioFinSemana()
    {
        if (date('N') == 7) {
            $fecha['fin'] = date('d-m-Y', strtotime("Now"));
        } else {
            $fecha['fin'] = date('d-m-Y', strtotime("next Sunday"));
        }

        if (date('N') == 1) {
            $fecha['inicio'] = date('d-m-Y', strtotime("Now"));
        } else {
            $fecha['inicio'] = date('d-m-Y', strtotime("last Monday"));
        }

        return $fecha;
    }

    public function getAbonoRecibo($conexion)
    {
        $q = "SELECT c.descripcion AS Concepto, a.semana, a.anio, a.tipoAbono, i.monto
        FROM abono a
        INNER JOIN importe i ON a.idImporte = i.idImporte
        INNER JOIN concepto c ON i.idConcepto = c.idConcepto
        WHERE idRecibo = '$this->recibo';";
        $rq = $conexion->query($q);

        $res = array();

        if ($rq->num_rows != 0) {
            while ($row = $rq->fetch_array(MYSQLI_ASSOC)) {
                array_push($res, $row);
            }
        } else {
            array_push($res);
        }
        return $res;
    }


    function abonoConvenio($conexion)
    {
        $q = "SELECT	c.descripcion AS Concepto, i.monto AS importeUnitario, 
		GROUP_CONCAT(semana ORDER BY semana ASC SEPARATOR ', ') AS Semanas, anio,  SUM(i.monto) AS Adeudo
        FROM abono a
        INNER JOIN importe i ON a.idImporte = i.idImporte 
        INNER JOIN concepto c ON i.idConcepto = c.idConcepto 
        WHERE idRecibo = '$this->recibo'
        AND tipoAbono = 'V'
        GROUP BY c.descripcion,anio,i.monto";
        $rq = $conexion->query($q);

        return $rq;
    }

    public function aniosKardex($conexion)
    {
        $q = "SELECT anio 
        FROM abono
        WHERE idOperador = '$this->operador'
        GROUP BY anio
        ORDER BY anio DESC;";
        $eq = $conexion->query($q);

        if ($eq->num_rows != 0) {
            $x = $eq;
        } else {
            $x = false;
        }

        return $x;
    }

    public function operadorRecibo($conexion)
    {
        $q = "SELECT o.nombreCompleto, o.contable, o.correo
        FROM abono a 
        INNER JOIN operadorview o ON a.idOperador = o.idOperador 
        WHERE a.idRecibo = '$this->recibo'";
        $eq = $conexion->query($q);

        if ($eq->num_rows != 0) {
            $x = $eq->fetch_array(MYSQLI_ASSOC);
        } else {
            $x = false;
        }

        return $x;
    }

    public function capturaCajaRecibo($conexion, $tabla)
    {
        $q = "SELECT u.nombreUsuario 
        FROM $tabla a 
        INNER JOIN login l ON a.idUsuario = l.idUsuario AND a.tipoUsuario = l.tipoUsuario 
        INNER JOIN userlist u ON l.idUsuario = u.idUsuario 
        WHERE a.idRecibo = '$this->recibo'";
        $eq = $conexion->query($q);

        if ($eq->num_rows != 0) {
            $row = $eq->fetch_array(MYSQLI_ASSOC);
            $x = $row['nombreUsuario'];
        } else {
            $x = false;
        }

        return $x;
    }

    public function totalXConcepto($conexion, $concepto, $fecha)
    {
        $q = "SELECT SUM(i.monto) AS montoTotal 
        FROM abono a
        INNER JOIN importe i ON a.idImporte = i.idImporte
        INNER JOIN recibo r ON a.idRecibo = r.idRecibo
        WHERE a.idOperador = '$this->operador' 
        AND a.tipoAbono NOT IN ('V')
        AND i.idConcepto = '$concepto'
        AND r.fechaPago < '$fecha';";
        $eq = $conexion->query($q);
        if ($eq->num_rows != 0) {
            $row = $eq->fetch_array(MYSQLI_ASSOC);
            $monto = $row['montoTotal'];
        } else {
            $monto = null;
        }

        if (is_null($monto)) {
            $monto = 0;
        }

        return $monto;
    }
}
