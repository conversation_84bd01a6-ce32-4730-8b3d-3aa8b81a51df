<?php
include_once("../conexion.php");
include_once("../configDB.php");
include_once("../classAbono.php");
include_once("../classProcesaVariable.php");
include_once("../classRecibo.php");

if (isset($_POST['datoBusca'])) {

    $taxiConect = new foo_mysqli("localhost", USER, PASS, DBNAME);
    $taxiConect->query("SET NAMES utf8");

    $dato = new procesaVariable($_POST['datoBusca']);
    $x = $dato->procesoBusqueda();

    $recibos = new recibo();
    $aux = $recibos->reciboSinDeposito($taxiConect, $x);

    $taxiConect->close();
} else {
    $aux = array(0);
}

echo json_encode($aux);
