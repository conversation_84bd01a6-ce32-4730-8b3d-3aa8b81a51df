-- Script para crear las tablas del sistema de minutas
-- Base de datos: minutas_db

CREATE DATABASE IF NOT EXISTS minutas_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE minutas_db;

-- Tabla principal de minutas
CREATE TABLE minutas (
    id INT AUTO_INCREMENT PRIMARY KEY,
    minuta_id VARCHAR(20) UNIQUE NOT NULL,
    fecha_hora DATETIME NOT NULL,
    lugar VARCHAR(255) NOT NULL,
    duracion VARCHAR(50) NOT NULL,
    convoca VARCHAR(255) NOT NULL,
    antecedentes TEXT,
    objetivo TEXT,
    relator VARCHAR(255) NOT NULL,
    orden_dia TEXT,
    proxima_reunion DATETIME,
    fecha_creacion TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    fecha_modificacion TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Tabla de asistentes por minuta
CREATE TABLE minuta_asistentes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    minuta_id VARCHAR(20) NOT NULL,
    nombre VARCHAR(255) NOT NULL,
    area VARCHAR(255),
    extension_correo VARCHAR(255),
    firma VARCHAR(255),
    FOREIGN KEY (minuta_id) REFERENCES minutas(minuta_id) ON DELETE CASCADE
);

-- Tabla de asuntos tratados por minuta
CREATE TABLE minuta_asuntos (
    id INT AUTO_INCREMENT PRIMARY KEY,
    minuta_id VARCHAR(20) NOT NULL,
    orden_asunto INT NOT NULL,
    descripcion TEXT NOT NULL,
    FOREIGN KEY (minuta_id) REFERENCES minutas(minuta_id) ON DELETE CASCADE
);

-- Tabla de acuerdos por minuta
CREATE TABLE minuta_acuerdos (
    id INT AUTO_INCREMENT PRIMARY KEY,
    minuta_id VARCHAR(20) NOT NULL,
    orden_acuerdo INT NOT NULL,
    descripcion TEXT NOT NULL,
    responsable VARCHAR(255),
    fecha_aproximada DATETIME,
    FOREIGN KEY (minuta_id) REFERENCES minutas(minuta_id) ON DELETE CASCADE
);

-- Índices para mejorar rendimiento
CREATE INDEX idx_minuta_fecha ON minutas(fecha_hora);
CREATE INDEX idx_minuta_convoca ON minutas(convoca);
CREATE INDEX idx_asistentes_minuta ON minuta_asistentes(minuta_id);
CREATE INDEX idx_asuntos_minuta ON minuta_asuntos(minuta_id);
CREATE INDEX idx_acuerdos_minuta ON minuta_acuerdos(minuta_id);

-- Insertar algunos datos de ejemplo (opcional)
INSERT INTO minutas (minuta_id, fecha_hora, lugar, duracion, convoca, antecedentes, objetivo, relator, orden_dia, proxima_reunion) 
VALUES 
('MIN-2024-001', '2024-01-15 10:00:00', 'Sala de junta A del RUV', '2 horas', 'María Elena López', 
 'Reunión de seguimiento del proyecto de desarrollo', 'Revisar avances y definir próximos pasos', 
 'Norberto Rojas Nava', 'Revisión de avances\nDefinición de tareas\nPlanificación siguiente fase', 
 '2024-01-22 10:00:00');
