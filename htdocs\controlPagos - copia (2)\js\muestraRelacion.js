$(document).ready(function () {
  if ($.get("res")) {
    Rsp = $.get("res");
    switch (Rsp) {
      case "0":
        rmsg = "Error en el proceso";
        break;
      case "1":
        rmsg = "Baja guardada correctamente";
        break;
      default:
        rmsg = "Error al guardar en la base de datos.";
    }
    alertify
      .alert()
      .setting({
        title: "Servitaxis Sitio 152",
        closable: false,
        message: rmsg,
        onok: function () {
          let myUrl = window.location.origin;
          myUrl = `${myUrl}/muestraRelacion.php`;
          $(location).attr("href", myUrl);
        },
      })
      .show();
  }

  const admin = $("#admin").val();
  $.ajax({
    type: "post",
    url: "php/ajax/relacionesActivas.php",
    data: { myVal: admin },
  })
    .done(function (r) {
      R = $.parseJSON(r);
      if (R[0] != 0) {
        $.each(R, function (indexInArray, valueOfElement) {
          const { idEconomico, placa, economico } = valueOfElement.auto;
          const {
            idOperador,
            nombre,
            apellidoPaterno,
            apellidoMaterno,
            contable,
          } = valueOfElement.operador;
          const estado = valueOfElement.estadoRelacion;
          const Alta = valueOfElement.alta;
          const Baja = valueOfElement.baja;

          //   const append = `<form action="php/bajaRelacion.php" method="POST"><div class="row border-bottom">

          //          <div class="col-lg-1">${indexInArray + 1}</div>
          //          <div class="col-lg-4"><input type="hidden" name="idOperador" value="${idOperador}">
          //              <p>Operador: ${nombre} ${apellidoPaterno} ${apellidoMaterno}</p>
          //              <p>Contable: ${contable}</p>
          //          </div>
          //          <div class="col-lg-4"><input type="hidden" name="idEconomico" value="${idEconomico}">
          //              <p>Placa: ${placa}</p>
          //              <p>Economico: ${economico}</p>
          //          </div>
          //          <div class="col-lg-2"><button type="submit" class="btn btn-danger">Dar de Baja</button></div>
          //  </div></form>`;
          const append = `<tr>                    
                    <td>${indexInArray + 1}</td>
                    <td>
                      <p>Operador: ${nombre} ${apellidoPaterno} ${apellidoMaterno}</p>
                      <p>Contable: ${contable}</p>
                    </td>
                    <td>
                      <p>Placa: ${placa}</p>
                      <p>Economico: ${economico}</p>
                    </td>
                    <td>${Alta}</td>
                    <td>${Baja}</td>
                    <td><button value="${idOperador}-${idEconomico}" name="acctionList" class="btn ${
            estado == "A" ? "btn-danger" : "btn-outline-secondary"
          }" ${estado == "A" ? "" : "disabled"}>${
            estado == "A" ? "Dar de Baja" : "Relacion Inactiva"
          }</button></td>
                </tr>`;
          $("#contenidoTabla tbody").append(append);
        });

        $("#contenidoTabla").DataTable({
          language: {
            url: "//cdn.datatables.net/plug-ins/9dcbecd42ad/i18n/Spanish.json",
          },
        });
        $("#contenidoTabla").tableExport({
          formats: ["csv", "txt"],
        });
      }
    })
    .fail(function () {
      alertify.alert("Warning!", "Error al cargar los datos");
    });
});
