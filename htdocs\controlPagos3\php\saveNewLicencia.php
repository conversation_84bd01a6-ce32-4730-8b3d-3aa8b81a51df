<?php
include_once("conexion.php");
include_once("configDB.php");
include_once("classProcesaVariable.php");
include_once("classLicencia.php");

if (isset($_POST['folio']) and isset($_POST['fechaExpedicion'])) {

    $folio = new procesaVariable($_POST['folio']);
    $fechaExpedicion = new procesaVariable($_POST['fechaExpedicion']);
    $fechaVigencia = new procesaVariable($_POST['fechaVigencia']);
    $idOperador = new procesaVariable($_POST['idOperador']);

    $taxiConect = new foo_mysqli("localhost", USER, PASS, DBNAME);
    $taxiConect->query("SET NAMES utf8");

    $licenciaOP =  new licencia($folio->valor, $fechaExpedicion->valor, $fechaVigencia->valor, $idOperador->valor);
    if ($licenciaOP->saveLicencia($taxiConect)) {
        $var = 1;
    } else {
        $var = 0;
    }

    $taxiConect->close();
} else {
    $var = '0';
}

header("Location: ../nuevaLicencia.php?res=$var");
