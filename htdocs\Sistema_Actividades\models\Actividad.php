<?php
/**
 * Modelo de Actividad - Sistema de Actividades
 */

class Actividad {
    private $conn;
    private $table_name = "actividades";
    private $table_temas = "actividades_temas";
    private $table_historial = "actividades_historial";

    public $id;
    public $actividad_id;
    public $tarea;
    public $tema;
    public $prioridad;
    public $fecha_hora_compromiso;
    public $responsable;
    public $progreso;
    public $comentarios;

    public function __construct($db) {
        $this->conn = $db;
    }

    /**
     * Generar ID único para actividad
     */
    public function generateId() {
        $year = date('Y');
        $month = date('m');
        
        // Buscar el último número de actividad del mes
        $query = "SELECT actividad_id FROM " . $this->table_name . " 
                  WHERE actividad_id LIKE :pattern 
                  ORDER BY actividad_id DESC LIMIT 1";
        
        $pattern = "ACT-{$year}-{$month}-%";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':pattern', $pattern);
        $stmt->execute();
        
        $lastId = $stmt->fetch();
        
        if ($lastId) {
            // Extraer el número y incrementar
            $parts = explode('-', $lastId['actividad_id']);
            $number = intval($parts[3]) + 1;
        } else {
            $number = 1;
        }
        
        return sprintf("ACT-%s-%s-%03d", $year, $month, $number);
    }

    /**
     * Crear nueva actividad
     */
    public function create($data) {
        try {
            $this->conn->beginTransaction();

            // Generar ID único
            $actividad_id = $this->generateId();

            // Convertir fecha al formato MySQL
            $fecha_compromiso = $this->convertDateToMysql($data['fechaHoraCompromiso']);

            $query = "INSERT INTO " . $this->table_name . " 
                     (actividad_id, tarea, tema, prioridad, fecha_hora_compromiso, responsable, comentarios) 
                     VALUES (:actividad_id, :tarea, :tema, :prioridad, :fecha_compromiso, :responsable, :comentarios)";

            $stmt = $this->conn->prepare($query);

            $stmt->bindParam(':actividad_id', $actividad_id);
            $stmt->bindParam(':tarea', $data['tarea']);
            $stmt->bindParam(':tema', $data['tema']);
            $stmt->bindParam(':prioridad', $data['prioridad']);
            $stmt->bindParam(':fecha_compromiso', $fecha_compromiso);
            $stmt->bindParam(':responsable', $data['responsable']);
            $stmt->bindParam(':comentarios', $data['comentarios']);

            if (!$stmt->execute()) {
                throw new Exception("Error al insertar actividad");
            }

            // Registrar en historial
            $this->registrarHistorial($actividad_id, 'creacion', '', 'Actividad creada', 'SISTEMA');

            $this->conn->commit();
            return $actividad_id;

        } catch (Exception $e) {
            $this->conn->rollback();
            throw $e;
        }
    }

    /**
     * Obtener actividad por ID
     */
    public function getById($actividad_id) {
        $query = "SELECT * FROM vista_actividades WHERE actividad_id = :actividad_id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':actividad_id', $actividad_id);
        $stmt->execute();

        $actividad = $stmt->fetch();
        
        if ($actividad) {
            // Convertir fecha al formato de visualización
            $actividad['fecha_hora_compromiso'] = $this->convertDateFromMysql($actividad['fecha_hora_compromiso']);
        }

        return $actividad;
    }

    /**
     * Obtener todas las actividades
     */
    public function getAll($limit = 100, $offset = 0, $filtros = []) {
        $whereConditions = [];
        $params = [];

        // Aplicar filtros
        if (!empty($filtros['tema'])) {
            $whereConditions[] = "tema = :tema";
            $params[':tema'] = $filtros['tema'];
        }

        if (!empty($filtros['prioridad'])) {
            $whereConditions[] = "prioridad = :prioridad";
            $params[':prioridad'] = $filtros['prioridad'];
        }

        if (!empty($filtros['responsable'])) {
            $whereConditions[] = "responsable = :responsable";
            $params[':responsable'] = $filtros['responsable'];
        }

        if (!empty($filtros['progreso'])) {
            if ($filtros['progreso'] === 'Vencido') {
                $whereConditions[] = "estado_calculado = 'Vencido'";
            } else {
                $whereConditions[] = "progreso = :progreso";
                $params[':progreso'] = $filtros['progreso'];
            }
        }

        $whereClause = !empty($whereConditions) ? "WHERE " . implode(" AND ", $whereConditions) : "";

        $query = "SELECT * FROM vista_actividades 
                  {$whereClause}
                  ORDER BY 
                    CASE prioridad 
                        WHEN 'Alta' THEN 1 
                        WHEN 'Media' THEN 2 
                        WHEN 'Baja' THEN 3 
                    END,
                    fecha_hora_compromiso ASC
                  LIMIT :limit OFFSET :offset";

        $stmt = $this->conn->prepare($query);
        
        foreach ($params as $key => $value) {
            $stmt->bindValue($key, $value);
        }
        
        $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
        $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
        $stmt->execute();

        $actividades = $stmt->fetchAll();

        // Convertir fechas al formato de visualización
        foreach ($actividades as &$actividad) {
            $actividad['fecha_hora_compromiso'] = $this->convertDateFromMysql($actividad['fecha_hora_compromiso']);
        }

        return $actividades;
    }

    /**
     * Actualizar actividad
     */
    public function update($actividad_id, $data) {
        try {
            $this->conn->beginTransaction();

            // Obtener datos actuales para historial
            $actividadActual = $this->getById($actividad_id);
            if (!$actividadActual) {
                throw new Exception("Actividad no encontrada");
            }

            // Convertir fecha al formato MySQL
            $fecha_compromiso = $this->convertDateToMysql($data['fechaHoraCompromiso']);

            $query = "UPDATE " . $this->table_name . " 
                     SET tarea = :tarea, 
                         tema = :tema, 
                         prioridad = :prioridad, 
                         fecha_hora_compromiso = :fecha_compromiso, 
                         responsable = :responsable, 
                         progreso = :progreso,
                         comentarios = :comentarios
                     WHERE actividad_id = :actividad_id";

            $stmt = $this->conn->prepare($query);

            $stmt->bindParam(':actividad_id', $actividad_id);
            $stmt->bindParam(':tarea', $data['tarea']);
            $stmt->bindParam(':tema', $data['tema']);
            $stmt->bindParam(':prioridad', $data['prioridad']);
            $stmt->bindParam(':fecha_compromiso', $fecha_compromiso);
            $stmt->bindParam(':responsable', $data['responsable']);
            $stmt->bindParam(':progreso', $data['progreso']);
            $stmt->bindParam(':comentarios', $data['comentarios']);

            if (!$stmt->execute()) {
                throw new Exception("Error al actualizar actividad");
            }

            // Registrar cambios en historial
            $this->compararYRegistrarCambios($actividad_id, $actividadActual, $data);

            $this->conn->commit();
            return true;

        } catch (Exception $e) {
            $this->conn->rollback();
            throw $e;
        }
    }

    /**
     * Obtener temas disponibles
     */
    public function getTemas() {
        $query = "SELECT nombre FROM " . $this->table_temas . " WHERE activo = 1 ORDER BY nombre";
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_COLUMN);
    }

    /**
     * Convertir fecha de formato dd/mm/yyyy hh:mm a MySQL
     */
    private function convertDateToMysql($dateString) {
        if (empty($dateString)) return null;
        
        $date = DateTime::createFromFormat('d/m/Y H:i', $dateString);
        if ($date === false) {
            throw new Exception("Formato de fecha inválido: $dateString");
        }
        
        return $date->format('Y-m-d H:i:s');
    }

    /**
     * Convertir fecha de MySQL a formato dd/mm/yyyy hh:mm
     */
    private function convertDateFromMysql($dateString) {
        if (empty($dateString)) return '';
        
        $date = DateTime::createFromFormat('Y-m-d H:i:s', $dateString);
        if ($date === false) return $dateString;
        
        return $date->format('d/m/Y H:i');
    }

    /**
     * Registrar en historial
     */
    private function registrarHistorial($actividad_id, $campo, $valor_anterior, $valor_nuevo, $usuario = 'SISTEMA') {
        $query = "INSERT INTO " . $this->table_historial . " 
                  (actividad_id, campo_modificado, valor_anterior, valor_nuevo, usuario_modificacion) 
                  VALUES (:actividad_id, :campo, :valor_anterior, :valor_nuevo, :usuario)";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':actividad_id', $actividad_id);
        $stmt->bindParam(':campo', $campo);
        $stmt->bindParam(':valor_anterior', $valor_anterior);
        $stmt->bindParam(':valor_nuevo', $valor_nuevo);
        $stmt->bindParam(':usuario', $usuario);
        $stmt->execute();
    }

    /**
     * Comparar datos y registrar cambios
     */
    private function compararYRegistrarCambios($actividad_id, $datosAnteriores, $datosNuevos) {
        $campos = [
            'tarea' => 'tarea',
            'tema' => 'tema', 
            'prioridad' => 'prioridad',
            'fechaHoraCompromiso' => 'fecha_hora_compromiso',
            'responsable' => 'responsable',
            'progreso' => 'progreso',
            'comentarios' => 'comentarios'
        ];

        foreach ($campos as $campoNuevo => $campoAnterior) {
            $valorAnterior = $datosAnteriores[$campoAnterior] ?? '';
            $valorNuevo = $datosNuevos[$campoNuevo] ?? '';

            if ($campoNuevo === 'fechaHoraCompromiso') {
                $valorNuevo = $this->convertDateToMysql($valorNuevo);
                $valorNuevo = $this->convertDateFromMysql($valorNuevo);
            }

            if ($valorAnterior != $valorNuevo) {
                $this->registrarHistorial($actividad_id, $campoAnterior, $valorAnterior, $valorNuevo, 'USUARIO');
            }
        }
    }
}
?>
