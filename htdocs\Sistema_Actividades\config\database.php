<?php
/**
 * Configuración de Base de Datos - Sistema de Actividades
 * Utiliza la misma base de datos que el Sistema de Minutas
 */

class Database {
    private $host = "localhost";
    private $db_name = "minutas_db";  // Misma base de datos
    private $username = "root";
    private $password = "";
    public $conn;

    /**
     * Obtener conexión a la base de datos
     */
    public function getConnection() {
        $this->conn = null;

        try {
            $this->conn = new PDO(
                "mysql:host=" . $this->host . ";dbname=" . $this->db_name . ";charset=utf8",
                $this->username,
                $this->password
            );
            $this->conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            $this->conn->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
        } catch(PDOException $exception) {
            echo "Error de conexión: " . $exception->getMessage();
        }

        return $this->conn;
    }

    /**
     * Cerrar conexión
     */
    public function closeConnection() {
        $this->conn = null;
    }

    /**
     * Verificar si las tablas del sistema existen
     */
    public function verificarTablas() {
        try {
            $query = "SHOW TABLES LIKE 'actividades'";
            $stmt = $this->conn->prepare($query);
            $stmt->execute();
            
            if ($stmt->rowCount() === 0) {
                throw new Exception("Las tablas del sistema no existen. Ejecute el script database_setup.sql");
            }
            
            return true;
        } catch (Exception $e) {
            throw new Exception("Error verificando tablas: " . $e->getMessage());
        }
    }

    /**
     * Ejecutar actualización automática de estados
     */
    public function actualizarEstados() {
        try {
            $query = "CALL ActualizarEstadosActividades()";
            $stmt = $this->conn->prepare($query);
            $stmt->execute();
            return true;
        } catch (Exception $e) {
            // Log del error pero no interrumpir la aplicación
            error_log("Error actualizando estados: " . $e->getMessage());
            return false;
        }
    }
}
?>
