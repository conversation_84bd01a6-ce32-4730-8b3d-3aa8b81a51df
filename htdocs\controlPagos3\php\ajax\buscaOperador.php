<?php
include_once("../conexion.php");
include_once("../configDB.php");
include_once("../classAbono.php");
include_once("../classProcesaVariable.php");

if (isset($_POST['datoBusca'])) {

	$taxiConect = new foo_mysqli("localhost", USER, PASS, DBNAME);
	$taxiConect->query("SET NAMES utf8");

	$dato = new procesaVariable($_POST['datoBusca']);
	$x = $dato->procesoBusqueda();

	$qry = "SELECT idOperador, nombreCompleto, contable, referenciado 
	FROM operadorview 
	WHERE contable = '$x'
	OR nombreCompleto LIKE '$x'";

	$rqry = $taxiConect->query($qry);

	$aux = array();

	if ($rqry->num_rows != 0) {
		while ($row = $rqry->fetch_array(MYSQLI_ASSOC)) {
			array_push($aux, $row);
		}

		$transac = new abono();
		$transac->siguienteSemana();
		$aux['semanaSig'] = $transac->semana;
		$aux['anioSemana'] = $transac->anio;
		$aux['semInicioFin'] = $transac->inicioFinSemana();

		if (isset($_POST['licencia'])) {
			include_once("../classLicencia.php");
			$data = $aux[0];
			$Licencia = new licencia("", "", "", $data['idOperador']);
			$aux['licencia'] = $Licencia->vigente($taxiConect);
		}
	} else {
		$aux = array(0, $qry);
	}

	$taxiConect->close();
} else {
	$aux = array(0);
}

echo json_encode($aux);
