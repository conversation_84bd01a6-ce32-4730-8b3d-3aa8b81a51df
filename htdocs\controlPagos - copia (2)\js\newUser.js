$(document).ready(function () {
  $("#nombre,#aPaterno,#aMaterno").cambiaMayus();

  if ($.get("resp")) {
    resp = $.get("resp");
    switch (resp) {
      case "0":
        msg = "Erro<PERSON> al guardar, intente nuevamente.";
        break;
      case "1":
        msg = "Usuario guardado satisfacctoriamente";
        break;
    }
    alertify.alert("Servitaxis Sitio 152", msg);
  }

  $("#userForm").validate({
    rules: {
      correoUser: {
        remote: "php/ajax/validaVariable.php",
      },
      userName: {
        remote: "php/ajax/validaVariable.php",
      },
    },
    messages: {
      correoUser: {
        remote: "El correo ya es utilizado por un usuario previo.",
      },
      userName: {
        remote: "Nombre de usuario invalido.",
      },
    },
  });
});
