<?php
include_once("conexion.php");
include_once("configDB.php");
include_once("classProcesaVariable.php");
include_once("classImporte.php");

if (isset($_POST['concepto'])) {
    $concepto = new procesaVariable($_POST['concepto']);
    $importe = new procesaVariable($_POST['importe']);

    $taxiConect = new foo_mysqli("localhost", USER, PASS, DBNAME);
    $taxiConect->query("SET NAMES utf8");

    $transac = $taxiConect->iniciaTransaccion();

    $idConcepto = $concepto->valor;
    $baja = new importe($idConcepto,'I',0);
    $transac = $baja->bajaImporte($taxiConect,$transac);
    $importes = $importe->valor;
    foreach ($importes as $c => $m) {
        $importe = new importe($idConcepto,'A',$m);
        $transac = $importe->nuevoImporte($taxiConect,$transac);
    }

    $link = $taxiConect->finTransaccion($transac, '$next', '$back');

    $taxiConect->resetTabla($transac,'idImporte','importe');

    $taxiConect->close();
} else {
    $transac = 0;
}

header("Location: ../conceptoImporte.php?resp=$transac");
