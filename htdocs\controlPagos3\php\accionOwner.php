<?php
include_once("conexion.php");
include_once("configDB.php");
include_once("classProcesaVariable.php");
include_once("classOwner.php");

if (isset($_POST['ownerAction'])) {
    $owner = new procesaVariable($_POST['ownerAction']);

    $taxiConect = new foo_mysqli("localhost", USER, PASS, DBNAME);
    $taxiConect->query("SET NAMES utf8");

    $data = explode("-", $owner->valor);
    $propietario =  new owner("", "", "", "", "");
    $propietario->identificador = $data[0];
    $propietario->estado = $data[1];

    if ($propietario->baja($taxiConect)) {
        $var = 1;
    } else {
        $var = 0;
    }
    $liga = "../listaOwner.php?res=$var";

    $taxiConect->close();
} elseif (isset($_POST['ownerEdit'])) {
    $owner = new procesaVariable($_POST['ownerEdit']);
    $liga = "../editaOwner.php?owner=$owner->valor";
} else {
    $var = '0';
}
echo $liga;
header("Location: $liga");
