<?php include_once('php/session.php'); ?>
<!DOCTYPE html>
<html>

<head>
    <meta charset="UTF-8">
    <?php include_once("includes/head.html"); ?>
    <script src="js/changePassAdmin.js"></script>

</head>

<body class="bg-light">
    <div class="container">
        <br>
        <div id="header"></div>
        <?php include_once("includes/menu.html"); ?>

        <br>
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="card border-success mb-3" style="max-width: 100%;">
                    <div class="card-header bg-danger p-2 text-dark bg-opacity-10">
                        <h3>Nueva contraseña</h3>
                    </div>
                    <div class="card-body">
                        <form method="POST" action="php/actualizaContra.php" id="formNewPass">
                            <input type="hidden" name="admin" value="8709011992">
                            <input type="hidden" name="contraActual" id="contraActual" value="$2y$10$/gZdbRBxFetIiiGzF863S.znVIExUOWGgNeN6uqT2RRce9y.R/oge">
                            <div class="row justify-content-center text-center">
                                <h4>Seleccione empleado</h4>
                            </div>
                            <div class="row justify-content-center">
                                <div class="col-lg-3">
                                    <label for="owner">
                                        <h5>Empleado:</h5>
                                    </label>
                                    <select class="form-control" name="empleado" id="empleado" required>
                                        <option value="">Selecione una opción ...</option>
                                    </select>
                                </div>
                            </div>
                            <div class="row justify-content-center mt-4" id="divUser">
                                <div class="col-lg-3">
                                    <label for="usuario">
                                        <h5>Usuario:</h5>
                                    </label>
                                    <select class="form-control" name="usuario" id="usuario" required></select>
                                </div>
                            </div>
                            <hr>
                            <div id="divPassNew">
                                <div class="row justify-content-center text-center mt-2">
                                    <h4>Nueva Constraseña</h4>
                                </div>
                                <div class="row justify-content-center">
                                    <div class="col-lg-4">
                                        <label for="newPwd">
                                            <h5>Nueva Contraseña</h5>
                                        </label>
                                        <input class="form-control" type="password" minlength="8" name="contraNueva" id="contraNueva" required>
                                    </div>
                                </div>
                                <div class="row justify-content-center">
                                    <div class="col-lg-4">
                                        <label for="newPwd2">
                                            <h5>Repite Nueva Contraseña</h5>
                                        </label>
                                        <input class="form-control" type="password" minlength="8" name="contraNuevaC" id="contraNuevaC" required>
                                    </div>
                                </div>
                            </div>
                            <br>
                            <br>
                            <div class="row justify-content-center">
                                <div class="col-lg-3">
                                    <div class="d-grid gap-2">
                                        <button type="submit" class="btn btn-success">Guardar</button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        <div id="footer"></div>
    </div>
</body>

</html>