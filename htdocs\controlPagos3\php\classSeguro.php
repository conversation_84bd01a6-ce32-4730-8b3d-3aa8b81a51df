<?php
class seguro
{
    public $identificador;
    public $economico;
    public $aseguradora;
    public $poliza;
    public $vigenciaBegin;
    public $vigenciaEnd;

    function __construct($eco, $seg, $pol, $begin, $end)
    {
        $this->economico = $eco;
        $this->aseguradora = $seg;
        $this->poliza = $pol;
        $this->vigenciaBegin = $begin;
        $this->vigenciaEnd = $end;
    }

    public function save($conexion)
    {
        $q = "INSERT INTO seguro(idEconomico, aseguradora, poliza, vigenciaInicio, vigenciaFin) 
                VALUES ('$this->economico',            
                '$this->aseguradora',
                '$this->poliza',
                '$this->vigenciaBegin',
                '$this->vigenciaEnd')";
        $eq = $conexion->query($q);

        return $eq;
    }

    public function vigente($conexion)
    {
        $q = "SELECT idSeguro, idEconomico, aseguradora, poliza, vigenciaInicio, vigenciaFin 
        FROM segurovigente 
        WHERE idEconomico = '$this->economico'";
        $eq = $conexion->query($q);

        if ($eq->num_rows != 0) {
            $row = $eq->fetch_array(MYSQLI_ASSOC);
        } else {
            $row = array(0);
        }

        return $row;
    }

    public function update($conexion)
    {
        $q = "UPDATE seguro 
            SET aseguradora='$this->aseguradora',
            poliza='$this->poliza',
            vigenciaInicio='$this->vigenciaBegin',
            vigenciaFin='$this->vigenciaEnd'
            WHERE idSeguro = '$this->identificador'";
        $eq = $conexion->query($q);

        return $eq;
    }
}
