$(document).ready(function () {
  $("#reciboConceptos,#numChequeDiv").hide();
  if ($.get("resp")) {
    Rsp = $.get("resp");
    switch (Rsp) {
      case "0":
        rmsg = "Error al actualizar los datos";
        break;
      case "1":
        rmsg = "Datos guardados correctamente";
        break;
    }
    alertify.alert("Servitaxis Sitio 152", rmsg);
  }

  $("#dateDeposito").datepicker({
    dateFormat: "yy-mm-dd",
    maxDate: "0",
    showWeek: true,
    beforeShowDay: function (date) {
      return [!date.getDay() == 0];
    },
  });

  $("#busqueda").validate({
    submitHandler: function (form) {
      datoOP = $("#buscadorOp").val();
      $.ajax({
        type: "post",
        url: "php/ajax/buscaReciboNoDeposito.php",
        data: { datoBusca: datoOP },
      })
        .done(function (r) {
          R = $.parseJSON(r);
          if (R[0] != 0) {
            $("#recibosForm").append(
              '<div class="row justify-content-center">' +
                '<div class="col-lg-6 text-center">' +
                "<h2>Recibos Encontrados</h2>" +
                "</div>" +
                "</div>"
            );
            $("#recibosForm").empty();
            R.forEach((element) => {
              const { idRecibo } = element;
              $("#recibosForm").append(
                '<div class="row"><div class="col-lg-2"><div class="form-check">' +
                  '<input class="form-check-input" type="radio" name="recibo" id="recibo' +
                  idRecibo +
                  '" value="' +
                  idRecibo +
                  '"><label class="form-check-label"  for="recibo' +
                  idRecibo +
                  '"><h4>' +
                  idRecibo +
                  "</h4></label>" +
                  "</div></div></div>"
              );
            });
            $("#recibosForm").append(
              '<div class="row justify-content-center">' +
                '<div class="col-lg-4">' +
                '<label class="error" for="recibo"></label>' +
                "</div>" +
                "</div>" +
                '<div class="row justify-content-center">' +
                '<div class="col-lg-3">' +
                '<div class="d-grid gap-2">' +
                '<button class="btn btn-dark" name="mostrar" id="mostrar">Mostrar</button>' +
                "</div>" +
                "</div>" +
                "</div>"
            );
          } else {
            $("#recibosForm").empty();
            alertify.alert(
              "Servitaxis Sitio 152",
              "No se encontraron recibos sin pagar para este operador"
            );
          }
        })
        .fail(function () {
          alertify.alert("Warning!", "Error al cargar el formulario");
        });
    },
  });

  $("#recibosForm").validate({
    rules: {
      recibo: {
        required: true,
      },
    },
    messages: {
      recibo: "Por favor seleccione un folio de recibo",
    },
    submitHandler: function (form) {
      datoOP = $("input:radio[name=recibo]:checked").val();
      $.ajax({
        type: "post",
        url: "php/ajax/conceptosXRecibo.php",
        data: { datoBusca: datoOP },
      })
        .done(function (r) {
          R = $.parseJSON(r);

          if (R[0] != 0) {
            $("#reciboHide").val(datoOP);
            let i = 0;
            total = 0;
            $("#listaConceptos tbody").empty();
            $("#reciboConceptos").show();
            $("#divTextoCambio").hide();
            R.forEach((element) => {
              const { Concepto, semana, monto, anio, tipoAbono } = element;
              if (tipoAbono === "C") {
                condonaImage = "images/checked.png";
              } else if (tipoAbono === "A") {
                condonaImage = "images/unchecked.png";
                total = total + parseFloat(monto);
              }
              i++;
              $("#listaConceptos").append(
                "<tr>" +
                  `<th> ${i} </th>` +
                  `<td> ${Concepto} </td>` +
                  `<td>$ ${monto} </td>` +
                  `<td> ${semana} </td>` +
                  `<td> ${anio} </td>` +
                  `<td><img class="elimina" src="${condonaImage}" width="25" height="25">` +
                  "</td>" +
                  "</tr>"
              );
            });
            $("#totalRecibo").empty().html(`$ ${total}`);
          } else {
            $("#registroPago").hide();
            alertify.alert(
              "Servitaxis Sitio 152",
              "No se encontraron registros para este folio"
            );
          }
        })
        .fail(function () {
          alertify.alert("Warning!", "Error al cargar el formulario");
        });
    },
  });

  $("#reciboConceptos").validate();
});
