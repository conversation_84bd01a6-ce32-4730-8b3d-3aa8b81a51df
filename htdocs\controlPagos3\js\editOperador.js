$(document).ready(function () {
  if ($.get("res")) {
    Rsp = $.get("res");
    switch (Rsp) {
      case "0":
        rmsg = "Error en el proceso";
        break;
      case "1":
        rmsg = "Datos actualizados correctamente";
        break;
    }
    alertify
      .alert()
      .setting({
        title: "Servitaxis Sitio 152",
        closable: false,
        message: rmsg,
        onok: function () {
          let myUrl = window.location.origin;
          myUrl = `${myUrl}/editOperador.php`;
          $(location).attr("href", myUrl);
        },
      })
      .show();
  }

  $("#divBtnBaja,#dataOperador").hide();
  $("#editForm").hide();
  $("#editForm").validate();

  $("#buscadorOp").cambiaMayus().focus();
  $("#busqueda").validate({
    submitHandler: function (form) {
      datoOP = $("#buscadorOp").val();
      $.ajax({
        type: "post",
        url: "php/ajax/buscaOperadorData.php",
        data: { datoBusca: datoOP },
      })
        .done(function (r) {
          R = $.parseJSON(r);
          if (R[0] != 0) {
            const {
              apellidoMaterno,
              apellidoPaterno,
              celular,
              contable,
              correo,
              domicilio,
              idOperador,
              nombre,
              referenciado,
              telefono,
            } = R;

            $("#nombre").val(nombre).cambiaMayus();
            $("#aPaterno").val(apellidoPaterno).cambiaMayus();
            $("#aMaterno").val(apellidoMaterno).cambiaMayus();
            $("#contable").val(contable);
            $("#referenciado").val(referenciado).attr("readonly", true);
            $("#celular").val(celular);
            $("#correo").val(correo);
            $("#telefono").val(telefono);
            $("#direccion").val(domicilio).cambiaMayus();
            $("#idOperador").val(idOperador);

            $("#divBtnBaja,#dataOperador").show();
            $("#editForm").show();
          } else {
            $("#dataOperador").empty();
            $("#divBtnBaja,#dataOperador").hide();
            $("#editForm").hide();
            alertify.alert("Warning!", "Operador no encontrado");
          }
        })
        .fail(function () {
          alertify.alert("Warning!", "Error al cargar el formulario");
        });
    },
  });
});
