TYPE=VIEW
query=select `t`.`id` AS `id`,`t`.`nombre` AS `nombre`,`t`.`descripcion` AS `descripcion`,`t`.`color` AS `color`,`t`.`icono` AS `icono`,`t`.`orden` AS `orden`,`t`.`activo` AS `activo`,`t`.`fecha_creacion` AS `fecha_creacion`,`t`.`usuario_creacion` AS `usuario_creacion`,count(`a`.`id`) AS `total_actividades`,count(case when `a`.`progreso` = \'Vigente\' then 1 end) AS `actividades_vigentes`,count(case when `a`.`progreso` = \'Completado\' then 1 end) AS `actividades_completadas` from (`minutas_db`.`actividades_temas` `t` left join `minutas_db`.`actividades` `a` on(`t`.`nombre` = `a`.`tema`)) group by `t`.`id`,`t`.`nombre`,`t`.`descripcion`,`t`.`color`,`t`.`icono`,`t`.`orden`,`t`.`activo`,`t`.`fecha_creacion`,`t`.`usuario_creacion` order by `t`.`orden`,`t`.`nombre`
md5=3a4539b905f6ea24f08b5f6f98634628
updatable=0
algorithm=0
definer_user=root
definer_host=localhost
suid=2
with_check_option=0
timestamp=0001749250226371504
create-version=2
source=SELECT 
\n    t.id,
\n    t.nombre,
\n    t.descripcion,
\n    t.color,
\n    t.icono,
\n    t.orden,
\n    t.activo,
\n    t.fecha_creacion,
\n    t.usuario_creacion,
\n    COUNT(a.id) as total_actividades,
\n    COUNT(CASE WHEN a.progreso = \'Vigente\' THEN 1 END) as actividades_vigentes,
\n    COUNT(CASE WHEN a.progreso = \'Completado\' THEN 1 END) as actividades_completadas
\nFROM actividades_temas t
\nLEFT JOIN actividades a ON t.nombre = a.tema
\nGROUP BY t.id, t.nombre, t.descripcion, t.color, t.icono, t.orden, t.activo, t.fecha_creacion, t.usuario_creacion
\nORDER BY t.orden ASC, t.nombre ASC
client_cs_name=utf8mb4
connection_cl_name=utf8mb4_unicode_ci
view_body_utf8=select `t`.`id` AS `id`,`t`.`nombre` AS `nombre`,`t`.`descripcion` AS `descripcion`,`t`.`color` AS `color`,`t`.`icono` AS `icono`,`t`.`orden` AS `orden`,`t`.`activo` AS `activo`,`t`.`fecha_creacion` AS `fecha_creacion`,`t`.`usuario_creacion` AS `usuario_creacion`,count(`a`.`id`) AS `total_actividades`,count(case when `a`.`progreso` = \'Vigente\' then 1 end) AS `actividades_vigentes`,count(case when `a`.`progreso` = \'Completado\' then 1 end) AS `actividades_completadas` from (`minutas_db`.`actividades_temas` `t` left join `minutas_db`.`actividades` `a` on(`t`.`nombre` = `a`.`tema`)) group by `t`.`id`,`t`.`nombre`,`t`.`descripcion`,`t`.`color`,`t`.`icono`,`t`.`orden`,`t`.`activo`,`t`.`fecha_creacion`,`t`.`usuario_creacion` order by `t`.`orden`,`t`.`nombre`
mariadb-version=100432
