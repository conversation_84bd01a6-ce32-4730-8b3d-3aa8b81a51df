$(document).ready(function () {
  if ($.get("res")) {
    Rsp = $.get("res");
    switch (Rsp) {
      case "0":
        rmsg = "Error en el proceso";
        break;
      case "1":
        rmsg = "Alta guardada correctamente";
        break;
      case "2":
        rmsg = "Economico o contable inactivo";
        break;
      case "1062":
        rmsg = "Error, relacion guardada previamente";
        break;
      default:
        rmsg = "Error al guardar en la base de datos.";
    }
    alertify
      .alert()
      .setting({
        title: "Servitaxis Sitio 152",
        closable: false,
        message: rmsg,
        onok: function () {
          let myUrl = window.location.origin;
          myUrl = `${myUrl}/operadorEconomico.php`;
          $(location).attr("href", myUrl);
        },
      })
      .show();
  }

  $("#contable0").miCatalogo("operadorcontable", "Error en  catalogo de operador");
  $("#economico0").miCatalogo("carroactivo", "Error en  catalogo de economico");

  $("#relacionForm").validate();
  // $("#relacionForm").validate({
  //   rules: {
  //     contable0: {
  //       remote: "php/ajax/validaVariable.php",
  //     },
  //     economico0: {
  //       remote: "php/ajax/validaVariable.php",
  //     },
  //   },
  //   messages: {
  //     contable0: {
  //       remote: "Contable con relacion activa.",
  //     },
  //     economico0: {
  //       remote: "Economico con relacion activa.",
  //     },
  //   },
  // });
});
