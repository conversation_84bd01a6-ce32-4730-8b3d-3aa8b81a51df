<?php
include_once("conexion.php");
include_once("configDB.php");
include_once("session.php");
include_once("classOwner.php");

$back = $_SERVER['HTTP_REFERER'];

if (isset($_POST['admin'])) {
    $owner = $_POST['owner'];

    $taxiConect = new foo_mysqli("localhost", USER, PASS, DBNAME);
    $taxiConect->query("SET NAMES utf8");

    $baja = new owner("", "", "", "", "");
    $baja->identificador = $owner;
    $baja->estado = 'I';
    if ($baja->baja($taxiConect)) {
        $aux = 1;
    } else {
        $aux = 0;
    }

    $taxiConect->close();
} else {
    $aux = 0;
}
echo $back;
header("Location: $back?res=$aux");
