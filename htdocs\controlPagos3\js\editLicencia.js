$(document).ready(function () {
  if ($.get("res")) {
    Rsp = $.get("res");
    switch (Rsp) {
      case "0":
        rmsg = "Error en el proceso";
        break;
      case "1":
        rmsg = "Licencia guardada correctamente";
        break;
    }
    alertify
      .alert()
      .setting({
        title: "Servitaxis Sitio 152",
        closable: false,
        message: rmsg,
        onok: function () {
          let myUrl = window.location.origin;
          myUrl = `${myUrl}/editLicencia.php`;
          $(location).attr("href", myUrl);
        },
      })
      .show();
  }

  $("#fechaExpedicion")
    .datepicker({
      dateFormat: "yy-mm-dd",
      changeMonth: true,
      changeYear: true,
      numberOfMonths: 1,
    })
    .change(function () {
      $("#fechaVigencia").datepicker("option", "minDate", $(this).val());
    });
  $("#fechaVigencia")
    .datepicker({
      dateFormat: "yy-mm-dd",
      defaultDate: "+10m",
      changeMonth: true,
      changeYear: true,
      numberOfMonths: 1,
    })
    .change(function () {
      $("#fechaExpedicion").datepicker("option", "maxDate", $(this).val());
    });

  $("#divBtnBaja,#dataOperador").hide();
  $("#formLicencia").hide();
  $("#formLicencia").validate();

  $("#buscadorOp").cambiaMayus().focus();
  $("#busqueda").validate({
    submitHandler: function (form) {
      datoOP = $("#buscadorOp").val();
      $.ajax({
        type: "post",
        url: "php/ajax/buscaOperador.php",
        data: { datoBusca: datoOP, licencia: 1 },
      })
        .done(function (r) {
          R = $.parseJSON(r);
          console.log(R);
          if (R[0] != 0) {
            semanaActual = R.semanaSig;
            anioSemana = R.anioSemana;
            delete R.semanaSig;
            delete R.anioSemana;
            $("#dataOperador").empty();
            const { contable, idOperador, nombreCompleto } = R[0];
            const { idLicencia, vigenciaFin, vigenciaInicio } = R["licencia"];
            const append = `<div class="col-lg-8">
            <h3>Nombre: ${nombreCompleto}</h3>
            <h3>Contable: ${contable}</h3>
            <input type="hidden" name="idOperador" value="${idOperador}">
            <input type="hidden" name="idLicenciaVigente" value="${idLicencia}">
            </div>`;            
            $("#dataOperador").append(append);
            $("#folio").val(idLicencia);
            $("#fechaExpedicion").val(vigenciaInicio);
            $("#fechaVigencia").val(vigenciaFin);
            $("#divBtnBaja,#dataOperador").show();
            $("#formLicencia").show();
          } else {
            $("#dataOperador").empty();
            $("#divBtnBaja,#dataOperador").hide();
            $("#formLicencia").hide();
            alertify.alert("Warning!", "Operador no encontrado");
          }
        })
        .fail(function () {
          alertify.alert("Warning!", "Error al cargar el formulario");
        });
    },
  });
});
