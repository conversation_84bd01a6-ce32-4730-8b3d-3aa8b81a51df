$(document).ready(function () {
  $("#buscadorOp").focus();
  if ($.get("resp")) {
    Rsp = $.get("resp");
    switch (Rsp) {
      case "0":
        rmsg = "Error al guardar los datos";
        break;
      case "1":
        rmsg = "Datos guardados correctamente";
        break;
    }
    alertify.alert("Servitaxis Sitio 152", rmsg);
  }

  $("#fechaDeposito").datepicker({
    dateFormat: "yy-mm-dd",
    maxDate: "0",
    showWeek: true,
    beforeShowDay: function (date) {
      return [!date.getDay() == 0];
    },
  });
  $("#divFechaDeposito").hide();
  if ($.get("deposito") && $.get("deposito") == 1) {
    $("#divFechaDeposito").show();
    $("#depositoFlag").val(1);
  }

  $("#concepto").miCatalogo("concepto", "Catalogo concepto");
  $("#cuenta").miCatalogo("vistaCuenta", "Catalogo Cuentas Bancarias");
  $("#buscadorOp").cambiaMayus();
  $(
    "#divAgregaConcepto, #semanaDiv, #anioDiv, #agregaDiv, #importeDiv, #reciboConceptos"
  ).hide();

  // Formatear el importe a dos decimales al perder el foco
  $("#importe").on("blur", function () {
    let value = parseFloat(this.value);
    if (!isNaN(value) && value >= 0) {
      this.value = value.toFixed(2);
    } else {
      this.value = "";
    }
  });

  $("#concepto").change(function (e) {
    e.preventDefault();
    if ($(this).val()) {
      // Mostrar los campos de importe, semana, año y botón de agregar
      $("#importeDiv, #semanaDiv, #anioDiv, #agregaDiv").show();
      // Establecer valores predeterminados para semana y año
      let today = new Date();
      let year = today.getFullYear();
      let startOfYear = new Date(year, 0, 1);
      let weekNumber = Math.ceil((((today - startOfYear) / 86400000) + startOfYear.getDay() + 1) / 7);
      $("#semana").val(weekNumber);
      $("#anio").val(year);
      // Limpiar el campo de última semana
      $("#ultimaSemana").empty();
    } else {
      $("#importeDiv, #semanaDiv, #anioDiv, #agregaDiv").hide();
    }
  });

  $("#reciboConceptos").validate();

  let total = 0; // Inicializar total
  $("#conceptoAgrega").validate({
    submitHandler: function (f) {
      let semana = parseInt($("#semana").val());
      let concepto = $("#concepto").val();
      let importe = parseFloat($("#importe").val());
      let anio = parseInt($("#anio").val());
      let operador = $("#idOperador").val();
      let conceptoText = $("#concepto option:selected").text();

      // Depuración
      console.log("Importe:", importe);
      console.log("Semana:", semana);
      console.log("Año:", anio);
      console.log("Concepto:", concepto);
      console.log("Operador:", operador);

      // Validar que los campos sean válidos
      if (isNaN(importe) || importe <= 0) {
        alertify.alert("Servitaxis Sitio 152", "Por favor, ingrese un importe válido mayor a 0.");
        return;
      }
      if (isNaN(semana) || semana < 1 || semana > 53) {
        alertify.alert("Servitaxis Sitio 152", "Por favor, ingrese una semana válida (1-53).");
        return;
      }
      if (isNaN(anio) || anio < 2000 || anio > new Date().getFullYear()) {
        alertify.alert("Servitaxis Sitio 152", "Por favor, ingrese un año válido.");
        return;
      }
      if (!concepto) {
        alertify.alert("Servitaxis Sitio 152", "Por favor, seleccione un concepto.");
        return;
      }
      if (!operador) {
        alertify.alert("Servitaxis Sitio 152", "Por favor, seleccione un operador primero.");
        return;
      }

      let importeText = importe.toFixed(2); // Formatear a dos decimales
      console.log("Importe formateado:", importeText);

      if ($("#flexCondonacion").prop("checked")) {
        condona = "C";
        condonaImage = "images/checked.png";
      } else {
        condona = "A";
        condonaImage = "images/unchecked.png";
        total += parseFloat(importeText); // Sumar al total
      }
      $("#operadorID").val(operador);

      let numId = $("#listaConceptos").children("tbody").children("tr").length;

      $("#reciboConceptos").show();
      $("#listaConceptos").append(
        '<tr id="fila' +
          numId +
          concepto +
          '">' +
          "<th></th>" +
          '<td><input type="hidden" name="conceptos[]" value="' +
          concepto +
          '">' +
          conceptoText +
          "</td>" +
          '<td><input type="hidden" name="importes[]" value="' +
          importeText +
          '">$ ' +
          importeText +
          "</td>" +
          '<td><input type="hidden" name="semanas[]" value="' +
          semana +
          '">' +
          semana +
          "</td>" +
          '<td><input type="hidden" name="anios[]" value="' +
          anio +
          '">' +
          anio +
          "</td>" +
          '<td><input type="hidden" name="condona[]" id="condona' +
          numId +
          concepto +
          semana +
          '" value="' +
          condona +
          '"><img class="elimina" src="' +
          condonaImage +
          '" width="25" height="25">' +
          "</td>" +
          '<td><a href="#"><img class="elimina" src="images/delete.png" width="25" height="25"></a></td>' +
          "</tr>"
      );
      $("#listaConceptos").enumera();
      $("#totalRecibo")
        .empty()
        .html("$ " + total.toFixed(2));

      console.log("Total después de agregar:", total);

      // Limpiar formulario
      $("#flexCondonacion").prop("checked", false);
      $("#semanaDiv, #anioDiv, #agregaDiv, #importeDiv").hide();
      $("#concepto").val("");
      $("#importe").val("");
      $("#semana").val("");
      $("#anio").val("");

      $(".elimina").off("click").on("click", function (e) {
        e.preventDefault();
        let id = $(this).parents("tr").attr("id");
        let importeFila = parseFloat(
          $(this).parents("tr").find('input[name="importes[]"]').val()
        );
        let condonaFila = $(this).parents("tr").find('input[name="condona[]"]').val();

        console.log("Eliminando fila:", id);
        console.log("Importe fila:", importeFila);
        console.log("Condona fila:", condonaFila);

        $(`#${id}`).remove();

        if (condonaFila === "A" && !isNaN(importeFila)) {
          total -= importeFila;
        }

        $("#totalRecibo")
          .empty()
          .html("$ " + total.toFixed(2));

        console.log("Total después de eliminar:", total);

        $("#listaConceptos").enumera();

        if ($("#listaConceptos tbody").children("tr").length == 0) {
          $("#reciboConceptos").hide();
          total = 0; // Reiniciar total si no hay filas
        }
      });
    },
  });

  $("#busqueda").validate({
    submitHandler: function (form) {
      let datoOP = $("#buscadorOp").val();
      $.ajax({
        type: "post",
        url: "php/ajax/buscaOperador.php",
        data: { datoBusca: datoOP },
      })
        .done(function (r) {
          R = $.parseJSON(r);
          if (R[0] != 0) {
            semanaActual = R.semanaSig;
            anioSemana = R.anioSemana;
            const { inicio, fin } = R.semInicioFin;
            delete R.semanaSig;
            delete R.anioSemana;
            delete R.semInicioFin;
            datos = R[0];
            $("#divAgregaConcepto").show();
            $("#operadorName").empty().html(datos.nombreCompleto);
            $("#contableNum").empty().html(datos.contable);
            $("#idOperador").val(datos.idOperador);
            $("#semanaActual")
              .empty()
              .html(`Semana ${semanaActual} del ${inicio} al ${fin}`);
          } else {
            $("#operadorName").empty();
            $("#contableNum").empty();
            $("#idOperador").val("");
            $("#semanaActual").empty();
            $("#divAgregaConcepto").hide();
            alertify.alert("Warning!", "Operador no encontrado");
          }
        })
        .fail(function () {
          alertify.alert("Warning!", "Error al cargar el formulario");
        });
    },
  });
});