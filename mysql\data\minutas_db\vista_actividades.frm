TYPE=VIEW
query=select `a`.`id` AS `id`,`a`.`actividad_id` AS `actividad_id`,`a`.`tarea` AS `tarea`,`a`.`tema` AS `tema`,`a`.`prioridad` AS `prioridad`,`a`.`fecha_hora_compromiso` AS `fecha_hora_compromiso`,`a`.`responsable` AS `responsable`,`a`.`progreso` AS `progreso`,`a`.`comentarios` AS `comentarios`,`a`.`fecha_creacion` AS `fecha_creacion`,`a`.`fecha_modificacion` AS `fecha_modificacion`,case when `a`.`progreso` = \'Completado\' then \'Completado\' when `a`.`progreso` = \'En Progreso\' then \'En Progreso\' when `a`.`fecha_hora_compromiso` < current_timestamp() and `a`.`progreso` = \'Vigente\' then \'Vencido\' else `a`.`progreso` end AS `estado_calculado`,to_days(`a`.`fecha_hora_compromiso`) - to_days(current_timestamp()) AS `dias_restantes` from `minutas_db`.`actividades` `a` order by case `a`.`prioridad` when \'Alta\' then 1 when \'Media\' then 2 when \'Baja\' then 3 end,`a`.`fecha_hora_compromiso`
md5=6af82eebcd668165f8fed68192e49409
updatable=1
algorithm=0
definer_user=root
definer_host=localhost
suid=2
with_check_option=0
timestamp=0001749245974973208
create-version=2
source=SELECT 
\n    a.id,
\n    a.actividad_id,
\n    a.tarea,
\n    a.tema,
\n    a.prioridad,
\n    a.fecha_hora_compromiso,
\n    a.responsable,
\n    a.progreso,
\n    a.comentarios,
\n    a.fecha_creacion,
\n    a.fecha_modificacion,
\n    CASE 
\n        WHEN a.progreso = \'Completado\' THEN \'Completado\'
\n        WHEN a.progreso = \'En Progreso\' THEN \'En Progreso\'
\n        WHEN a.fecha_hora_compromiso < NOW() AND a.progreso = \'Vigente\' THEN \'Vencido\'
\n        ELSE a.progreso
\n    END AS estado_calculado,
\n    DATEDIFF(a.fecha_hora_compromiso, NOW()) as dias_restantes
\nFROM actividades a
\nORDER BY 
\n    CASE a.prioridad 
\n        WHEN \'Alta\' THEN 1 
\n        WHEN \'Media\' THEN 2 
\n        WHEN \'Baja\' THEN 3 
\n    END,
\n    a.fecha_hora_compromiso ASC
client_cs_name=utf8mb4
connection_cl_name=utf8mb4_unicode_ci
view_body_utf8=select `a`.`id` AS `id`,`a`.`actividad_id` AS `actividad_id`,`a`.`tarea` AS `tarea`,`a`.`tema` AS `tema`,`a`.`prioridad` AS `prioridad`,`a`.`fecha_hora_compromiso` AS `fecha_hora_compromiso`,`a`.`responsable` AS `responsable`,`a`.`progreso` AS `progreso`,`a`.`comentarios` AS `comentarios`,`a`.`fecha_creacion` AS `fecha_creacion`,`a`.`fecha_modificacion` AS `fecha_modificacion`,case when `a`.`progreso` = \'Completado\' then \'Completado\' when `a`.`progreso` = \'En Progreso\' then \'En Progreso\' when `a`.`fecha_hora_compromiso` < current_timestamp() and `a`.`progreso` = \'Vigente\' then \'Vencido\' else `a`.`progreso` end AS `estado_calculado`,to_days(`a`.`fecha_hora_compromiso`) - to_days(current_timestamp()) AS `dias_restantes` from `minutas_db`.`actividades` `a` order by case `a`.`prioridad` when \'Alta\' then 1 when \'Media\' then 2 when \'Baja\' then 3 end,`a`.`fecha_hora_compromiso`
mariadb-version=100432
