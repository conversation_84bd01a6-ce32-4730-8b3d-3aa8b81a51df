<?php
include_once("conexion.php");
include_once("configDB.php");
include_once("session.php");
include_once("classEconomico.php");

$back = $_SERVER['HTTP_REFERER'];

if (isset($_POST['idEconomico'])) {
    $idEconomico = $_POST['idEconomico'];

    $taxiConect = new foo_mysqli("localhost", USER, PASS, DBNAME);
    $taxiConect->query("SET NAMES utf8");

    $ecoDown = new economico("", "", "", "", "", "", "", 'I', "", "");
    $ecoDown->identificador = $idEconomico;

    $aux = 1;
    if (!$ecoDown->baja($taxiConect)) {
        $aux = 0;
    }

    $taxiConect->close();
} else {
    $aux = 0;
}

header("Location: $back?res=$aux");
