-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 4.8.5
-- https://www.phpmyadmin.net/
--
-- Servidor: 127.0.0.1
-- Tiempo de generación: 28-10-2021 a las 03:19:27
-- Versión del servidor: 10.1.38-MariaDB
-- Versión de PHP: 5.6.40

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
SET AUTOCOMMIT = 0;
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Base de datos: `control`
--

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `abono`
--

CREATE TABLE `abono` (
  `idAbono` int(11) NOT NULL,
  `idUsuario` int(11) NOT NULL,
  `idOperador` int(11) NOT NULL,
  `idImporte` int(11) NOT NULL,
  `idRecibo` char(10) NOT NULL,
  `semana` int(2) NOT NULL,
  `anio` char(4) NOT NULL,
  `tipoAbono` enum('A','C','V') DEFAULT NULL COMMENT 'abono,condonacion,convenio',
  `observaciones` varchar(200) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Volcado de datos para la tabla `abono`
--

INSERT INTO `abono` (`idAbono`, `idUsuario`, `idOperador`, `idImporte`, `idRecibo`, `semana`, `anio`, `tipoAbono`, `observaciones`) VALUES
(1, 1, 1, 5, '2021070001', 30, '2021', 'A', NULL),
(2, 1, 1, 5, '2021080001', 31, '2021', 'A', NULL),
(3, 1, 1, 5, '2021100001', 32, '2021', 'A', NULL),
(4, 1, 1, 6, '2021100001', 40, '2021', 'A', NULL),
(5, 1, 1, 7, '2021100001', 40, '2021', 'C', NULL),
(6, 1, 1, 3, '2021100001', 40, '2021', 'A', NULL);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `cargo`
--

CREATE TABLE `cargo` (
  `idCargo` int(11) NOT NULL,
  `idOperador` int(11) NOT NULL,
  `idUsuario` int(11) NOT NULL,
  `monto` decimal(8,2) NOT NULL,
  `numeroCheque` char(10) NOT NULL,
  `tipoCargo` enum('C','P') NOT NULL COMMENT 'convenio, prestamo',
  `fechaRegistro` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Volcado de datos para la tabla `cargo`
--

INSERT INTO `cargo` (`idCargo`, `idOperador`, `idUsuario`, `monto`, `numeroCheque`, `tipoCargo`, `fechaRegistro`) VALUES
(1, 1, 1, '9000.00', '123456879', 'P', '2021-10-21 23:29:03');

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `concepto`
--

CREATE TABLE `concepto` (
  `idConcepto` int(11) NOT NULL,
  `descripcion` varchar(100) NOT NULL,
  `fechaRegistro` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Volcado de datos para la tabla `concepto`
--

INSERT INTO `concepto` (`idConcepto`, `descripcion`, `fechaRegistro`) VALUES
(1, 'Prueba', '2021-09-15 02:31:55'),
(2, 'Caseta', '2021-09-15 02:36:30'),
(3, 'Seguro', '2021-09-15 02:37:45'),
(4, 'PRUEBA 3', '2021-09-15 02:38:15');

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `economico`
--

CREATE TABLE `economico` (
  `idEconomico` int(11) NOT NULL,
  `idOwner` int(11) NOT NULL,
  `marca` varchar(30) NOT NULL,
  `modelo` varchar(30) NOT NULL,
  `placa` char(10) NOT NULL,
  `anio` char(4) DEFAULT NULL,
  `fechaRegistro` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Estructura Stand-in para la vista `fechaultimadesactivacion`
-- (Véase abajo para la vista actual)
--
CREATE TABLE `fechaultimadesactivacion` (
`idConcepto` int(11)
,`fechaDesactivado` timestamp
);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `importe`
--

CREATE TABLE `importe` (
  `idImporte` int(11) NOT NULL,
  `idConcepto` int(11) NOT NULL,
  `monto` decimal(8,2) NOT NULL,
  `fechaRegistro` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `estatus` enum('A','I') NOT NULL,
  `fechaUpdate` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Volcado de datos para la tabla `importe`
--

INSERT INTO `importe` (`idImporte`, `idConcepto`, `monto`, `fechaRegistro`, `estatus`, `fechaUpdate`) VALUES
(1, 1, '100.00', '2021-09-15 02:31:55', 'I', '2021-10-27 22:55:24'),
(2, 1, '200.00', '2021-09-15 02:31:55', 'I', '2021-10-27 22:55:24'),
(3, 1, '300.00', '2021-09-15 02:31:55', 'I', '2021-10-27 22:55:24'),
(4, 1, '400.00', '2021-09-15 02:31:55', 'I', '2021-10-27 22:55:24'),
(5, 2, '500.00', '2021-09-15 02:36:30', 'A', NULL),
(6, 3, '100.00', '2021-09-15 02:37:45', 'A', NULL),
(7, 4, '90.00', '2021-09-15 02:38:15', 'I', '2021-10-27 23:12:09'),
(8, 1, '350.00', '2021-10-27 22:55:24', 'A', NULL),
(9, 1, '400.00', '2021-10-27 22:55:24', 'A', NULL),
(10, 1, '600.00', '2021-10-27 22:55:24', 'A', NULL),
(11, 4, '250.00', '2021-10-27 23:12:09', 'A', NULL),
(12, 4, '375.00', '2021-10-27 23:12:09', 'A', NULL);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `importeconvenio`
--

CREATE TABLE `importeconvenio` (
  `idImporteConvenio` int(11) NOT NULL,
  `monto` decimal(6,2) NOT NULL,
  `fechaRegistro` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `fechaBaja` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Volcado de datos para la tabla `importeconvenio`
--

INSERT INTO `importeconvenio` (`idImporteConvenio`, `monto`, `fechaRegistro`, `fechaBaja`) VALUES
(1, '250.00', '2021-10-26 23:08:44', NULL);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `licencia`
--

CREATE TABLE `licencia` (
  `idLicencia` int(11) NOT NULL,
  `idOperador` int(11) NOT NULL,
  `vigenciaInicio` date DEFAULT NULL,
  `vigenciaFin` date NOT NULL,
  `fechaRegistro` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `operador`
--

CREATE TABLE `operador` (
  `idOperador` int(11) NOT NULL,
  `nombre` varchar(30) NOT NULL,
  `apellidoPaterno` varchar(30) NOT NULL,
  `apellidoMaterno` varchar(30) NOT NULL,
  `contable` char(3) NOT NULL,
  `referenciado` char(6) NOT NULL,
  `celular` char(10) NOT NULL,
  `estado` enum('A','I') NOT NULL DEFAULT 'A',
  `fechaRegistro` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Volcado de datos para la tabla `operador`
--

INSERT INTO `operador` (`idOperador`, `nombre`, `apellidoPaterno`, `apellidoMaterno`, `contable`, `referenciado`, `celular`, `estado`, `fechaRegistro`) VALUES
(1, 'EDCEL', 'FUERTE', 'MARTÍNEZ', '335', '234343', '5513548968', 'A', '2021-09-14 20:40:09');

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `operadoreconomico`
--

CREATE TABLE `operadoreconomico` (
  `idOperador` int(11) NOT NULL,
  `idEconomico` int(11) NOT NULL,
  `fechaRegsitro` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Estructura Stand-in para la vista `operadorview`
-- (Véase abajo para la vista actual)
--
CREATE TABLE `operadorview` (
`idOperador` int(11)
,`nombreCompleto` varchar(92)
,`contable` char(3)
,`referenciado` char(6)
);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `owner`
--

CREATE TABLE `owner` (
  `idOwner` int(11) NOT NULL,
  `nombre` varchar(30) NOT NULL,
  `apellidoPaterno` varchar(30) NOT NULL,
  `apellidoMaterno` varchar(30) NOT NULL,
  `telefono` char(10) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `recibo`
--

CREATE TABLE `recibo` (
  `idRecibo` char(10) NOT NULL,
  `estado` enum('A','P') NOT NULL DEFAULT 'A' COMMENT 'adeudo, pagado',
  `efectivo` decimal(8,2) NOT NULL,
  `cheque` decimal(8,2) NOT NULL,
  `numeroCheque` varchar(10) DEFAULT NULL,
  `fechaRegistro` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `fechaPago` timestamp NULL DEFAULT NULL,
  `fechaDeposito` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Volcado de datos para la tabla `recibo`
--

INSERT INTO `recibo` (`idRecibo`, `estado`, `efectivo`, `cheque`, `numeroCheque`, `fechaRegistro`, `fechaPago`, `fechaDeposito`) VALUES
('2021070001', 'P', '500.00', '0.00', NULL, '2021-09-29 02:44:04', '2021-09-29 02:44:04', NULL),
('2021080001', 'P', '0.00', '500.00', '345345643', '2021-09-29 02:44:04', '2021-09-29 02:45:05', NULL),
('2021100001', 'P', '700.00', '500.00', '595959955', '2021-10-04 02:14:31', '2021-10-20 01:28:39', NULL);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `seguro`
--

CREATE TABLE `seguro` (
  `idSeguro` int(11) NOT NULL,
  `idEconomico` int(11) NOT NULL,
  `poliza` varchar(45) NOT NULL,
  `vigenciaInicio` datetime NOT NULL,
  `vigenciaFin` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Estructura Stand-in para la vista `totalrecibo`
-- (Véase abajo para la vista actual)
--
CREATE TABLE `totalrecibo` (
`idRecibo` char(10)
,`Total` decimal(30,2)
,`Pago` decimal(9,2)
,`Cambio` decimal(31,2)
);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `usuario`
--

CREATE TABLE `usuario` (
  `idUsuario` int(11) NOT NULL,
  `nombre` varchar(30) NOT NULL,
  `apellidoPaterno` varchar(30) NOT NULL,
  `apellidoMaterno` varchar(30) NOT NULL,
  `estatus` enum('A','I') NOT NULL,
  `tipoUsuario` enum('A','C','J') NOT NULL,
  `fechaRegistro` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Volcado de datos para la tabla `usuario`
--

INSERT INTO `usuario` (`idUsuario`, `nombre`, `apellidoPaterno`, `apellidoMaterno`, `estatus`, `tipoUsuario`, `fechaRegistro`) VALUES
(1, 'Lupe', 'XXXX', 'YYYY', 'A', 'C', '2021-09-20 03:37:00');

-- --------------------------------------------------------

--
-- Estructura para la vista `fechaultimadesactivacion`
--
DROP TABLE IF EXISTS `fechaultimadesactivacion`;

CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `fechaultimadesactivacion`  AS  select `i`.`idConcepto` AS `idConcepto`,max(`i`.`fechaUpdate`) AS `fechaDesactivado` from `importe` `i` where (`i`.`estatus` = 'I') group by `i`.`idConcepto` ;

-- --------------------------------------------------------

--
-- Estructura para la vista `operadorview`
--
DROP TABLE IF EXISTS `operadorview`;

CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `operadorview`  AS  select `operador`.`idOperador` AS `idOperador`,concat(`operador`.`nombre`,' ',`operador`.`apellidoPaterno`,' ',`operador`.`apellidoMaterno`) AS `nombreCompleto`,`operador`.`contable` AS `contable`,`operador`.`referenciado` AS `referenciado` from `operador` ;

-- --------------------------------------------------------

--
-- Estructura para la vista `totalrecibo`
--
DROP TABLE IF EXISTS `totalrecibo`;

CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `totalrecibo`  AS  select `r`.`idRecibo` AS `idRecibo`,sum(`i`.`monto`) AS `Total`,(`r`.`efectivo` + `r`.`cheque`) AS `Pago`,((`r`.`efectivo` + `r`.`cheque`) - sum(`i`.`monto`)) AS `Cambio` from ((`abono` `a` join `recibo` `r` on((`a`.`idRecibo` = `r`.`idRecibo`))) join `importe` `i` on((`a`.`idImporte` = `i`.`idImporte`))) where (`a`.`tipoAbono` = 'A') group by `r`.`idRecibo` ;

--
-- Índices para tablas volcadas
--

--
-- Indices de la tabla `abono`
--
ALTER TABLE `abono`
  ADD PRIMARY KEY (`idAbono`),
  ADD KEY `fkAbonoRecibo` (`idRecibo`),
  ADD KEY `fkAbonoImporte` (`idImporte`),
  ADD KEY `fkAbonoOperador` (`idOperador`),
  ADD KEY `fkAbonoUsuario` (`idUsuario`);

--
-- Indices de la tabla `cargo`
--
ALTER TABLE `cargo`
  ADD PRIMARY KEY (`idCargo`);

--
-- Indices de la tabla `concepto`
--
ALTER TABLE `concepto`
  ADD PRIMARY KEY (`idConcepto`);

--
-- Indices de la tabla `economico`
--
ALTER TABLE `economico`
  ADD PRIMARY KEY (`idEconomico`),
  ADD KEY `fkEconomicoOwner` (`idOwner`);

--
-- Indices de la tabla `importe`
--
ALTER TABLE `importe`
  ADD PRIMARY KEY (`idImporte`),
  ADD KEY `fkImporteConcepto` (`idConcepto`);

--
-- Indices de la tabla `importeconvenio`
--
ALTER TABLE `importeconvenio`
  ADD PRIMARY KEY (`idImporteConvenio`);

--
-- Indices de la tabla `licencia`
--
ALTER TABLE `licencia`
  ADD PRIMARY KEY (`idLicencia`),
  ADD KEY `fkLicenciaOperador` (`idOperador`);

--
-- Indices de la tabla `operador`
--
ALTER TABLE `operador`
  ADD PRIMARY KEY (`idOperador`);

--
-- Indices de la tabla `operadoreconomico`
--
ALTER TABLE `operadoreconomico`
  ADD PRIMARY KEY (`idOperador`,`idEconomico`),
  ADD KEY `fkOperadorEconomicoEconomico` (`idEconomico`);

--
-- Indices de la tabla `owner`
--
ALTER TABLE `owner`
  ADD PRIMARY KEY (`idOwner`);

--
-- Indices de la tabla `recibo`
--
ALTER TABLE `recibo`
  ADD PRIMARY KEY (`idRecibo`);

--
-- Indices de la tabla `seguro`
--
ALTER TABLE `seguro`
  ADD PRIMARY KEY (`idSeguro`),
  ADD KEY `fkSeguroEconomico` (`idEconomico`);

--
-- Indices de la tabla `usuario`
--
ALTER TABLE `usuario`
  ADD PRIMARY KEY (`idUsuario`);

--
-- AUTO_INCREMENT de las tablas volcadas
--

--
-- AUTO_INCREMENT de la tabla `abono`
--
ALTER TABLE `abono`
  MODIFY `idAbono` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- AUTO_INCREMENT de la tabla `cargo`
--
ALTER TABLE `cargo`
  MODIFY `idCargo` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT de la tabla `concepto`
--
ALTER TABLE `concepto`
  MODIFY `idConcepto` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT de la tabla `importe`
--
ALTER TABLE `importe`
  MODIFY `idImporte` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=13;

--
-- AUTO_INCREMENT de la tabla `importeconvenio`
--
ALTER TABLE `importeconvenio`
  MODIFY `idImporteConvenio` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT de la tabla `operador`
--
ALTER TABLE `operador`
  MODIFY `idOperador` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT de la tabla `owner`
--
ALTER TABLE `owner`
  MODIFY `idOwner` int(11) NOT NULL AUTO_INCREMENT;

--
-- Restricciones para tablas volcadas
--

--
-- Filtros para la tabla `abono`
--
ALTER TABLE `abono`
  ADD CONSTRAINT `fkAbonoImporte` FOREIGN KEY (`idImporte`) REFERENCES `importe` (`idImporte`) ON UPDATE CASCADE,
  ADD CONSTRAINT `fkAbonoOperador` FOREIGN KEY (`idOperador`) REFERENCES `operador` (`idOperador`) ON UPDATE CASCADE,
  ADD CONSTRAINT `fkAbonoRecibo` FOREIGN KEY (`idRecibo`) REFERENCES `recibo` (`idRecibo`) ON UPDATE CASCADE,
  ADD CONSTRAINT `fkAbonoUsuario` FOREIGN KEY (`idUsuario`) REFERENCES `usuario` (`idUsuario`) ON UPDATE CASCADE;

--
-- Filtros para la tabla `economico`
--
ALTER TABLE `economico`
  ADD CONSTRAINT `fkEconomicoOwner` FOREIGN KEY (`idOwner`) REFERENCES `owner` (`idOwner`) ON UPDATE CASCADE;

--
-- Filtros para la tabla `importe`
--
ALTER TABLE `importe`
  ADD CONSTRAINT `fkImporteConcepto` FOREIGN KEY (`idConcepto`) REFERENCES `concepto` (`idConcepto`) ON UPDATE CASCADE;

--
-- Filtros para la tabla `licencia`
--
ALTER TABLE `licencia`
  ADD CONSTRAINT `fkLicenciaOperador` FOREIGN KEY (`idOperador`) REFERENCES `operador` (`idOperador`) ON UPDATE CASCADE;

--
-- Filtros para la tabla `operadoreconomico`
--
ALTER TABLE `operadoreconomico`
  ADD CONSTRAINT `fkOperadorEconomicoEconomico` FOREIGN KEY (`idEconomico`) REFERENCES `economico` (`idEconomico`) ON UPDATE CASCADE,
  ADD CONSTRAINT `fkOperadorEconomicoOperador` FOREIGN KEY (`idOperador`) REFERENCES `operador` (`idOperador`) ON UPDATE CASCADE;

--
-- Filtros para la tabla `seguro`
--
ALTER TABLE `seguro`
  ADD CONSTRAINT `fkSeguroEconomico` FOREIGN KEY (`idEconomico`) REFERENCES `economico` (`idEconomico`) ON UPDATE CASCADE;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
