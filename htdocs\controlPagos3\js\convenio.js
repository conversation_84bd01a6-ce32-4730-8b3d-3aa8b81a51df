$(document).ready(function () {
  if ($.get("cuotaConvenio")) {
    const idImporteConvenio = $.get("cuotaConvenio");
    if ($.get("resp")) {
      Rsp = $.get("resp");
      switch (Rsp) {
        case "0":
          rmsg = "Error al guardar los datos";
          break;
        case "1":
          rmsg = "Datos guardados correctamente";
          break;
      }
      alertify.alert("Servitaxis Sitio 152", rmsg);
    }

    $("#agregaDiv,#semanasDiv,#importeDiv,#notaDiv").hide();
    $(".card-body").hide();
    $("#convenioForm").hide();

    $("#concepto")
      .miCatalogo("concepto", "Catalogo concepto")
      .change(function (e) {
        e.preventDefault();
        idconcepto = $(this).val();
        operador = $("#operadorID").val();
        $.ajax({
          type: "post",
          url: "php/ajax/buscaImporte.php",
          data: { concepto: idconcepto, idOperado: operador, importeA: 1 },
        })
          .done(function (r) {
            R = $.parseJSON(r);
            $("#importeDiv").show();
            if (R[0] != 0) {
              semana = R.semana;
              anio = R.anio;
              semanaSig = R.semanaSig;
              anioSemana = R.anioSemana;
              delete R.semana;
              delete R.anio;
              delete R.semanaSig;
              delete R.anioSemana;
              delete R[0];

              extra = "";
              if (R.antiguo) {
                oldImport = R.antiguo;
                delete R.antiguo;
                for (var x in oldImport) {
                  extra = `${extra}<option class="text-danger" value="${oldImport[x].idImporte}">${oldImport[x].monto}</option>`;
                }
              }

              mySelect = '<option value="">Elegir importe ...</option>';

              for (var key in R) {
                mySelect = `${mySelect}<option value="${R[key].idImporte}">${R[key].monto}</option>`;
              }
              mySelect = mySelect + extra;
              $("#importe").empty().append(mySelect);

              if (semana == null) {
                msgUltima = "No hay datos para este concepto";
              } else {
                msgUltima =
                  "Ultima semana pagada: " + semana + " del año: " + anio;
              }
              $("#ultimaSemana").empty().html(msgUltima);

              $("#agregaDiv,#semanasDiv,#importeDiv,#notaDiv").show();
              $("#desdeWeek").val(semanaSig);
              $("#anio").val(anioSemana);
            } else {
              alertify.alert(
                "Servitaxis Sitio 152",
                "El concepto no tiene importes activos agregue uno antes de continuar"
              );
            }
          })
          .fail(function () {
            alertify.alert("Warning!", "Error al cargar el formulario.");
          });
      });

    $.ajax({
      type: "post",
      url: "php/ajax/importeConvenio.php",
      data: { valor: idImporteConvenio },
    })
      .done(function (r) {
        R = $.parseJSON(r);
        if (R[0] != 0) {
          $("#idConvenioImporte").val(R.idImporte);
          $("#importeConvenio").val(R.monto);
          $("#textImporte").html(
            `$ ${parseFloat(R.monto).toFixed(2)} <br> Pago Semanal`
          );
        } else {
          console.log("ERROR!");
        }
      })
      .fail(function () {
        alertify.alert("Warning!", "Error al cargar el formulario.");
      });

    total = 0;
    semTotal = 0;
    $("#dataConvenio").validate({
      rules: {
        hastaWeek: {
          biggerThan: true,
        },
      },
      submitHandler: function (form) {
        montoPagoSem = parseInt($("#importeConvenio").val());
        desde = $("#desdeWeek").val();
        hasta = $("#hastaWeek").val();
        concepto = $("#concepto").val();
        importe = $("#importe").val();
        anio = $("#anio").val();
        operador = $("#operadorID").val();
        conceptoText = $("#concepto option:selected").text();
        importeText = $("#importe option:selected").text();

        numeroSemanas = parseInt(hasta) - parseInt(desde) + 1;
        adeudo = numeroSemanas * parseFloat(importeText);
        semanasPagar = Math.ceil(adeudo / montoPagoSem);
        total = total + parseFloat(adeudo);
        semTotal = semTotal + semanasPagar;
        numId = $("#listaConceptos").children("tbody").children("tr").length;

        $("#convenioForm").show();

        $("#listaConceptos").append(`<tr id="fila${numId}${concepto}">
          <th></th>
          <td><input type="hidden" name="concepto[]" value="${concepto}">${conceptoText}</td>
          <td><input type="hidden" name="importe[]" value="${importe}">$ ${importeText}</td>
          <td><input type="hidden" name="desdeW[]" value="${desde}">${desde}</td>
          <td><input type="hidden" name="hastaW[]" value="${hasta}">${hasta}</td>
          <td><input type="hidden" name="anio[]" value="${anio}">${anio}</td>
          <td><input type="hidden" name="semanasTotal[]" value="${numeroSemanas}">${numeroSemanas}</td>
          <td><input type="hidden" name="importeTotal[]" value="${adeudo}">$ ${parseFloat(
          adeudo
        )}</td>
          <td><input type="hidden" name="semanaPagar[]" value="${semanasPagar}">${semanasPagar}</td>
          <td><a href="#"><img class="elimina" src="images/delete.png" width="25" height="25"></a></td>
          </tr>`);

        $("#listaConceptos").enumera();
        $("#agregaDiv,#semanasDiv,#importeDiv,#notaDiv").hide();
        $("#totalRecibo")
          .empty()
          .html(`$ ${parseFloat(total)}`);
        $("#semanasTotal").empty().html(`${semTotal}`);

        $("#concepto").val("");
        $("#desdeWeek").val("");
        $("#hastaWeek").val("");
        $("#ultimaSemana").empty();

        $(".elimina").on("click", function (e) {
          e.preventDefault();
          id = $(this).parents("tr").attr("id");
          $(this).parents("tr").remove();

          filasList = $("#listaConceptos").children("tbody").children("tr");
          total = 0;
          semTotal = 0;
          console.log(x);
          for (var x in filasList) {
            aux = $("#" + filasList[x].id).children("td");
            if (aux.length == 0) {
              importe = 0;
              s = 0;
            } else {
              importe = datoColumna(6, aux);
              s = datoColumna(7, aux);
            }
            total = total + importe;
            semTotal = semTotal + s;
          }

          semTotal = parseInt(semTotal);
          $("#totalRecibo")
            .empty()
            .html("$ " + total);
          $("#semanasTotal").empty().html(`${semTotal}`);

          $("#listaConceptos").enumera();

          if ($("#listaConceptos tbody").children("tr").length == 0) {
            $("#convenioForm").hide();
          }
        });
      },
    });

    $("#busqueda").validate({
      submitHandler: function (form) {
        datoOP = $("#buscadorOp").val();
        $.ajax({
          type: "post",
          url: "php/ajax/buscaOperador.php",
          data: { datoBusca: datoOP },
        })
          .done(function (r) {
            R = $.parseJSON(r);
            if (R[0] != 0) {
              semanaActual = R.semanaSig;
              anioSemana = R.anioSemana;
              const { inicio, fin } = R.semInicioFin;
              delete R.semanaSig;
              delete R.anioSemana;
              delete R.semInicioFin;
              datos = R[0];
              $(".card-body").show();
              $("#operador").empty().html(datos.nombreCompleto);
              $("#contable").empty().html(datos.contable);
              $("#operadorID").val(datos.idOperador);
              $("#semanaActual")
                .empty()
                .html(`Semana ${semanaActual} del ${inicio} al ${fin}`);
            } else {
              $("#operador").empty();
              $("#contable").empty();
              $("#operadorID").val("");
              $(".card-body").hide();
              alertify.alert("Warning!", "Operador no encontrado");
            }
          })
          .fail(function () {
            alertify.alert("Warning!", "Error al cargar el formulario");
          });
      },
    });
  } else {
    let myUrl = window.location.origin;
    myUrl = `${myUrl}/selectConvenio.php`;
    $(location).attr("href", myUrl);
  }
});

jQuery.validator.addMethod(
  "biggerThan",
  function (value, element) {
    return (
      this.optional(element) ||
      parseInt(value) > parseInt($("#desdeWeek").val())
    );
  },
  jQuery.validator.format(
    "La semana 'Hasta' no puede se menor o igual a la semana 'Desde'."
  )
);
