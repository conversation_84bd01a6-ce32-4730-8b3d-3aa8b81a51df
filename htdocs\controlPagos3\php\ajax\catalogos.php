<?php
include_once("../conexion.php");
include_once("../configDB.php");

$taxiConect = new foo_mysqli("localhost",USER,PASS,DBNAME);
$taxiConect->query("SET NAMES utf8");

$tabla = $_POST['tabla'];

$qry = "SELECT * FROM $tabla";
$rqry = $taxiConect->query($qry);

$aux = array();

if($rqry->num_rows != 0){
	while($row = $rqry->fetch_array(MYSQLI_BOTH)){
		array_push($aux,$row);
	}
}
else{
	$aux = array("error");
}

echo json_encode($aux);
$taxiConect->close();
