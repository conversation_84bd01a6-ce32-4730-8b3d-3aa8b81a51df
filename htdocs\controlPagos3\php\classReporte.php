<?php
class Reporte
{

	public	$nombre;
	private	$consulta;
	public	$cabeceras = array();
	public	$datos;
	private	$noColumna;
	private $output;

	function __construct($name, $query)
	{
		$this->nombre = $name;
		$this->consulta = $query;

		$hoy = date('Ymd');
		header("Content-Type: text/csv; charset=utf-8");
		header("Content-Disposition: attachment; filename=$this->nombre$hoy.csv");

		$this->output = fopen('php://output', 'w');
	}

	public function putHeaders($total = false)
	{
		while ($info = $this->consulta->fetch_field()) {
			array_push($this->cabeceras, utf8_decode($info->name));
		}

		if ($total) {
			array_push($this->cabeceras, "Total");
		}

		fputcsv($this->output, $this->cabeceras, ',', '"');
	}

	public function otrosHeaders()
	{
		fputcsv($this->output, $this->cabeceras, ',', '"');
	}

	public function otraData()
	{
		fputcsv($this->output, $this->datos, ',', '"');
	}

	public function closeFile()
	{
		fclose($this->output);
	}

	public function putData($total = false, $inicio = null)
	{
		$this->noColumna = $this->consulta->field_count;

		while ($row = $this->consulta->fetch_array(MYSQLI_NUM)) {
			$this->datos = array();
			$suma = 0;
			for ($i = 0; $i < $this->noColumna; $i++) {
				array_push($this->datos, utf8_decode($row[$i]));
				if ($total and $i >= $inicio) {
					$suma += $row[$i];
				}
			}
			if ($total) {
				array_push($this->datos, $suma);
			}
			fputcsv($this->output, $this->datos, ',', '"');
		}
		fclose($this->output);
	}
}
