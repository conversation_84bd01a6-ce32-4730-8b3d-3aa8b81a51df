<?php
include_once("conexion.php");
include_once("configDB.php");
include_once("classProcesaVariable.php");
include_once("classAbono.php");
include_once("classRecibo.php");
include_once("session.php");

$link = '0'; // Valor por defecto para redirección

if (isset($_POST['operadorID']) && isset($_POST['conceptos']) && isset($_POST['importes'])) {
    // Depuración de datos enviados
    error_log("POST data: " . print_r($_POST, true));

    $operadorID = new procesaVariable($_POST['operadorID']);
    $conceptos = new procesaVariable($_POST['conceptos']);
    $importes = new procesaVariable($_POST['importes']);
    $semanas = new procesaVariable($_POST['semanas']);
    $anios = new procesaVariable($_POST['anios']);
    $condona = new procesaVariable($_POST['condona']);
    $observaciones = new procesaVariable($_POST['observaciones']);

    $tipoUsuario = $_SESSION['userType'];
    $idUser = $_SESSION['idUser'];

    $taxiConect = new foo_mysqli("localhost", USER, PASS, DBNAME);
    $taxiConect->query("SET NAMES utf8");

    $auxil = $taxiConect->iniciaTransaccion();

    $myRecibo = new recibo();
    if ($myRecibo->guardaRecibo($taxiConect)) {
        $qObs = "UPDATE recibo SET observaciones = ? WHERE idRecibo=?";
        $stmtObs = $taxiConect->prepare($qObs);
        $obsValue = $observaciones->nulo();
        $stmtObs->bind_param("ss", $obsValue, $myRecibo->identificador);
        $rObs = $stmtObs->execute();
        if (!$rObs) {
            $auxil = 0;
            error_log("Error en UPDATE observaciones: " . $taxiConect->error);
        }

        $n = count($conceptos->valor);
        if ($n === 0) {
            $auxil = 0;
            error_log("Error: No se enviaron conceptos para idRecibo " . $myRecibo->identificador);
        } else {
            for ($i = 0; $i < $n; $i++) {
                $importe = $importes->valor;
                $semana = $semanas->valor;
                $anio = $anios->valor;
                $condonado = $condona->valor;
                $abonos = new abono();
                $abonos->asignaDatos($operadorID->valor, $idUser, $tipoUsuario, $semana[$i], $importe[$i], $anio[$i], $condonado[$i]);
                if (!$abonos->guardaAbono($taxiConect, $myRecibo->identificador)) {
                    $auxil = 0;
                    error_log("Error al guardar abono: " . $taxiConect->error);
                }
            }
        }
    } else {
        $auxil = 0;
        error_log("Error al guardar recibo: " . $taxiConect->error);
    }

    if ($auxil != 0 && isset($_POST['depositoFlag']) && $_POST['depositoFlag'] == 1) {
        $fechaDesposito = new procesaVariable($_POST['fechaDeposito']);
        $movimientoNo = new procesaVariable($_POST['movimientoNo']);
        $cuenta = new procesaVariable($_POST['cuenta']);
        
        // Calcular la suma de todos los importes para efectivo
        $efectivo = is_array($importes->valor) && !empty($importes->valor) ? array_sum(array_map('floatval', $importes->valor)) : 0;
        error_log("importes->valor: " . print_r($importes->valor, true));
        error_log("efectivo: " . $efectivo);

        if ($efectivo <= 0) {
            $auxil = 0;
            error_log("Error: efectivo es 0 o importes->valor no es válido para idRecibo " . $myRecibo->identificador);
        } else {
            $estado = "P";
            $q = "UPDATE recibo SET estado = ?, idUsuario = ?, tipoUsuario = ?, efectivo = ?, fechaPago = ?, fechaDeposito = ?, movimiento = ?, idCuentaBancaria = ? WHERE idRecibo = ?";
            $stmt = $taxiConect->prepare($q);
            $stmt->bind_param("ssssssis", $estado, $idUser, $tipoUsuario, $efectivo, $fechaDesposito->valor, $fechaDesposito->valor, $movimientoNo->valor, $cuenta->valor, $myRecibo->identificador);
            $eq = $stmt->execute();
            if (!$eq) {
                $auxil = 0;
                error_log("Error en UPDATE recibo: " . $taxiConect->error);
            }
        }
        $next = "1&deposito=1";
        $back = "0&deposito=1";
    } else {
        $next = "1";
        $back = "0";
    }

    $link = $taxiConect->finTransaccion($auxil, $next, $back);
    $taxiConect->resetTabla($auxil, "idAbono", "abono");
    $taxiConect->close();

    if ($auxil == 1) {
        $recibo = $myRecibo->identificador;
        include_once("enviaRecibo.php");
    }
} else {
    error_log("Error: POST['operadorID'] o POST['conceptos'] o POST['importes'] no están definidos");
}

header("Location: ../registroConcepto.php?resp=$link");
?>