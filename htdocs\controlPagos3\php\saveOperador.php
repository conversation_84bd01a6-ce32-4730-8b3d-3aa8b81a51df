<?php
include_once("conexion.php");
include_once("configDB.php");
include_once("classProcesaVariable.php");
include_once("classLicencia.php");

if (isset($_POST['nombre']) and isset($_POST['celular'])) {

    $nombre = new procesaVariable($_POST['nombre']);
    $aPaterno = new procesaVariable($_POST['aPaterno']);
    $aMaterno = new procesaVariable($_POST['aMaterno']);
    $contable = new procesaVariable($_POST['contable']);
    $referenciado = new procesaVariable($_POST['referenciado']);
    $celular = new procesaVariable($_POST['celular']);
    $telefono = new procesaVariable($_POST['telefono']);
    $direccion = new procesaVariable($_POST['direccion']);
    $correo = new procesaVariable($_POST['correo']);

    $mailOperador = strtolower($correo->valor);

    $folio = new procesaVariable($_POST['folio']);
    $fechaExpedicion = new procesaVariable($_POST['fechaExpedicion']);
    $fechaVigencia = new procesaVariable($_POST['fechaVigencia']);

    $taxiConect = new foo_mysqli("localhost", USER, PASS, DBNAME);
    $taxiConect->query("SET NAMES utf8");

    $insert = "INSERT INTO operador(idOperador, nombre, apellidoPaterno, apellidoMaterno, contable, referenciado, celular, estado, telefono, domicilio, correo) 
    VALUES (NULL,'$nombre->valor',
    '$aPaterno->valor',
    '$aMaterno->valor',
    '$contable->valor',
    '$referenciado->valor',
    '$celular->valor',
    'A',
    " . $telefono->nulo() . ",
    " . $direccion->nulo() . ",
    '$mailOperador')";
    $exeInsert = $taxiConect->query($insert);

    if ($exeInsert) {
        $idOp = $taxiConect->insert_id;
        $licenciaOP =  new licencia($folio->valor, $fechaExpedicion->valor, $fechaVigencia->valor, $idOp);
        if ($licenciaOP->saveLicencia($taxiConect)) {
            $var = 1;
        } else {
            $var = 0;
        }
    } else {
        $var = '0';
    }

    $taxiConect->close();
} else {
    $var = '0';
}

header("Location: ../newOperador.php?resp=$var");
