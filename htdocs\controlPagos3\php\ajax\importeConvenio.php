<?php
include_once("../conexion.php");
include_once("../configDB.php");
include_once("../classImporte.php");

if (isset($_POST['valor'])) {

	$taxiConect = new foo_mysqli("localhost", USER, PASS, DBNAME);
	$taxiConect->query("SET NAMES utf8");
	//cambiar consulta
	$importe =  new importe(2);
	$importe->identificador = $_POST['valor'];
	$aux = $importe->importeConvenio($taxiConect);

	$taxiConect->close();
} else {
	$aux = array(0);
}

echo json_encode($aux);
