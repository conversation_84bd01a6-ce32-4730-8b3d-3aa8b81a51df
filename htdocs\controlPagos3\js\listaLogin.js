$(document).ready(function () {
  if ($.get("res")) {
    Rsp = $.get("res");
    switch (Rsp) {
      case "0":
        rmsg = "Error en el proceso";
        break;
      case "1":
        rmsg = "Acción realizada correctamente";
        break;
      case "2":
        rmsg =
          "No se pudo activar, modifique el contable en la parte de edición.";
        break;
      default:
        rmsg = "Error al guardar en la base de datos.";
    }
    alertify
      .alert()
      .setting({
        title: "Servitaxis Sitio 152",
        closable: false,
        message: rmsg,
        onok: function () {
          let myUrl = window.location.origin;
          myUrl = `${myUrl}/listaLogin.php`;
          $(location).attr("href", myUrl);
        },
      })
      .show();
  }

  const admin = $("#admin").val();
  $.ajax({
    type: "post",
    url: "php/ajax/listadoLogin.php",
    data: { myVal: admin },
  })
    .done(function (r) {
      R = $.parseJSON(r);
      console.log(R);
      if (R[0] != 0) {
        $.each(R, function (indexInArray, valueOfElement) {
          const {
            idUsuario,
            tipoUsuario,
            Persona,
            usuarioTipo,
            describeEstado,
            estado,
          } = valueOfElement;
          const append = `<tr>
                    <td>${idUsuario}</td>
                    <td>${Persona}</td>
                    <td>${usuarioTipo}</td>
                    <td>
                        <div class="text-center alert alert-${
                          estado == "A" ? "success" : "danger"
                        }" role="alert">
                            ${describeEstado}
                        </div>
                    </td>
                    <td>
                        <button value="${idUsuario}-${
            estado == "A" ? "I" : "A"
          }-${tipoUsuario}" name="myButton" class="btn btn-${
            estado == "A" ? "warning" : "success"
          }">${estado == "A" ? "Desactivar" : "Activar"}</button>
                    </td>
                </tr>`;
          $("#loginList tbody").append(append);
        });
        $("#loginList").DataTable({
          language: {
            url: "//cdn.datatables.net/plug-ins/9dcbecd42ad/i18n/Spanish.json",
          },
        });
        $("#loginList").tableExport({
          formats: ["csv", "txt"],
        });
      }
    })
    .fail(function () {
      alertify.alert("Warning!", "Error al cargar los datos");
    });
});
