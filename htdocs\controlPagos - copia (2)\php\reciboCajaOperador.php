<style type="text/css">
    .headFoot {
        width: 100%;
        margin: 5px auto;
    }

    .headFoot h6 {
        font-size: 10px;
    }

    .bDashRight {
        border-right: 2px dashed black;
    }

    .bDashTop {
        border-top: 2px dashed black;
    }

    .bDashLeft {
        border-left: 2px dashed black;
    }

    td.col1 {
        text-align: right;
    }

    td.centrar {
        text-align: center;
    }

    .cuerpo {
        width: 100%;
        height: 100%;
    }

    .cuerpo td {
        font-size: 18px;
    }

    .cuerpo h1 {
        font-size: 45px;
        color: blue;
    }

    .cuerpo h3 {
        font-size: 25px;
    }

    .cuerpo h6 {
        font-size: 16px;
    }

    .derecha {
        text-align: right;
    }

    @supports(object-fit: cover) {
        .box img {
            height: 100%;
            object-fit: cover;
            object-position: center center;
        }
    }
</style>
<?php
include_once("conexion.php");
include_once("configDB.php");
include_once("classAbono.php");
include_once("funciones.php");

$taxiConect = new foo_mysqli("localhost", USER, PASS, DBNAME);
$taxiConect->query("SET NAMES utf8");

$abonos = new abono();
$abonos->recibo = $recibo;
$aux = $abonos->getAbonoRecibo($taxiConect);
$operador = $abonos->operadorRecibo($taxiConect);

$captirista = $abonos->capturaCajaRecibo($taxiConect, 'abono');
$cajera = $abonos->capturaCajaRecibo($taxiConect, 'recibo');

$taxiConect->close();
?>
<page orientation="paysage" format="LETTER" backtop="30mm" backbottom="20mm" backleft="5mm" backright="5mm">
    <page_header>
        <table class="headFoot bDashTop">
            <col style="width: 50%;">
            <col style="width: 50%;">
            <tr>
                <td>
                    <table class="headFoot bDashRight">
                        <col style="width: 50%;">
                        <col style="width: 50%;">
                        <tr>
                            <td class="centrar"><img src="../images/sitio 152.png" height="35" width="35"></td>
                            <td>
                                <h3>Sitio 152</h3>
                            </td>
                        </tr>
                        <tr>
                            <td><strong>Operador:</strong> <? echo $operador['nombreCompleto'] ?></td>
                            <td class="col1"><strong>Folio</strong>: <? echo $recibo ?></td>
                        </tr>
                        <tr>
                            <td><strong>Contable:</strong> <? echo $operador['contable'] ?></td>
                            <td class="col1"><strong>Caja</strong></td>
                        </tr>
                    </table>
                </td>
                <td>
                    <table class="headFoot bDashLeft">
                        <col style="width: 50%;">
                        <col style="width: 50%;">
                        <tr>
                            <td class="centrar"><img src="../images/sitio 152.png" height="35" width="35"></td>
                            <td>
                                <h3>Sitio 152</h3>
                            </td>
                        </tr>
                        <tr>
                            <td><strong>Operador:</strong> <? echo $operador['nombreCompleto'] ?></td>
                            <td class="col1"><strong>Folio</strong>: <? echo $recibo ?></td>
                        </tr>
                        <tr>
                            <td><strong>Contable:</strong> <? echo $operador['contable'] ?></td>
                            <td class="col1"><strong>Operador</strong></td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </page_header>
    <page_footer>
        <table class="headFoot bDashTop">
            <col style="width: 50%;">
            <col style="width: 50%;">
            <tr>
                <td class="bDashRight">
                    <h6>Capturado por: <? echo $captirista ?><br>
                        Cobrado por: <? echo $cajera ?><br>
                        La columna 'Tipo' indica si el concepto pagado fue condonado (C) o abonado (A).</h6>
                </td>
                <td class="bDashLeft">
                    <h6>Capturado por: <? echo $captirista ?><br>
                        Cobrado por: <? echo $cajera ?><br>
                        La columna 'Tipo' indica si el concepto pagado fue condonado (C) o abonado (A).</h6>
                </td>
            </tr>
        </table>
    </page_footer>

    <table class="cuerpo">
        <col style="width: 50%;">
        <col style="width: 50%;">
        <tr>
            <td>
                <table class="headFoot bDashRight">
                    <col style="width: 38%;">
                    <col style="width: 15%;">
                    <col style="width: 14%;">
                    <col style="width: 14%;">
                    <col style="width: 19%;">
                    <thead>
                        <tr>
                            <th>Concepto</th>
                            <th>Semana</th>
                            <th>Año</th>
                            <th>Tipo</th>
                            <th>Importe</th>
                        </tr>
                    </thead>
                    <tbody>
                        <? $total1 = printTD($aux, '#FFFFFF', '#D9D9D9'); ?>
                        <tr style="background: #FFF376">
                            <td colspan="3">&nbsp;</td>
                            <td>Total</td>
                            <td>$ <? echo number_format($total1, 2); ?></td>
                        </tr>
                    </tbody>
                </table>
            </td>
            <td>
                <table class="headFoot bDashLeft">
                    <col style="width: 38%;">
                    <col style="width: 15%;">
                    <col style="width: 14%;">
                    <col style="width: 14%;">
                    <col style="width: 19%;">
                    <thead>
                        <tr>
                            <th>Concepto</th>
                            <th>Semana</th>
                            <th>Año</th>
                            <th>Tipo</th>
                            <th>Importe</th>
                        </tr>
                    </thead>
                    <tbody>
                        <? $total2 = printTD($aux, '#FFFFFF', '#D9D9D9'); ?>
                        <tr style="background: #FFF376">
                            <td colspan="3">&nbsp;</td>
                            <td>Total</td>
                            <td>$ <? echo number_format($total2, 2); ?></td>
                        </tr>
                    </tbody>
                </table>
            </td>
        </tr>
    </table>
</page>
