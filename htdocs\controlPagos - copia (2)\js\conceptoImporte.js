$(document).ready(function () {
  $("#concepto").cambiaMayus();

  if ($.get("resp")) {
    resp = $.get("resp");
    switch (resp) {
      case "0":
        msg = "Error al guardar, intente nuevamente.";
        break;
      case "1":
        msg = "Importe guardado satisfacctoriamente";
        break;
    }
    alertify.alert("Servitaxis Sitio 152", msg);
  }

  $("#concepto").miCatalogo("concepto", "Catalogo concepto")

  $("#removeImporte")
    .attr("disabled", true)
    .click(function () {
      $("#renglones").children("div:last-child").remove();
      $("#renglones").children("br:last-child").remove();

      arrayControl = $("#renglones").children("div").length;
      if (arrayControl == 1) {
        $("#removeImporte").attr("disabled", true);
      }
    });

  $("#addIporte").click(function () {
    $("#removeImporte").attr("disabled", false);
    arrayControl = $("#renglones").children("div").length;
    auxCount = arrayControl + 1;

    $("#renglones").append(
      '<div class="row justify-content-center">' +
        '<div class="col-lg-4"><hr>' +
        '<label for="importe' +
        auxCount +
        '">' +
        "<h5>Importe:</h5>" +
        "</label>" +
        '<div class="input-group mb-3">' +
        '<span class="input-group-text">$</span>' +
        '<input type="number" name="importe[]" id="importe' +
        auxCount +
        '" class="form-control" aria-label="Amount (to the nearest dollar)">' +
        '<span class="input-group-text">.00</span>' +
        "</div>" +
        "</div>" +
        "</div>"
    );
  });
});
