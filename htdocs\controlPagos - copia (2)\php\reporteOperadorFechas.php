<?php
include_once("conexion.php");
include_once("configDB.php");
include_once("classProcesaVariable.php");
include_once("classAbono.php");
include_once("classImporte.php");
include_once("classReporte.php");
include_once("classOperador.php");
include_once("session.php");

if (isset($_POST['beginDate']) and isset($_POST['endDate'])) {

    $beginDate = new procesaVariable($_POST['beginDate']);
    $endDate = new procesaVariable($_POST['endDate']);
    $concepto = new procesaVariable($_POST['concepto']);
    $operador = new procesaVariable($_POST['operador']);
    $tipoReporte = new procesaVariable($_POST['tipoReporte']);

    $taxiConect = new foo_mysqli("localhost", USER, PASS, DBNAME);
    $taxiConect->query("SET NAMES utf8");

    switch ($tipoReporte->valor) {
        case 1:
            $q = "SELECT o.contable, a.semana, a.anio";
            foreach ($concepto->valor as $clave => $idConcepto) {
                $importeAux = new importe($idConcepto);
                $importes = $importeAux->importeXConcepto($taxiConect);
                $coneptoNombre = $importeAux->nombreConcepto($taxiConect);
                $q .= ", CONCAT(SUM(IF(a.idImporte IN($importes), i.monto, 0)),
            CASE REPLACE(GROUP_CONCAT(IF(a.idImporte IN($importes), a.tipoAbono, 'N') SEPARATOR ','),'N,','')
                WHEN 'A' THEN ''
                WHEN 'C' THEN '*'
                WHEN 'V' THEN '**'
                WHEN 'N' THEN '0'                    
                ELSE '***'
            END
            ) AS '$coneptoNombre', 
            GROUP_CONCAT(IF(a.idImporte IN($importes), DATE_FORMAT(r.fechaPago, '%d/%m/%Y %H:%i'), '') SEPARATOR ',') AS 'fechaPago-$coneptoNombre' ";
            }
            $q .= "FROM operador o 
    INNER JOIN abono a ON o.idOperador = a.idOperador
    INNER JOIN importe i ON a.idImporte = i.idImporte
    INNER JOIN recibo r ON a.idRecibo = r.idRecibo 
    WHERE o.idOperador = '$operador->valor'
    AND DATE_FORMAT(r.fechaPago, '%Y-%m-%d') BETWEEN '$beginDate->valor' AND '$endDate->valor'";

            if (isset($_POST['soloAbonos']) and $_POST['soloAbonos'] == 'S') {
                $q .= "AND a.tipoAbono = 'A'";
            }

            $q .= " GROUP BY o.idOperador, o.contable, a.semana, a.anio
    ORDER BY a.anio, a.semana";
            break;
        case 2:
            $q = "SELECT	a.idAbono AS noRegistro, o.contable,
                CONCAT_WS(' ',o.nombre,o.apellidoPaterno,o.apellidoMaterno) AS NombreCompleto,
                i.monto, c.descripcion AS ConceptoPagado, 
                DATE_FORMAT(r.fechaPago, '%d/%m/%Y %H:%i') AS fechaPago,
                a.idRecibo AS Recibo, a.semana, a.anio, 
                CASE a.tipoAbono
                    WHEN 'A' THEN 'Abono'
                    WHEN 'C' THEN 'Condonación'
                    WHEN 'V' THEN 'Convenio'
                END AS tipoRegistro
        FROM abono a 
        INNER JOIN operador o ON a.idOperador = o.idOperador
        INNER JOIN recibo r ON a.idRecibo = r.idRecibo 
        INNER JOIN importe i ON a.idImporte = i.idImporte 
        INNER JOIN concepto c ON i.idConcepto = c.idConcepto
        WHERE a.idOperador = 1
        AND DATE_FORMAT(r.fechaPago, '%Y-%m-%d') BETWEEN '$beginDate->valor' AND '$endDate->valor' ";
            if (isset($_POST['soloAbonos']) and $_POST['soloAbonos'] == 'S') {
                $q .= "AND a.tipoAbono = 'A' ";
            }
            $q .= "ORDER BY a.anio, a.semana ASC ;";
            break;
    }
    $rq = $taxiConect->query($q) or die($taxiConect->error . "<br><pre>$q");

    $thisOperador = new operador('A');
    $thisOperador->identificador = $operador->valor;
    $operadorData = $thisOperador->dataXIdentificador($taxiConect);
    $nameOperador = $operadorData['nombre'] . " " . $operadorData['apellidoPaterno'] . " " . $operadorData['apellidoMaterno'];

    $myCsv = new Reporte("$nameOperador", $rq);
    $myCsv->putHeaders(false);
    $myCsv->putData(false);

    $rq->free();

    $taxiConect->close();
} else {
    $link = '0';
}
