<?php
include_once("conexion.php");
include_once("configDB.php");
include_once("classProcesaVariable.php");
include_once("classCargo.php");
include_once("classAbono.php");
include_once("classRecibo.php");
include_once("session.php");

if (isset($_POST['operadorID']) and isset($_POST['idConvenioImporte'])) {

    $operadorID = new procesaVariable($_POST['operadorID']);
    $idConvenioImporte = new procesaVariable($_POST['idConvenioImporte']);
    $concepto = new procesaVariable($_POST['concepto']);
    $importe = new procesaVariable($_POST['importe']);
    $desdeW = new procesaVariable($_POST['desdeW']);
    $hastaW = new procesaVariable($_POST['hastaW']);
    $anio = new procesaVariable($_POST['anio']);
    $semanasTotal = new procesaVariable($_POST['semanasTotal']);
    $importeTotal = new procesaVariable($_POST['importeTotal']);
    $semanaPagar = new procesaVariable($_POST['semanaPagar']);

    $usuario = $_SESSION['idUser'];
    $tipoUsuario = $_SESSION['userType'];

    $taxiConect = new foo_mysqli("localhost", USER, PASS, DBNAME);
    $taxiConect->query("SET NAMES utf8");

    $auxil = $taxiConect->iniciaTransaccion();

    $myRecibo = new recibo();
    if ($myRecibo->guardaRecibo($taxiConect)) {
        $suma = 0;
        foreach ($concepto->valor as $i => $val) {
            $importeBucle = $importe->valor;
            $desdeBucle = $desdeW->valor;
            $hastaBucle = $hastaW->valor;
            $anioBucle = $anio->valor;
            $totalBucle = $importeTotal->valor;
            $j = $desdeBucle[$i];
            while ($j <= $hastaBucle[$i]) {
                $abonos = new abono();
                $abonos->asignaDatos($operadorID->valor, $usuario, $tipoUsuario, $j, $importeBucle[$i], $anioBucle[$i], 'V');
                if (!$abonos->guardaAbono($taxiConect, $myRecibo->identificador)) {
                    echo $taxiConect->error;
                    $auxil = 0;
                }
                $j++;
            }
            $suma = $suma + $totalBucle[$i];
        }

        if ($auxil != 0) {
            $q = "UPDATE recibo SET estado='P',
            convenio='$suma',
            idUsuario='$usuario', 
            tipoUsuario='$tipoUsuario',
            observaciones='Convenio',
            fechaPago=CURRENT_TIMESTAMP() 
            WHERE idRecibo='$myRecibo->identificador'";
            if ($taxiConect->query($q)) {
                $myConvenio = new cargo('C', $suma, $operadorID->valor, $myRecibo->identificador, $usuario, $tipoUsuario);
                if ($myConvenio->saveCargo($taxiConect)) {
                    $myConvenio->importePagar = $idConvenioImporte->valor;
                    if (!$myConvenio->saveComConvenio($taxiConect)) {
                        echo $taxiConect->error;
                        $auxil = 0;
                        $convenioID = 0;
                    } else {
                        $convenioID = $myRecibo->identificador;
                    }
                } else {
                    echo $taxiConect->error;
                    $auxil = 0;
                }
            } else {
                $auxil = 0;
                echo $taxiConect->error;
            }
        }
    }

    //checar id'simportes
    $link = $taxiConect->finTransaccion($auxil, "pagarePrint.php?con=$convenioID", "convenio.php?resp=0&cuotaConvenio=$idConvenioImporte->valor");
    $taxiConect->resetTabla($auxil, "idAbono", "abono");

    $taxiConect->close();
} else {
    $link = '0';
}

header("Location: ../$link");
