<?php
/**
 * API REST para Sistema de Actividades
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Manejar preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once '../config/database.php';
require_once '../models/Actividad.php';

// Variables globales
$database = new Database();
$db = $database->getConnection();
$actividad = new Actividad($db);
$input = json_decode(file_get_contents('php://input'), true);

/**
 * Enviar respuesta JSON
 */
function sendResponse($data, $status = 200) {
    http_response_code($status);
    echo json_encode($data, JSON_UNESCAPED_UNICODE);
    exit();
}

/**
 * Enviar error
 */
function sendError($message, $status = 400) {
    sendResponse(['success' => false, 'error' => $message], $status);
}

// Verificar conexión a base de datos
if (!$db) {
    sendError('Error de conexión a la base de datos', 500);
}

// Actualizar estados automáticamente
$database->actualizarEstados();

// Enrutamiento según método HTTP
switch ($_SERVER['REQUEST_METHOD']) {
    case 'GET':
        handleGet();
        break;
    case 'POST':
        handlePost();
        break;
    case 'PUT':
        handlePut();
        break;
    default:
        sendError('Método no permitido', 405);
}

/**
 * Manejar peticiones GET
 */
function handleGet() {
    global $actividad;

    $action = $_GET['action'] ?? '';

    if (isset($_GET['id'])) {
        // Obtener actividad específica
        $actividadData = $actividad->getById($_GET['id']);
        if ($actividadData) {
            sendResponse(['success' => true, 'data' => $actividadData]);
        } else {
            sendError('Actividad no encontrada', 404);
        }
    } elseif ($action === 'generate_id') {
        // Generar nuevo ID
        $newId = $actividad->generateId();
        sendResponse(['success' => true, 'actividad_id' => $newId]);
    } elseif ($action === 'temas') {
        // Obtener temas disponibles
        $temas = $actividad->getTemas();
        sendResponse(['success' => true, 'data' => $temas]);
    } elseif ($action === 'temas_detallados') {
        // Obtener temas con detalles completos
        $temasDetallados = $actividad->getTemasDetallados();
        sendResponse(['success' => true, 'data' => $temasDetallados]);
    } elseif ($action === 'crear_tema') {
        // Crear nuevo tema
        if (empty($_GET['nombre'])) {
            sendError('El nombre del tema es requerido');
        }

        $resultado = $actividad->crearTema([
            'nombre' => $_GET['nombre'],
            'descripcion' => $_GET['descripcion'] ?? '',
            'color' => $_GET['color'] ?? '#3498db',
            'icono' => $_GET['icono'] ?? 'fas fa-tag'
        ]);

        if ($resultado) {
            sendResponse(['success' => true, 'message' => 'Tema creado exitosamente']);
        } else {
            sendError('Error al crear el tema');
        }
    } elseif ($action === 'list') {
        // Obtener lista de actividades con filtros
        $limit = isset($_GET['limit']) ? intval($_GET['limit']) : 100;
        $offset = isset($_GET['offset']) ? intval($_GET['offset']) : 0;

        $filtros = [
            'tema' => $_GET['tema'] ?? '',
            'prioridad' => $_GET['prioridad'] ?? '',
            'responsable' => $_GET['responsable'] ?? '',
            'progreso' => $_GET['progreso'] ?? ''
        ];

        // Limpiar filtros vacíos
        $filtros = array_filter($filtros);

        $actividades = $actividad->getAll($limit, $offset, $filtros);
        sendResponse(['success' => true, 'data' => $actividades]);
    } else {
        sendError('Parámetros inválidos');
    }
}

/**
 * Manejar peticiones POST (crear actividad)
 */
function handlePost() {
    global $actividad, $input;

    if (!$input) {
        sendError('Datos no válidos');
    }

    // Validar campos requeridos
    $required_fields = ['tarea', 'tema', 'prioridad', 'fechaHoraCompromiso', 'responsable'];
    foreach ($required_fields as $field) {
        if (empty($input[$field])) {
            sendError("El campo '$field' es requerido");
        }
    }

    // Validar formato de fecha
    if (!empty($input['fechaHoraCompromiso'])) {
        $testDate = DateTime::createFromFormat('d/m/Y H:i', $input['fechaHoraCompromiso']);
        if ($testDate === false) {
            sendError("Formato de fecha inválido. Esperado: dd/mm/yyyy hh:mm");
        }
    }

    // Validar prioridad
    $prioridadesValidas = ['Alta', 'Media', 'Baja'];
    if (!in_array($input['prioridad'], $prioridadesValidas)) {
        sendError("Prioridad inválida. Valores permitidos: " . implode(', ', $prioridadesValidas));
    }

    try {
        $actividad_id = $actividad->create($input);
        sendResponse([
            'success' => true,
            'message' => 'Actividad creada exitosamente',
            'actividad_id' => $actividad_id
        ], 201);
    } catch (Exception $e) {
        sendError('Error al crear la actividad: ' . $e->getMessage());
    }
}

/**
 * Manejar peticiones PUT (actualizar actividad)
 */
function handlePut() {
    global $actividad, $input;

    if (!$input) {
        sendError('Datos no válidos');
    }

    // Validar que se proporcione el ID de la actividad
    if (empty($input['actividad_id'])) {
        sendError('ID de actividad requerido para actualización');
    }

    // Validar campos requeridos
    $required_fields = ['tarea', 'tema', 'prioridad', 'fechaHoraCompromiso', 'responsable', 'progreso'];
    foreach ($required_fields as $field) {
        if (!isset($input[$field])) {
            sendError("El campo '$field' es requerido");
        }
    }

    // Validar formato de fecha
    if (!empty($input['fechaHoraCompromiso'])) {
        $testDate = DateTime::createFromFormat('d/m/Y H:i', $input['fechaHoraCompromiso']);
        if ($testDate === false) {
            sendError("Formato de fecha inválido. Esperado: dd/mm/yyyy hh:mm");
        }
    }

    // Validar prioridad
    $prioridadesValidas = ['Alta', 'Media', 'Baja'];
    if (!in_array($input['prioridad'], $prioridadesValidas)) {
        sendError("Prioridad inválida. Valores permitidos: " . implode(', ', $prioridadesValidas));
    }

    // Validar progreso
    $progresosValidos = ['Vigente', 'En Progreso', 'Vencido', 'Completado'];
    if (!in_array($input['progreso'], $progresosValidos)) {
        sendError("Progreso inválido. Valores permitidos: " . implode(', ', $progresosValidos));
    }

    try {
        $result = $actividad->update($input['actividad_id'], $input);
        if ($result) {
            sendResponse([
                'success' => true,
                'message' => 'Actividad actualizada exitosamente',
                'actividad_id' => $input['actividad_id']
            ]);
        } else {
            sendError('No se pudo actualizar la actividad. Verifique que el ID existe.');
        }
    } catch (Exception $e) {
        sendError('Error al actualizar la actividad: ' . $e->getMessage());
    }
}
?>
