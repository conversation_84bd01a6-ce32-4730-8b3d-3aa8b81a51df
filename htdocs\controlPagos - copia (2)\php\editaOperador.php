<?php
include_once("conexion.php");
include_once("configDB.php");
include_once("classProcesaVariable.php");
include_once("classLicencia.php");

if (isset($_POST['nombre']) and isset($_POST['idOperador'])) {

    $nombre = new procesaVariable($_POST['nombre']);
    $aPaterno = new procesaVariable($_POST['aPaterno']);
    $aMaterno = new procesaVariable($_POST['aMaterno']);
    $contable = new procesaVariable($_POST['contable']);
    // $referenciado = new procesaVariable($_POST['referenciado']);
    $celular = new procesaVariable($_POST['celular']);
    $telefono = new procesaVariable($_POST['telefono']);
    $direccion = new procesaVariable($_POST['direccion']);
    $correo = new procesaVariable($_POST['correo']);
    $idOperador = new procesaVariable($_POST['idOperador']);
    $mailOperador = strtolower($correo->valor);

    $taxiConect = new foo_mysqli("localhost", USER, PASS, DBNAME);
    $taxiConect->query("SET NAMES utf8");

    $insert = "UPDATE operador 
    SET nombre='$nombre->valor',
    apellidoPaterno='$aPaterno->valor',
    apellidoMaterno='$aMaterno->valor',
    contable='$contable->valor',
    celular='$celular->valor',
    telefono=" . $telefono->nulo() . ",
    domicilio=" . $direccion->nulo() . ",
    correo='$mailOperador'
    WHERE idOperador = '$idOperador->valor'";
    $exeInsert = $taxiConect->query($insert);

    if ($exeInsert) {
        $var = 1;
    } else {
        $var = '0';
    }

    $taxiConect->close();
} else {
    $var = '0';
}

header("Location: ../editOperador.php?res=$var");
