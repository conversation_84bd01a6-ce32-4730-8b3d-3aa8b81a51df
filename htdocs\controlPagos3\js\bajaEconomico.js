$(document).ready(function () {
  if ($.get("res")) {
    Rsp = $.get("res");
    switch (Rsp) {
      case "0":
        rmsg = "Error en el proceso";
        break;
      case "1":
        rmsg = "Baja realizada correctamente";
        break;
    }
    alertify
      .alert()
      .setting({
        title: "Servitaxis Sitio 152",
        closable: false,
        message: rmsg,
        onok: function () {
          let myUrl = window.location.origin;
          myUrl = `${myUrl}/bajaEconomico.php`;
          $(location).attr("href", myUrl);
        },
      })
      .show();
  }

  $("#divBtnBaja,#dataAuto").hide();

  $("#busqueda").validate({
    submitHandler: function (form) {
      datoOP = $("#buscaEconomico").val();
      $.ajax({
        type: "post",
        url: "php/ajax/buscaEconomico.php",
        data: { datoBusca: datoOP },
      })
        .done(function (r) {
          R = $.parseJSON(r);
          if (R[0] != 0) {
            const {
              anio,
              apellidoMaterno,
              apellidoPaterno,
              economico,
              idEconomico,
              marca,
              modelo,
              nombre,
              placa,
              motor,
            } = R;
            $("#dataAuto").empty().append(`<div class="col-lg-8">
            <h5>Propietario: ${nombre} ${apellidoPaterno} ${apellidoMaterno}</h5>
            <h5>NIV: ${idEconomico}</h5><input type="hidden" name="idEconomico" id="idEconomico" value="${idEconomico}">
            <h5>No. motor: ${motor}</h5>
            <h5>Marca: ${marca}</h5>
            <h5>Modelo: ${modelo}</h5>
            <h5>Placa: ${placa}</h5>
            <h5>Año: ${anio}</h5>
            <h5>Economico: ${economico}</h5>
        </div>`);
            $("#divBtnBaja,#dataAuto").show();
          } else {
            $("#divBtnBaja,#dataAuto").hide();
            alertify.alert("Servitaxis Sitio 152", "Economico no encontrado");
          }
        })
        .fail(function () {
          alertify.alert("Warning!", "Error al cargar el formulario");
        });
    },
  });
});
