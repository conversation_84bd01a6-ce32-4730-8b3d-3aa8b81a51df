# Configuración Apache para Sistema de Actividades

# Habilitar reescritura de URLs
RewriteEngine On

# Redireccionar todas las peticiones de API a actividades.php
RewriteRule ^api/(.*)$ api/actividades.php [QSA,L]

# Configuración de seguridad
<Files "*.php">
    Order Allow,Deny
    Allow from all
</Files>

# Proteger archivos de configuración
<Files "*.sql">
    Order Deny,Allow
    Deny from all
</Files>

<Files "README.md">
    Order Deny,Allow
    Deny from all
</Files>

# Configuración de cache para recursos estáticos
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/ico "access plus 1 month"
    ExpiresByType image/icon "access plus 1 month"
    ExpiresByType text/ico "access plus 1 month"
    ExpiresByType application/ico "access plus 1 month"
</IfModule>

# Configuración de compresión
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# Configuración de headers de seguridad
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
</IfModule>
