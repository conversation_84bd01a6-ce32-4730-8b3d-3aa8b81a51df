$(document).ready(function () {
  if ($.get("res")) {
    Rsp = $.get("res");
    switch (Rsp) {
      case "0":
        rmsg = "Error en el proceso";
        break;
      case "1":
        rmsg = "Datos actualizados correctamente";
        break;
    }
    alertify
      .alert()
      .setting({
        title: "Servitaxis Sitio 152",
        closable: false,
        message: rmsg,
        onok: function () {
          let myUrl = window.location.origin;
          myUrl = `${myUrl}/editaOwner.php`;
          $(location).attr("href", myUrl);
        },
      })
      .show();
  }

  $("input").cambiaMayus();
  if ($.get("owner")) {
    const propietario = $.get("owner");
    $.ajax({
      type: "post",
      url: "php/ajax/buscaOwnerData.php",
      data: { datoBusca: propietario },
    })
      .done(function (r) {
        R = $.parseJSON(r);
        if (R[0] != 0) {
          const {
            idOwner,
            nombre,
            apellidoPaterno,
            apellidoMaterno,
            telefono,
            correo,
            tipo,
          } = R;

          $("#nombre").val(nombre).cambiaMayus();
          $("#aPaterno").val(apellidoPaterno).cambiaMayus();
          $("#aMaterno").val(apellidoMaterno).cambiaMayus();
          $("#correoOwner").val(correo);
          $("#telefono").val(telefono);
          $("#tipoPropietario").val(tipo);
          $("#idOwner").val(idOwner);

          $("#formEditOwner").show();
        } else {
          alertify
            .alert()
            .setting({
              title: "Servitaxis Sitio 152",
              closable: false,
              message: "rmsg",
              onok: function () {
                let myUrl = window.location.origin;
                myUrl = `${myUrl}/editaOwner.php`;
                $(location).attr("href", myUrl);
              },
            })
            .show();
        }
      })
      .fail(function () {
        alertify.alert("Warning!", "Error al cargar el formulario");
      });
  } else {
    let myUrl = window.location.origin;
    myUrl = `${myUrl}/listaOwner.php`;
    $(location).attr("href", myUrl);
  }

  $("#formEditOwner").validate();
});
