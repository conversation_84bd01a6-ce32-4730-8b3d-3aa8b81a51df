$(document).ready(function () {
  $("#nombre,#aPaterno,#aMaterno").cambiaMayus();

  if ($.get("resp")) {
    resp = $.get("resp");
    switch (resp) {
      case "0":
        msg = "Error al guardar, intente nuevamente.";
        break;
      case "1":
        msg = "Login guardado satisfacctoriamente";
        break;
      case "2":
        msg = `<span class="text-danger">ERROR</span><br>El usuario ya tiene un login activo con ese tipo de usuario.`;
        break;
      case "3":
        msg = `<span class="text-danger">ERROR</span>
              <br>Se asigno login previamente para este usuario,
              valide <a href="listaLogin.php">estatus del mismo en la lista</a>.`;
        break;
    }
    alertify.alert("Servitaxis Sitio 152", msg);
  }

  $("#usuario").miCatalogo("userlist", "Error al cargar lista de usuarios");

  $("#formLogin").validate({
    rules: {
      userName: {
        remote: "php/ajax/validaVariable.php",
      },
      rePasswd: {
        equalTo: "#passwd",
      },
    },
    messages: {
      userName: {
        remote: "Nombre de usuario invalido.",
      },
    },
  });
});
