$(document).ready(function () {
  if ($.get("res")) {
    Rsp = $.get("res");
    switch (Rsp) {
      case "0":
        rmsg = "Error en el proceso";
        break;
      case "1":
        rmsg = "Baja guardada correctamente";
        break;
      default:
        rmsg = "Error al guardar en la base de datos.";
    }
    alertify
      .alert()
      .setting({
        title: "Servitaxis Sitio 152",
        closable: false,
        message: rmsg,
        onok: function () {
          let myUrl = window.location.origin;
          myUrl = `${myUrl}/listaEconomico.php`;
          $(location).attr("href", myUrl);
        },
      })
      .show();
  }

  const admin = $("#admin").val();
  $.ajax({
    type: "post",
    url: "php/ajax/listadoEconomico.php",
    data: { myVal: admin },
  })
    .done(function (r) {
      R = $.parseJSON(r);
      if (R[0] != 0) {
        $.each(R, function (indexInArray, valueOfElement) {
          const {
            idEconomico,
            Propietario,
            economico,
            marca,
            modelo,
            placa,
            anio,
            Status,
            propietarioAuto,
            motor,
            vigenciaSeguro,
          } = valueOfElement;
          const append = `<tr ${
            vigenciaSeguro == "Caducado" ? 'class="table-danger"' : ""
          }>
                    <td>${idEconomico}</td>
                    <td>${Propietario}</td>
                    <td>${economico}</td>
                    <td>${motor}</td>
                    <td>${marca}</td>
                    <td>${modelo}</td>
                    <td>${anio}</td>
                    <td>${placa}</td>
                    <td>${propietarioAuto}</td>
                    <td>${vigenciaSeguro}</td>
                    <td>${Status}</td>
                </tr>`;
          $("#economicoList tbody").append(append);
        });
        $("#economicoList").DataTable({
          language: {
            url: "//cdn.datatables.net/plug-ins/9dcbecd42ad/i18n/Spanish.json",
          },
        });
        $("#economicoList").tableExport({
          formats: ["csv", "txt"],
        });
      }
    })
    .fail(function () {
      alertify.alert("Warning!", "Error al cargar los datos");
    });
});
