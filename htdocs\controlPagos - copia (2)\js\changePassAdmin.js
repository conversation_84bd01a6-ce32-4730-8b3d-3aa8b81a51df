$(document).ready(function () {
  if ($.get("res")) {
    Rsp = $.get("res");
    switch (Rsp) {
      case "0":
        rmsg = "Error al guardar los datos";
        break;
      case "1":
        rmsg = "Datos guardados correctamente";
        break;
    }
    alertify.alert("Servitaxis Sitio 152", rmsg);
  }
  $("#divUser,#divPassNew").hide();

  $("#empleado")
    .miCatalogo("userlist", "Error en  catalogo de empleados")
    .change(function (e) {
      e.preventDefault();
      const user = $(this).val();
      $.ajax({
        type: "post",
        url: "php/ajax/empleadoUserList.php",
        data: { idUser: user },
      })
        .done(function (r) {
          R = $.parseJSON(r);
          if (R[0] != 0) {
            $("#usuario").empty();
            let userAppend = `<option value="">Selecione una opción ...</option>`;
            R.forEach(function (param) {
              let { tipoUsuario, userName } = param;
              userAppend = `${userAppend}<option value="${tipoUsuario}">${userName}</option>`;
            });
            $("#usuario").append(userAppend);
            $("#divUser,#divPassNew").show();
          } else {
            alertify.alert(
              "Servitaxis Sitio 152",
              "El empleado no tiene usuarios activos"
            );
            $("#empleado").val("")
            $("#divUser,#divPassNew").hide();
          }
        })
        .fail(function () {
          console.log("prueba");
        });
    });

  $("#formNewPass").validate({
    rules: {
      contraNuevaC: {
        equalTo: "#contraNueva",
      },
    },
  });
});
