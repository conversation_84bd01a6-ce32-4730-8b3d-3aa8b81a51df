# 📋 Sistema de Actividades

Sistema de gestión de actividades desarrollado con la misma arquitectura que el Sistema de Minutas.

## 🚀 Características

### ✅ **Funcionalidades Implementadas:**
- ✅ Registro de nuevas actividades
- ✅ Consulta y filtrado de actividades
- ✅ Edición de actividades existentes
- ✅ Estados automáticos (Vigente → Vencido)
- ✅ Interfaz responsiva con React
- ✅ Validaciones en frontend y backend
- ✅ Historial de cambios (auditoría)

### 📊 **Campos de Actividad:**
- **Tarea**: Descripción de la actividad (campo abierto)
- **Tema**: Categoría de la actividad (combo predefinido)
- **Prioridad**: Alta, Media, Baja (combo)
- **Fecha y Hora Compromiso**: Fecha límite para completar
- **Responsable**: Usuario asignado (combo del Sistema de Minutas)
- **Progreso**: <PERSON><PERSON><PERSON>, En Progreso, Vencido, Completado
- **Comentarios**: Observaciones adicionales

### 🔄 **Estados Automáticos:**
- **Vigente**: Actividad creada y dentro del plazo
- **En Progreso**: Actividad en desarrollo (manual)
- **Vencido**: Fecha compromiso superada (automático)
- **Completado**: Actividad finalizada (manual)

## 📁 Estructura del Proyecto

```
Sistema_Actividades/
├── index.php              # Interfaz principal
├── config/
│   └── database.php       # Configuración de BD
├── models/
│   └── Actividad.php      # Modelo de datos
├── api/
│   └── actividades.php    # API REST
├── database_setup.sql     # Script de BD
└── README.md              # Este archivo
```

## 🛠️ Instalación

### **1. Ejecutar Script de Base de Datos**
```sql
-- Conectar a MySQL y ejecutar:
mysql -u root -p minutas_db < database_setup.sql
```

### **2. Verificar Configuración**
- Asegurar que `config/database.php` tenga las credenciales correctas
- La base de datos `minutas_db` debe existir (del Sistema de Minutas)

### **3. Acceder al Sistema**
```
http://localhost/Sistema_Actividades/
```

## 🎯 Uso del Sistema

### **📝 Nueva Actividad:**
1. Ir a la pestaña "Nueva Actividad"
2. Llenar todos los campos requeridos (*)
3. Seleccionar tema, prioridad y responsable
4. Establecer fecha y hora de compromiso
5. Agregar comentarios (opcional)
6. Guardar actividad

### **📋 Consultar Actividades:**
1. Ir a la pestaña "Consultar Actividades"
2. Usar filtros para buscar actividades específicas
3. Ver tabla con todas las actividades
4. Hacer clic en ✏️ para editar una actividad

### **✏️ Editar Actividad:**
1. Desde la consulta, hacer clic en el botón de editar
2. Se abre el formulario con datos precargados
3. Modificar campos necesarios (incluyendo progreso)
4. Actualizar actividad

## 🔧 Características Técnicas

### **🏗️ Arquitectura:**
- **Frontend**: React 18 + Bootstrap 5
- **Backend**: PHP 8+ con PDO
- **Base de Datos**: MySQL 8+
- **API**: REST con JSON

### **🛡️ Validaciones:**
- **Frontend**: Validación en tiempo real
- **Backend**: Validación de datos y tipos
- **Base de Datos**: Constraints y triggers

### **📊 Base de Datos:**
- **Tabla Principal**: `actividades`
- **Catálogos**: `actividades_temas`
- **Auditoría**: `actividades_historial`
- **Vista**: `vista_actividades` (con estados calculados)

### **⚡ Automatización:**
- **Evento MySQL**: Actualiza estados cada hora
- **Procedimiento**: `ActualizarEstadosActividades()`
- **Estados Calculados**: Vista con lógica de vencimiento

## 🎨 Interfaz de Usuario

### **🎯 Diseño:**
- Interfaz moderna con Bootstrap 5
- Iconos Font Awesome
- Colores consistentes con Sistema de Minutas
- Responsive design

### **📱 Componentes:**
- Navegación por pestañas
- Formularios validados
- Tabla con filtros
- Mensajes de estado
- Indicadores de carga

### **🏷️ Badges de Estado:**
- **Prioridad Alta**: Rojo
- **Prioridad Media**: Amarillo
- **Prioridad Baja**: Azul
- **Vigente**: Verde
- **En Progreso**: Azul
- **Vencido**: Rojo
- **Completado**: Gris

## 🔗 Integración

### **👥 Usuarios Compartidos:**
- Utiliza el mismo catálogo de usuarios del Sistema de Minutas
- Consistencia en responsables entre sistemas

### **🗄️ Base de Datos Compartida:**
- Misma BD: `minutas_db`
- Tablas independientes pero relacionadas
- Posibilidad de reportes cruzados

## 🚨 Solución de Problemas

### **❌ Error de Conexión:**
- Verificar credenciales en `config/database.php`
- Asegurar que MySQL esté ejecutándose
- Verificar que la BD `minutas_db` exista

### **❌ Tablas No Existen:**
- Ejecutar `database_setup.sql`
- Verificar permisos de usuario MySQL

### **❌ Estados No Se Actualizan:**
- Verificar que el evento MySQL esté activo:
```sql
SHOW EVENTS LIKE 'evento_actualizar_estados';
```

## 📈 Próximas Mejoras

### **🔮 Funcionalidades Futuras:**
- [ ] Exportación a Excel/PDF
- [ ] Notificaciones por email
- [ ] Dashboard con estadísticas
- [ ] Reportes avanzados
- [ ] Integración con calendario
- [ ] Asignación múltiple de responsables
- [ ] Subtareas y dependencias
- [ ] Adjuntos de archivos

### **⚡ Optimizaciones:**
- [ ] Cache de consultas frecuentes
- [ ] Paginación en tabla
- [ ] Búsqueda por texto
- [ ] Filtros avanzados por fecha
- [ ] Ordenamiento personalizable

## 📞 Soporte

Para soporte técnico o reportar problemas:
- Revisar logs de PHP y MySQL
- Verificar configuración de base de datos
- Comprobar permisos de archivos

---

**Desarrollado con la misma arquitectura del Sistema de Minutas**
*Versión 1.0 - Sistema de Actividades*
