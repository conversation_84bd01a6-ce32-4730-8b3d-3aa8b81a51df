$(document).ready(function () {
  $("#beginDate")
    .datepicker({
      dateFormat: "yy-mm-dd",
      defaultDate: "-2M",
      changeMonth: true,
      numberOfMonths: 3,
    })
    .on("change", function () {
      $("#endDate").datepicker("option", "minDate", $(this).val());
    });
  $("#endDate")
    .datepicker({
      dateFormat: "yy-mm-dd",
      defaultDate: "+2M",
      changeMonth: true,
      numberOfMonths: 3,
    })
    .on("change", function () {
      $("#beginDate").datepicker("option", "maxDate", $(this).val());
    });

  $("#divAnio,#divGenera").hide();
  $("#operador")
    .miCatalogo("operadorcontable", "Error en  catalogo de operador")
    .change(function (e) {
      e.preventDefault();
      const user = $(this).val();
      $.ajax({
        type: "post",
        url: "php/ajax/aniosKardex.php",
        data: { operador: user },
      })
        .done(function (r) {
          R = $.parseJSON(r);
          if (R[0] != 0) {
            $("#usuario").empty();
            let userAppend = `<option value="">Selecione una opción ...</option>`;
            R.forEach(function (param) {
              const { anio } = param;
              userAppend = `${userAppend}<option value="${anio}">${anio}</option>`;
            });
            // $("#anio").empty().append(userAppend);
            $("#divAnio,#divGenera").show();
          } else {
            alertify.alert(
              "Servitaxis Sitio 152",
              "Operador no tiene registros"
            );
            $("#divAnio,#divGenera").hide();
          }
        })
        .fail(function () {
          console.log("Error al cargar");
        });
    });
});
