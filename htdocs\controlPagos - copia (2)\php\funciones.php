<?php
function parImpar($num)
{
    if ($num % 2 == 0) {
        $r = 0;
    } else {
        $r = 1;
    }
    return $r;
}

function mes($dia)
{
    $meses = array('X', 'Enero', 'Febrero', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', 'Mayo', '<PERSON><PERSON>', 'Julio', 'Agosto', 'Septiembre', 'Octubre', 'Noviembre', 'Diciembre');
    $mes = $meses[$dia];

    return $mes;
}

function printTD($array, $color1, $color2)
{
    $colores = [$color1, $color2];
    $renglones = count($array);
    $total = 0;
    for ($i = 0; $i < $renglones; $i++) {
        $color = $colores[parImpar($i)];
        $renglon = $array[$i];
        $imp = '<tr style="background:' . $color . '">';
        foreach ($renglon as $clave => $valor) {
            if ($clave == 'monto') {
                $auxMonto = $valor;
                $imp .= '<td>$ ' . $auxMonto . '</td>';
            } else {
                $imp .= '<td>' . $valor . '</td>';
                if ($clave == 'tipoAbono') {
                    $tipoAbono = $valor;
                }
            }
        }
        $imp .= '</tr>';
        if ($tipoAbono == 'A') {
            $total += $auxMonto;
        }

        echo $imp;
    }
    return $total;
}

function dataTD($array, $color1, $color2)
{
    $colores = [$color1, $color2];
    $renglones = count($array);
    $total = 0;
    $imp = '';
    for ($i = 0; $i < $renglones; $i++) {
        $color = $colores[parImpar($i)];
        $renglon = $array[$i];
        $imp .= '<tr style="background:' . $color . '">';
        foreach ($renglon as $clave => $valor) {
            if ($clave == 'monto') {
                $auxMonto = $valor;
                $imp .= '<td>$ ' . $auxMonto . '</td>';
            } else {
                $imp .= '<td>' . $valor . '</td>';
                if ($clave == 'tipoAbono') {
                    $tipoAbono = $valor;
                }
            }
        }
        $imp .= '</tr>';
        if ($tipoAbono == 'A') {
            $total += $auxMonto;
        }
    }
    $res = array('total' => $total, 'tabla' => $imp);
    return $res;
}
