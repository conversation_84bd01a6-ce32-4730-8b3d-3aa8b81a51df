<?php
include_once("conexion.php");
include_once("configDB.php");
include_once("classProcesaVariable.php");
include_once("classSeguro.php");

if (isset($_POST['poliza']) and isset($_POST['idSeguro'])) {

    $idSeguro = new procesaVariable($_POST['idSeguro']);
    $poliza = new procesaVariable($_POST['poliza']);
    $vigenciaBegin = new procesaVariable($_POST['vigenciaBegin']);
    $vigenciaEnd = new procesaVariable($_POST['vigenciaEnd']);
    $aseguradora = new procesaVariable($_POST['aseguradora']);

    $taxiConect = new foo_mysqli("localhost", USER, PASS, DBNAME);
    $taxiConect->query("SET NAMES utf8");

    $seguroUp = new seguro('', $aseguradora->valor, $poliza->valor, $vigenciaBegin->valor, $vigenciaEnd->valor);
    $seguroUp->identificador = $idSeguro->valor;
    if ($seguroUp->update($taxiConect)) {
        $var = 1;
    } else {
        $var = 0;
    }

    $taxiConect->close();
} else {
    $var = '0';
}

header("Location: ../editSeguro.php?res=$var");
