$(document).ready(function () {
  $("#nombre").cambiaMayus();

  if ($.get("resp")) {
    resp = $.get("resp");
    switch (resp) {
      case "0":
        msg = "Error al guardar, intente nuevamente.";
        break;
      case "1":
        msg = "Nueva cuenta guardada satisfacctoriamente";
        break;
      case "1062":
        msg = "Error, cuenta guardada previamente";
        break;
      default:
        msg = "Error al guardar en la base de datos.";
    }
    alertify.alert("Servitaxis Sitio 152", msg);
  }

  $("#cuentaForm").validate();

  $("#cuentaList").hide();

  // --------------------------------------------------------------------------
  const admin = $("#admin").val();
  $.ajax({
    type: "post",
    url: "php/ajax/listadoCuentas.php",
    data: { myVal: admin },
  })
    .done(function (r) {
      R = $.parseJSON(r);
      if (R[0] != 0) {
        $.each(R, function (indexInArray, valueOfElement) {
          const { idCuentaBancaria, banco, estado, fechaRegistro } =
            valueOfElement;
          const append = `<tr>                    
                    <td>${banco}</td>
                    <td>${idCuentaBancaria}</td>
                    <td>${fechaRegistro}</td>
                </tr>`;
          $("#cuentaList tbody").append(append);
          $("#msgList").hide();
          $("#cuentaList").show();
        });
        $("#cuentaList").DataTable({
          language: {
            url: "//cdn.datatables.net/plug-ins/9dcbecd42ad/i18n/Spanish.json",
          },
        });
        $("#cuentaList").tableExport({
          formats: ["csv", "txt"],
        });
      }
      else {
        $("#msgList").show();
        $("#cuentaList").hide();
      }
    })
    .fail(function () {
      alertify.alert("Warning!", "Error al cargar los datos");
    });
});
