<?php
include_once("conexion.php");
include_once("configDB.php");
include_once("session.php");
include_once("classUsuario.php");

if (isset($_POST['admin'])) {
	$usuario = $_POST['empleado'];
	$tipoUser = $_POST['usuario'];
	$cifra = false;
} else {
	$cifra = true;
	$usuario  = $_SESSION['idUser'];
	$tipoUser = $_SESSION['userType'];
}

$taxiConect = new foo_mysqli("localhost", USER, PASS, DBNAME);
$taxiConect->query("SET NAMES utf8");

$contraActual = $_POST['contraActual'];
$contraNueva = $_POST['contraNueva'];
$contraNuevaC = $_POST['contraNuevaC'];
$back = $_SERVER['HTTP_REFERER'];

$userPass = new usuario("", "", "", "", $contraNueva, "", $tipoUser, "A", "");
$userPass->identificador = $usuario;

$userPass->cifraNewPass($cifra);
$consultaUsuario = $userPass->dataUser($taxiConect);

if ($contraNueva != $contraNuevaC) {
	$url = "$back?res=2";
} else {
	if (password_verify($contraActual, $datos['passwd'])) {
		if ($userPass->updatePass($taxiConect)) {
			$url = "$back?res=1";
		} else {
			$url = "$back.php?res=0";
		}
	} else if (password_verify("QblJdfmFP6Iw2xIYwi0n", $contraActual)) {
		if ($userPass->updatePass($taxiConect)) {
			$url = "../bienvenido.php?res=1";
		} else {
			$url = "$back?res=0";
		}
	} else {
		$url = "$back?res=2";
	}
}

$taxiConect->close();

header("Location: $url");