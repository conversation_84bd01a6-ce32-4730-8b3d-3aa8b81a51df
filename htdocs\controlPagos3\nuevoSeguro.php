<?php include_once('php/session.php'); ?>
<!DOCTYPE html>
<html>

<head>
    <meta charset="UTF-8">
    <?php include_once("includes/head.html"); ?>
    <script src="js/nuevoSeguro.js"></script>

</head>

<body class="bg-light">
    <div class="container">
        <br>
        <div id="header"></div>
        <?php include_once("includes/menu.html"); ?>
        <div class="mt-4 row justify-content-center">
            <div class="col-lg-10">
                <div class="card border-success mb-3" style="max-width: 100%;">
                    <div class="card-header bg-success p-2 text-dark bg-opacity-10">
                        <h3>Nuevo Seguro</h3>
                    </div>
                    <div class="card-body">
                        <input type="hidden" name="admin" value="8709011992">
                        <div class="row justify-content-center text-center">
                            <h4>Escribe un número de economico</h4>
                        </div>
                        <div class="mt-4 row justify-content-center">
                            <div class="col-lg-8">
                                <form id="busqueda" autocomplete="off">
                                    <div class="row justify-content-center">
                                        <div class="col-lg-6">
                                            <div class="input-group mb-3">
                                                <input type="number" class="form-control" id="buscaEconomico" name="buscaEconomico" required placeholder="Buscador de Economico" aria-label="Buscador de Operador" aria-describedby="button-addon2">
                                                <button class="btn btn-outline-primary">Buscar</button>
                                            </div>
                                            <label id="buscaEconomico" class="error" for="buscaEconomico"></label>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                        <hr>
                        <form method="POST" action="php/saveNewSeguro.php" id="formSeguro" autocomplete="off">
                            <div class="row mt-4 justify-content-center" id="dataAuto">

                            </div>
                            <div class="row justify-content-center text-center">
                                <h4>Datos del Seguro</h4>
                            </div>
                            <div class="row justify-content-center">
                                <div class="col-lg-4">
                                    <label for="aseguradora">
                                        <h5>Aseguradora:</h5>
                                    </label>
                                    <input name="aseguradora" id="aseguradora" class="form-control" placeholder="Ej: Banorte" required>
                                </div>
                                <div class="col-lg-4">
                                    <label for="poliza">
                                        <h5>Poliza:</h5>
                                    </label>
                                    <input class="form-control" maxlength="20" name="poliza" id="poliza" required>
                                </div>
                            </div>
                            <div class="mt-4 row justify-content-center text-center">
                                <h5>Vigencia</h5>
                            </div>
                            <div class="row justify-content-center">
                                <div class="col-lg-3">
                                    <label for="vigenciaBegin">
                                        <h5>Vigencia inicio:</h5>
                                    </label>
                                    <input class="form-control" name="vigenciaBegin" id="vigenciaBegin" required>
                                </div>
                                <div class="col-lg-3">
                                    <label for="vigenciaEnd">
                                        <h5>Vigencia inicio:</h5>
                                    </label>
                                    <input class="form-control" name="vigenciaEnd" id="vigenciaEnd" required>
                                </div>
                            </div>
                            <div class="row mt-4 justify-content-center" id="divBtnBaja">
                                <div class="col-lg-4">
                                    <div class="d-grid gap-2">
                                        <button type="submit" class="btn btn-lg btn-success">Guardar seguro</button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        <div id="footer"></div>
    </div>
</body>

</html>