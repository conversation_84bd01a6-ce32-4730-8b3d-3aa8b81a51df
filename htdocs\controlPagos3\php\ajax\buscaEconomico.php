<?php
include_once("../conexion.php");
include_once("../configDB.php");
include_once("../classEconomico.php");
include_once("../classProcesaVariable.php");

if (isset($_POST['datoBusca'])) {

    $taxiConect = new foo_mysqli("localhost", USER, PASS, DBNAME);
    $taxiConect->query("SET NAMES utf8");

    $dato = new procesaVariable($_POST['datoBusca']);
    $x = $dato->procesoBusqueda();

    $buscaEco = new economico("", "", "", "", "", "", $x, 'A', "", "");

    $row = $buscaEco->dataEconomico($taxiConect);

    if (isset($_POST['seguro']) and count($row) > 1) {
        include_once("../classSeguro.php");
        $Seguro = new seguro($row['idEconomico'], '', '', '', '');
        $data = $Seguro->vigente($taxiConect);
        $row['suSeguro'] = $data;
    }

    $taxiConect->close();
} else {
    $row = array(0);
}

echo json_encode($row);
