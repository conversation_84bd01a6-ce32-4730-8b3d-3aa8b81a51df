<?php
use <PERSON>HPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\SMTP;
use PHPMailer\PHPMailer\Exception;

//Load Composer's autoloader
require '../vendor/autoload.php';

function microtime_float()
{
    list($useg, $seg) = explode(" ", microtime());
    return ((float)$useg + (float)$seg);
}

$tiempo_inicio = microtime_float();
$mail = new PHPMailer(true);

try {
    //Server settings
    $mail->SMTPDebug = 0;                      //Enable verbose debug output
    $mail->isSMTP();                                            //Send using SMTP
    $mail->Host       = 'smtp.exchangeadministrado.com';
    $mail->SMTPAuth   = true;
    $mail->Username   = '<EMAIL>';
    $mail->Password   = 'StarDust07';
    $mail->SMTPSecure = 'STARTTLS';
    $mail->Port       = 587;

    //Recipients
    $mail->setFrom('<EMAIL>', 'Casetas Sitio 152');
    $mail->addReplyTo('<EMAIL>', 'Sitio 152');
    $mail->addAddress('<EMAIL>');

    //Content
    $mail->isHTML(true);                                  //Set email format to HTML
    $mail->Subject = 'Recibo Sitio 152';
    $mail->Body    = 'Hola mundo desde PHPMailer salio sitio <b>in bold!</b> Hello Predcel';
    $mail->AltBody = 'Cuerpo en texto plano';

    $mail->send();
    echo 'Enviado';
} catch (Exception $e) {
    echo "Algo fallo Mailer Error: {$mail->ErrorInfo}";
}
$tiempo_fin = microtime_float();
$tiempo = $tiempo_fin - $tiempo_inicio;

echo "Tiempo empleado: " . ($tiempo_fin - $tiempo_inicio);
