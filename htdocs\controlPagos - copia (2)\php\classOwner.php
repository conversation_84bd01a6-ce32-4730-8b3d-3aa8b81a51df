<?php
class owner
{

    public $nombre;
    public $paterno;
    public $materno;
    public $telefono;
    public $correo;
    public $identificador;
    public $estado;
    public $tipoPropietario;

    function __construct($nom, $ap, $am, $mail, $tel, $tipo)
    {
        $this->nombre = $nom;
        $this->paterno = $ap;
        $this->materno = $am;
        $this->telefono = $tel;
        $this->correo = $mail;
        $this->tipoPropietario = $tipo;
    }

    public function saveOwner($conexion)
    {
        $q = "INSERT INTO owner(nombre, apellidoPaterno, apellidoMaterno, telefono, correo, tipo) 
        VALUES ('$this->nombre',
        '$this->paterno',
        '$this->materno',
        '$this->telefono',
        '$this->correo',
        '$this->tipoPropietario')";
        $eq = $conexion->query($q) or die($conexion->error);;
        if ($eq) {
            $this->identificador = $conexion->insert_id;
        } else {
            $this->identificador = false;
        }

        return $this->identificador;
    }

    public function update($conexion)
    {
        $q = "UPDATE owner SET nombre='$this->nombre',        
        apellidoPaterno='$this->paterno',
        apellidoMaterno='$this->materno',
        telefono='$this->telefono',
        correo='$this->correo',
        tipo='$this->tipoPropietario'
        WHERE idOwner='$this->identificador'";
        $eq = $conexion->query($q);

        return $eq;
    }

    public function dataOwner($conexion)
    {
        $q = "SELECT o.idOwner, o.nombre, o.apellidoPaterno, o.apellidoMaterno, 
		o.telefono, o.correo, o.tipo
        FROM owner o
        WHERE o.idOwner = '$this->identificador'";
        $eq = $conexion->query($q);
        if ($eq->num_rows != 0) {
            $row = $eq->fetch_array(MYSQLI_ASSOC);
        } else {
            $row = array(0);
        }
        return $row;
    }

    public function baja($conexion)
    {
        $q = "UPDATE owner SET estado = '$this->estado' WHERE idOwner = '$this->identificador'";

        return $conexion->query($q);
    }

    public function ownerList($conexion)
    {
        $q = "SELECT o.idOwner, CONCAT_WS(' ', o.nombre, o.apellidoPaterno, o.apellidoMaterno) AS nombreCompleto, 
		o.telefono, o.correo, o.estado, textTipoPropietario(o.tipo) AS Tipo
        FROM `owner` o";
        $eq = $conexion->query($q);

        return $eq;
    }
}
