<?php
include_once("conexion.php");
include_once("configDB.php");
include_once("classProcesaVariable.php");
include_once("classUsuario.php");

if (isset($_POST['usuario']) and isset($_POST['rePasswd'])) {

    $usuario = new procesaVariable($_POST['usuario']);
    $tipoUser = new procesaVariable($_POST['tipoUser']);
    $userName = new procesaVariable($_POST['userName']);
    $passwd = new procesaVariable($_POST['passwd']);
    $rePasswd = new procesaVariable($_POST['rePasswd']);

    $taxiConect = new foo_mysqli("localhost", USER, PASS, DBNAME);
    $taxiConect->query("SET NAMES utf8");

    $aux =  $taxiConect->iniciaTransaccion();

    $myUser = new usuario(
        "",
        "",
        "",
        $userName->valor,
        $passwd->valor,
        "",
        $tipoUser->valor,
        'A',
        ""
    );

    $myUser->identificador = $usuario->valor;
    if (!$myUser->validaUserType($taxiConect)) {
        if (!$myUser->saveLogin($taxiConect)) {
            $aux = 0;
            if ($myUser->estadoLogin($taxiConect) == 'I') {
                $type = 2;
            }
        }
    } else {
        $type = 1;
        $aux = 0;
    }

    $var = $taxiConect->finTransaccion($aux, '1', '0');

    if ($type == 1 and $var == 0) {
        $var = '2';
    } elseif ($type == 2) {
        $var = '3';
    }

    $taxiConect->close();
} else {
    $var = '0';
}

header("Location: ../newLogin.php?resp=$var");
