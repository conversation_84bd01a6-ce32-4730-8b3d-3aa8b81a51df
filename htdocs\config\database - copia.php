<?php
/**
 * Configuración de la base de datos para el sistema de minutas
 */

class Database {
    private $host = 'localhost';
    private $db_name = 'minutas_db';
    private $username = 'root';  // Cambiar según tu configuración de XAMPP
    private $password = '';      // Cambiar según tu configuración de XAMPP
    private $conn;

    /**
     * Obtener conexión a la base de datos
     */
    public function getConnection() {
        $this->conn = null;

        try {
            $this->conn = new PDO(
                "mysql:host=" . $this->host . ";dbname=" . $this->db_name . ";charset=utf8mb4",
                $this->username,
                $this->password,
                array(
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                    PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
                )
            );
        } catch(PDOException $exception) {
            echo "Error de conexión: " . $exception->getMessage();
        }

        return $this->conn;
    }

    /**
     * Generar ID único para minuta
     */
    public static function generateMinutaId() {
        $year = date('Y');
        $month = date('m');
        
        // Formato: MIN-YYYY-MM-XXX donde XXX es un número secuencial
        $prefix = "MIN-{$year}-{$month}-";
        
        // Buscar el último ID del mes actual
        $db = new Database();
        $conn = $db->getConnection();
        
        $query = "SELECT minuta_id FROM minutas WHERE minuta_id LIKE :prefix ORDER BY minuta_id DESC LIMIT 1";
        $stmt = $conn->prepare($query);
        $stmt->bindValue(':prefix', $prefix . '%');
        $stmt->execute();
        
        $result = $stmt->fetch();
        
        if ($result) {
            // Extraer el número secuencial y incrementar
            $lastId = $result['minuta_id'];
            $lastNumber = intval(substr($lastId, -3));
            $newNumber = $lastNumber + 1;
        } else {
            // Primer ID del mes
            $newNumber = 1;
        }
        
        return $prefix . str_pad($newNumber, 3, '0', STR_PAD_LEFT);
    }
}
?>
